import { constantRouterMap } from '@/router/routers'
import Layout from '@/layout/index'

const permission = {
  state: {
    routers: constantRouterMap,
    addRouters: []
  },
  mutations: {
    SET_ROUTERS: (state, routers) => {
      state.addRouters = routers
      state.routers = constantRouterMap.concat(routers)
    }
  },
  actions: {
    GenerateRoutes({ commit }, asyncRouter) {
      commit('SET_ROUTERS', asyncRouter)
    }
  }
}

export const filterAsyncRouter = (routers, loca) => { // 遍历后台传来的路由字符串，转换为组件对象
  return routers.filter(router => {
    if (router.component) {
      if (router.component === 'Layout') { // Layout组件特殊处理
        router.component = Layout
      } else {
       // const component = router.component
       loadView(router, loca)
      }
    }
    if (router.children && router.children.length) {
      router.children = filterAsyncRouter(router.children, loca)
    }
    return true
  })
}

export const loadView = (router, loca) => {
 setData( router, loca );
 //return view;
// return (resolve) => require([`@/views/${view}`], resolve)
}


function setData(router, data){
  data.map(item=>{
    if(item.matchingComponentName && item.matchingComponentName == router.component){
      router.component = item.component
    }
    if(item.children && item.children.length){
      setData( router, item.children );
    }

  })
}

export default permission
