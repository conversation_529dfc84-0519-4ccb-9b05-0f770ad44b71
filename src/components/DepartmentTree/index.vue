<template>
  <div class="department-tree-container">
    <el-scrollbar style="height: 100%">
      <!-- 搜索框 -->
      <el-input
        size="small"
        placeholder="输入关键字进行过滤"
        v-model="filterText"
        clearable
        class="tree-search-input"
      >
        <i slot="prefix" class="el-input__icon el-icon-search"></i>
      </el-input>
      
      <!-- 部门树 -->
      <el-tree
        ref="tree"
        :data="treeData"
        :props="treeProps"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        :highlight-current="highlightCurrent"
        :default-expand-all="defaultExpandAll"
        :default-expanded-keys="defaultExpandedKeys"
        @node-click="handleNodeClick"
        class="department-tree"
      >
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <span class="tree-node-label">
            <i :class="getNodeIcon(data)" class="tree-node-icon"></i>
            {{ node.label }}
          </span>
        </span>
      </el-tree>
    </el-scrollbar>
  </div>
</template>

<script>
export default {
  name: 'DepartmentTree',
  props: {
    // 树形数据
    treeData: {
      type: Array,
      default: () => []
    },
    // 树形配置
    treeProps: {
      type: Object,
      default: () => ({
        children: 'children',
        label: 'name'
      })
    },
    // 是否高亮当前选中节点
    highlightCurrent: {
      type: Boolean,
      default: true
    },
    // 是否默认展开所有节点
    defaultExpandAll: {
      type: Boolean,
      default: false
    },
    // 默认展开的节点key数组
    defaultExpandedKeys: {
      type: Array,
      default: () => []
    },
    // 是否显示搜索框
    showSearch: {
      type: Boolean,
      default: true
    },
    // 搜索框占位符
    searchPlaceholder: {
      type: String,
      default: '输入关键字进行过滤'
    }
  },
  data() {
    return {
      filterText: ''
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  methods: {
    /**
     * 节点过滤方法
     * @param {string} value 过滤值
     * @param {object} data 节点数据
     * @returns {boolean} 是否显示该节点
     */
    filterNode(value, data) {
      if (!value) return true
      const label = data[this.treeProps.label] || data.name || ''
      return label.indexOf(value) !== -1
    },
    
    /**
     * 节点点击事件处理
     * @param {object} data 节点数据
     * @param {object} node 节点对象
     * @param {object} component 组件实例
     */
    handleNodeClick(data, node, component) {
      this.$emit('node-click', data, node, component)
    },
    
    /**
     * 获取节点图标
     * @param {object} data 节点数据
     * @returns {string} 图标类名
     */
    getNodeIcon(data) {
      if (data.children && data.children.length > 0) {
        return 'el-icon-folder-opened'
      }
      return 'el-icon-folder'
    },
    
    /**
     * 设置当前选中节点
     * @param {string|number} key 节点key
     */
    setCurrentKey(key) {
      this.$refs.tree.setCurrentKey(key)
    },
    
    /**
     * 获取当前选中节点
     * @returns {object} 当前选中节点数据
     */
    getCurrentNode() {
      return this.$refs.tree.getCurrentNode()
    },
    
    /**
     * 获取当前选中节点的key
     * @returns {string|number} 当前选中节点的key
     */
    getCurrentKey() {
      return this.$refs.tree.getCurrentKey()
    },
    
    /**
     * 展开所有节点
     */
    expandAll() {
      this.expandNodes(this.treeData, true)
    },
    
    /**
     * 收起所有节点
     */
    collapseAll() {
      this.expandNodes(this.treeData, false)
    },
    
    /**
     * 递归展开/收起节点
     * @param {array} nodes 节点数组
     * @param {boolean} expand 是否展开
     */
    expandNodes(nodes, expand) {
      nodes.forEach(node => {
        this.$refs.tree.store.nodesMap[node.id].expanded = expand
        if (node.children && node.children.length > 0) {
          this.expandNodes(node.children, expand)
        }
      })
    },
    
    /**
     * 清空搜索
     */
    clearFilter() {
      this.filterText = ''
    }
  }
}
</script>

<style scoped>
.department-tree-container {
  height: 100%;
  padding: 10px;
  background: #fff;
  border-radius: 4px;
}

.department-tree {
  margin-top: 10px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.tree-node-label {
  display: flex;
  align-items: center;
}

.tree-node-icon {
  margin-right: 6px;
  color: #409eff;
  font-size: 16px;
}

/* 树节点样式优化 */
.department-tree >>> .el-tree-node__label {
  font-size: 13px;
  color: #606266;
}

.department-tree >>> .el-tree-node:hover > .el-tree-node__content {
  background-color: #f5f7fa;
}

.department-tree >>> .el-tree-node.is-current > .el-tree-node__content {
  background-color: #ecf5ff;
  color: #409eff;
}

.department-tree >>> .el-tree-node__content {
  height: 32px;
  line-height: 32px;
  border-radius: 4px;
  margin-bottom: 2px;
}

/* 滚动条样式 */
.el-scrollbar >>> .el-scrollbar__wrap {
  overflow-x: hidden;
}

.el-scrollbar >>> .el-scrollbar__bar.is-vertical {
  right: 2px;
  width: 6px;
}

.el-scrollbar >>> .el-scrollbar__thumb {
  background-color: rgba(144, 147, 153, 0.3);
  border-radius: 3px;
}

.el-scrollbar >>> .el-scrollbar__thumb:hover {
  background-color: rgba(144, 147, 153, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .department-tree-container {
    padding: 8px;
  }
  
  .tree-search-input {
    margin-bottom: 10px;
  }
  
  .custom-tree-node {
    font-size: 13px;
  }
  
  .tree-node-icon {
    font-size: 14px;
    margin-right: 4px;
  }
}
</style>
