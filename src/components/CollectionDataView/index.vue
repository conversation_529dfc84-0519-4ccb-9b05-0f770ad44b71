<template>
  <div>
    <el-dialog title="手动归集" :visible.sync="collectionDataVisible" :before-close="handleClose" width="60%"
               :close-on-click-modal="false">
      <el-row :gutter="10" style="margin-bottom:20px">
        <el-col :xs="24" :sm="11" :lg="11">
          <el-card class="box-card">
            <span>结算数据</span>
            <el-tree :data="resultData" :check-strictly="true" :props="resultProps" node-key="id" show-checkbox
                     @node-click="handleNodeClick" style="margin-top: 10px" ref="resultTree"></el-tree>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="2" :lg="2">
          <div style="margin-top: 20px">
            <el-button type="primary" icon="el-icon-arrow-right" circle style="display:block;margin:0 auto"
                       @click="apply" :highlight-current='true'></el-button>
          </div>
        </el-col>
        <el-col :xs="24" :sm="11" :lg="11">
          <el-card class="box-card">
            <span>概算数据</span>
            <el-tree :data="budgetData" :check-strictly="true" :auto-expand-parent="true" :props="budgetProps"
                     :render-content="renderContent" @check-change="orgCheckChange"
                     node-key="id" show-checkbox style="margin-top: 10px" ref="budgetTree">
            </el-tree>
          </el-card>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="commitData">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
  import {getBudgetTree} from "../../views/compare/budget/config/request";
  import {getResultDetailByResultId} from "../../views/compare/result/config/request";
  import {updateNodeTree} from "../../views/compare/compareDetail/config/request";

  export default {
    props: {
      projectId: {
        type: String,
        default: ''
      },
      resultId: {
        type: String,
        default: ''
      },
      collectionDataVisible: {
        type: Boolean,
        default: false
      },
      closeDialog: {
        type: Function,
        default: () => {
        }
      }
    },
    watch: {
      collectionDataVisible() {
        this.changeData = []
        this.initData()
      }
    },

    data() {
      return {
        budgetData: [],
        resultData: [],
        budgetProps: {
          label: 'nodeName',
          children: 'children'
        },
        resultProps: {
          label: 'resultNodeName',
          children: 'children'
        },
        //最终提交数据
        changeData: [],
      };

    },
    methods: {
      handleNodeClick(data) {
        //关闭所有
        this.expandFunc(this.budgetData)
        //打开对应的节点
        if (data.projectBudgetNodeId) {
          //设置单选
          this.orgCheckChange(data,true,null)
          //设置选中
          this.$refs.budgetTree.store.nodesMap[data.projectBudgetNodeId].checked = true
          if (this.$refs.budgetTree.store.nodesMap[data.projectBudgetNodeId].parent) {
            this.openNode(this.$refs.budgetTree.store.nodesMap[data.projectBudgetNodeId].parent)
          }
        }
      },
      openNode(node) {
        if (this.$refs.budgetTree.store.nodesMap[node.data.id]) {
          this.$refs.budgetTree.store.nodesMap[node.data.id].expanded = true;
          if (node.parent) {
            this.openNode(node.parent)
          }
        }
      },
      expandFunc(data) {
        data.forEach(item => {
          this.$refs.budgetTree.store.nodesMap[item.id].expanded = this.isExpand
          if (this.$refs.budgetTree.store.nodesMap[item.id].data.itemType === 2) {
            this.$refs.budgetTree.store.nodesMap[item.id].data.itemType = 3
          }
          if (item.children && item.children.length > 0) {
            this.expandFunc(item.children)
          }
        })
      },
      apply() {
        let budgetSelectedData = this.$refs.budgetTree.getCheckedNodes()
        let resultSelectedData = this.$refs.resultTree.getCheckedNodes()
        if (budgetSelectedData.length !== 1 || budgetSelectedData.length === 0) {
          this.$message.error("请正确选择概算节点")
          return;
        }
        if (budgetSelectedData.length === 0) {
          this.$message.error("请正确选择结算节点")
          return;
        }
        //拼接节点
        resultSelectedData.forEach(result => {
            //取消选中
            this.$refs.resultTree.store.nodesMap[result.id].checked = false;
            //设置颜色
            result.type = 1
            result.nodeName = result.resultNodeName
            this.$refs.budgetTree.append(result, budgetSelectedData[0]);
          }
        )
        //拼装数据，缓存
        resultSelectedData.forEach(result => {
          let data = {
            newBudgetId: budgetSelectedData[0].id,
            oldBudgetId: result.projectBudgetNodeId,
            projectId: result.projectId,
            resultId: result.id
          }
          this.changeData.push(data)
        })
        //展开当前节点
        this.$refs.budgetTree.store.nodesMap[budgetSelectedData[0].id].expanded = true;
      },
      initData() {
        getBudgetTree(this.projectId).then(res => {
          this.budgetData = res;
        })
        getResultDetailByResultId(this.projectId).then(res => {
          this.resultData = res;
        })
      },
      commitData() {
        updateNodeTree(this.changeData)
          .then(res => {
            this.closeDialog();
          })
      },
      handleClose() {
        this.closeDialog();
      },
      renderContent(h, {node, data, store}) {
        if (data.type === 1){//结算
          return  <span class="custom-tree-node-child"> {data.nodeName}</span>
        }else if (data.type === 2) {//概算
          return  <span class="custom-tree-node"> {data.nodeName}</span>
        }else{
          return  <span> {data.nodeName}</span>
        }
      },
      //实现单选
      orgCheckChange(data, checked, indeterminate) {
        if (checked) {
          let budgetSelectedData = this.$refs.budgetTree.getCheckedNodes()
          budgetSelectedData.forEach(budget => {
            if (data.id !== budget.id){
              this.$refs.budgetTree.store.nodesMap[budget.id].checked = false;
            }
          })
        }
      }
    },

  }
</script>
<style>
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
    color: green;
  }

  .custom-tree-node-child {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
    color: orangered;
  }
</style>
