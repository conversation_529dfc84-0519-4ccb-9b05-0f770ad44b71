<template>
<div>
    <div v-html="svgData" style="overflow-x: auto;" ref="flowChart"></div>
     <div class="operation">
        <i class="el-icon-minus" @click="scaleZoom(-0.2)"></i>
        <i class="el-icon-plus" @click="scaleZoom(0.2)"></i>
    </div>
</div>
</template>
<script>
export default {
    props: {
        svgData: {
            type: String,
            default: ''
        },
        imgPreview: {
            type: Boolean,
            default: false
        }
    },

    data() {
        return {
            scale: 1
        }
    },
    watch: {
       imgPreview(){
           this.scaleZoom();
       }
    },
    methods: {
        scaleZoom(radio){
                 radio = Number( radio )
                const newScale = !radio
                ?  1.0
                : this.scale + radio <= 0.2
                ?  0.2
                : this.scale + radio;
                //this.bpmnModeler.get('canvas').zoom(newScale);
                try{
                    this.$refs.flowChart.getElementsByTagName('svg')[0].style.transform = 'scale('+ newScale +')'
                }catch(e){}
                this.scale = newScale;
            }
    },


}
</script>

<style scoped>
    .operation{
        position: absolute;
        right: 40px;
        bottom: 80px;
    }
    .operation i{
        display: block;
        margin-top: 40px;
        cursor: pointer;
    }
</style>
