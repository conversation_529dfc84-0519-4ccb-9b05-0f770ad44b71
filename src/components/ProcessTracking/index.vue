<template>
  <div>
    <el-table :data="processTrackingList"
              height="500px"
             style="width: 100%;"
              border>
      <el-table-column
        label="流程"
        prop="processInstanceName">
      </el-table-column>

      <el-table-column
        label="执行人"
        prop="executor">
      </el-table-column>

      <el-table-column
        label="执行时间"
        prop="startTime">
      </el-table-column>

      <el-table-column
        label="接收人"
        prop="receiver">
      </el-table-column>

      <el-table-column
        label="办理时间"
        prop="processingTime">
      </el-table-column>

      <el-table-column
        label="状态"
        prop="state">
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  export default {
    name: "ProcessTracking",
    props: {
      processTrackingList:{
        type:Array,
        default: ()=>([])
      }
    },
    data(){
      return{
        auditRecords:{}
      }
    }
  }
</script>

<style scoped>

</style>
