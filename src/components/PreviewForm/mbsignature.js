import CryptoJS from 'crypto-js';
import axios from 'axios'
import { getToken } from '@/utils/auth'

function stringToMD5(str) {
    const hash = CryptoJS.SHA256(str);
    return CryptoJS.enc.Utf8.parse(hash.toString().substring(0, 32));
}

export function getQrCodeUrl(){
    return process.env.VUE_APP_BASE_SIGBATURE + 'mb-signature?ticket=' + CryptoJS.SHA256(getToken())
}

export function decryptData(data) {
    const decrypted = CryptoJS.AES.decrypt(data, stringToMD5(getToken()), {
        iv: CryptoJS.enc.Utf8.parse("a3f830f4d4c99b23"),
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    return decrypted.toString(CryptoJS.enc.Utf8);
}

export function getSignatureData(){
    const config = {
        headers: {
          'ticket': getToken()
        }
      }
      return axios.get(process.env.VUE_APP_BASE_SIGBATURE + 'getSignature', config);
}