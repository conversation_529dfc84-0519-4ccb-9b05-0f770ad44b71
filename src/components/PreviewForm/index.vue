<template>
  <div>
    <el-divider>签名信息</el-divider>
    <div v-if="!isHideBtn" style="text-align:center">
      <div v-if="markImgList.length>0">
          <el-carousel :interval="4000" height="72px" :autoplay="false" trigger="click" @change="changeUserImg">
            <el-carousel-item v-for="item in markImgList" :key="item" style="text-align: center"
                              :initial-index="initialIndex">
              <el-image style="height: 70px;" :src="item" alt="" :preview-src-list="[item]"></el-image>
            </el-carousel-item>
          </el-carousel>
      </div>
      <div class="container" v-else>
        <div style="text-align: center;margin-bottom: 10px;">
          <qrcode-vue :value="qrcodeValue" :size="90" level="H" className="box"/>
          <el-popover
            v-model="popoverVisible"
            placement="right-end"
            trigger="click">
            <el-image
              style="width: 400px; height: 200px"
              :src="this.signatureUrl"
              :fit="'fill'">
            </el-image>  
            
            <el-button style="display:block;text-align: right;margin-top:5px" type="success" @click="saveSignature" @>保存</el-button>
            <button slot="reference" @click="getSigData">获取签名</button>
          </el-popover>
        </div>

        <div class="text-container">
          <div class="qrcode-text">
            <div>说明：</div>
            <div>1.手机扫描二维码进行签名并在手机端点击【上传】按钮上传签名</div>
            <div>2.点击【获取签名】加载签名图片</div>
          </div>
        </div>
      </div>
    </div>
    <k-form-build
      :key="theKey"
      :value="jsonData"
      ref="KFB"
      @submit="handleSubmit"
      :default-value="defaultValue"
      :disabled="disabled"
      :config="config"
      @change="handleChange"
      :dynamicData="dynamicData"
    />
    <div style="width: 100% ;margin-bottom: 10px;" v-if="fileUploadVisible">
      <WorkFlowUploadFile
        :uploadList="fileUploadList"
        :disabled="disabled"
        :addFile="addFile"
        :deleteFile="deleteFile"
      />
    </div>
    <div style="width: 100% ;margin-bottom: 10px;" v-if="dialogVisible">
      <resultUpload
        :fileChage="resultUploadList"
        :disabled="disabled"
        :record="record"
        :projectAuditId="record.variables.projectAuditId"
        :orgTypeCd="record.variables.orgTypeCd"
        :addResultAuditFile="addResultAuditFile"
        ref="resultUpload"
        :projectId="record.variables.projectId"
      />
    </div>
    <div align="center">
      <span style="padding-right: 70px">
        <el-button v-show="!isHideBtn" style="width:200px;" @click="returnNode"
                   type="danger">收审修改
        </el-button>
      </span>

      <span>
        <el-popover
          placement="right"
          width="400"
          trigger="click">
          <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="textarea"></el-input>
          <el-select v-model="recipient" size="small" placeholder="选择退回节点" style="margin-top: 10px;" clearable>
             <el-option
               v-for="(item) in recipientDict"
               :key="item.value"
               :label="item.label"
               :value="item.value"
             />
          </el-select>
          <el-button style="margin-top: 10px;float: right" @click="rollbackTaskClick">确认</el-button>
            <el-button v-show="!isHideBtn" style="width:200px;" type="warning" slot="reference">退回任务</el-button>
          </el-popover>
      </span>
      <span style="padding-left: 70px">
        <el-button v-show="!isHideBtn" style="width:200px;" @click="getData" type="success">
          提交
        </el-button>
      </span>
    </div>


  </div>
</template>
<script>
import KFormDesign from '@/components/k-form-design/packages'
import '@/components/k-form-design/styles/form-design.less'
import Vue from 'vue'
import resultUpload from '@/components/AttachmentsUploadFile/resultUpload'
import request from '@/utils/request'
import {downloadFile} from '@/utils/index'
import WorkFlowUploadFile from '@/components/AttachmentsUploadFile/WorkFlowUploadFile'

Vue.use(KFormDesign)
import {getToken} from '@/utils/auth'
import {getUnitList} from '../../views/qualificationManagement/enterpriseQualification/config/request'
import {getUserList} from '../../views/workflow/workflow/config/request'
import {historicTaskInstanceDict} from '../../views/myTask/myAgendaTask/config/request'

import {getDictItem} from "../Dict/request";
import {parseTime} from '@/utils/index'
import {mapGetters} from "vuex";
import crudRequest from "../../views/myTask/myAgendaTask/config/request";
import {Message, Notification} from "element-ui";
import QrcodeVue from 'qrcode.vue'
import {getSignatureData, getQrCodeUrl, decryptData} from './mbsignature'
import {uploadSeal} from '@/views/user/user/config/request'
import {upload} from '@/utils/upload'
export default {
  name: 'Demo',
  props: {
    isRollback: {
      type: Boolean,
      default: false
    },
    jsonData: {
      type: Object,
      default: () => ({})
    },
    submit: {
      type: Function,
      default: () => {
      }
    },
    defaultValue: {
      type: Object,
      default: () => ({})
    },
    isHideBtn: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    record: {
      type: Object,
      default: () => ({})
    },
    addResultAuditFile: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters([
      'user',
    ])
  },
  data() {
    return {
      markImgList: [],
      popoverVisible: false,
      signatureUrl:'',
      qrcodeValue: getQrCodeUrl(),
      userListFormId: '',
      recipientDict: [],
      recipient: '',
      textarea: '',
      theKey: 0,
      refsData: {},
      dynamicData: {
        selectNode: [],
        outUnitName: [],
        userList: [],
        download: this.download,
      },
      config: {
        uploadFile: process.env.VUE_APP_BASE_API + 'general/file/upload',
        uploadFileHeaders: {
          'xToken': getToken()
        }
      },
      nextNode: 0,
      outUnitName: '',
      auditLessAmount: 0,
      assignee: '',
      dialogVisible: false,
      fileList: [],
      sheetList: [],
      unitList: [],
      initialIndex: 0,
      imgIndex: 0,
      fileUploadVisible: false,
      fileUploadList: [],

    }
  },
  watch: {
    jsonData: {
      immediate: false,
      handler() {
        //重新渲染KBF的组件
        this.theKey++
        //清空数据
        this.clearData();
        this.$nextTick(() => {
          //初始化数据
          this.initData()
        })
      }
    }
  },
  created() {
    getDictItem('node_type').then(data => {
      for (let i = 0; i < data.length; i++) {
        let item = {
          label: data[i].name,
          value: data[i].code,
        }
        this.dynamicData.selectNode.push(item)
      }
    })

  },
  components: {
    resultUpload,
    WorkFlowUploadFile,
    QrcodeVue
  },
  mounted() {
    this.markImgList = this.user.imgList.map(img => img.replace('/file-img/', '/images/'));
    this.getHistoricTaskDict();
    this.initData()
  },
  methods: {
    saveSignature(){
      // 将 base64 数据转换为 Blob 对象
      const blob = this.dataURItoBlob(this.signatureUrl);
      // 创建一个 File 对象
      const file = new File([blob], this.user.nickname+'.jpg', { type: 'image/jpeg' });
      upload('general/file/img/upload', file)
        .then(res => {
          if (res.data.status === 200) {
            uploadSeal({id:this.user.id, imgList:[res.data.data]}).then(res2=>{
              this.markImgList.unshift(res.data.data.url.replace('/file-img/', '/images/'))
              this.$message.success('保存成功');
            })
          } else {
            this.$message.error(res.data.message);
          }
        })
        .catch(e => {
          this.$message.error('上传失败！请稍后重试！');
        })

    },
    getSigData(){
      getSignatureData().then(res=>{
        var data = res.data.data;
        if(data){
          this.signatureUrl = decryptData(data);
          this.popoverVisible = true
        }else{
          this.popoverVisible = false
          this.signatureUrl = ''
          this.$message.warning('请先提交签名后获取');
        }
      })
    },
    // 使用 dataURItoBlob 函数将 base64 数据转换为 Blob 对象的实现
    dataURItoBlob(dataURI) {
      // 将 base64 数据去掉开头的 "data:image/jpeg;base64,"
      const byteString = atob(dataURI.split(',')[1]);
      // 创建一个 ArrayBuffer 对象
      const ab = new ArrayBuffer(byteString.length);
      // 创建一个 Uint8Array 类型的数组，表示二进制数据
      const ia = new Uint8Array(ab);

      // 将 base64 数据转换为二进制数据
      for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
      }
      // 返回一个 Blob 对象
      return new Blob([ab], { type: 'image/jpeg' });
    },
    getHistoricTaskDict() {
      if (!this.isHideBtn) {
        let taskId = this.record.id;
        historicTaskInstanceDict(taskId).then(res => {
          this.recipientDict = res
        })
      }
    },
    //回退任务
    rollbackTaskClick() {
      if (this.recipient != '' && this.recipient != null) {
        this.$confirm('你确定退回该任务吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$emit('func', {returnData: this.textarea, historyTaskId: this.recipient});
          })
      } else {
        Message({
          message: '请选择回退节点',
          type: 'warning'
        })
      }
    },
    handleSubmit(p) {
      // 通过表单提交按钮触发，获取promise对象
      p().then(res => {
        // 获取数据成功
      }).catch(err => {
        console.log(err, "校验失败");
      });
    },
    clearData() {
      this.outUnitName = '';
      this.assignee = '';
      this.auditLessAmount = 0;
      this.dynamicData.outUnitName = [];
      this.dynamicData.assignee = [];
      this.dynamicData.userList = []
    },
    download() {
      this.$refs.KFB.getData()
        .then(res => {
          for (let x in res) {
            if (x.indexOf("upload") !== -1) {
              request({
                url: '/general/file/download?uri=' + res[x][0].url,
                type: 'get',
                responseType: 'blob'
              })
                .then(result => {
                  downloadFile(result, res[x][0].name)
                })
            }
          }
        })
    },
    getData() {
      let data = this.commitResultData();
      // 通过函数获取数据
      this.$refs.KFB.getData()
        .then(res => {
          //设置审核数据
          if (this.KFBSetData(res)) {
            // 提交审核
            if (res['unit' + this.record.variables.subType] && this.record.name === '造价中心审核资料+外审') {
              if (this.record.variables.actionType === 0) {
                res.nextNode = 3
                res.actionType = 1
              }
            }
            if (!this.isHideBtn) {
              if (this.markImgList.length > 0) {
                res.userImg = this.markImgList[this.imgIndex].replace('/images/', '/file-img/')
              }
            } else {
              res.userImg = ''
            }
            this.submit(res, data);
          }
        })
        .catch(err => {
          console.log(err, "校验失败");
        });
    },
    KFBSetData(res) {
      res.nextNode = this.nextNode;
      if (this.auditLessAmount !== 0) {
        res.auditLessAmount = this.auditLessAmount;
      }
      //设置收审人列表
      res.userList = this.dynamicData.userList;
      //当前公司类型（初审，1审，2审//1造价中心，2二级单位，3项目公司）
      res.orgTypeCd = this.user.orgTypeCd;
      //根据for循环获取数据
      for (let x in this.$refs.KFB.form.formItems) {
        let value = this.$refs.KFB.form.formItems[x].getField().value
        if (this.$refs.KFB.form.formItems[x].label.trim() === '外委单位名称') {
          if (value) {
            //nextNode初始为0，防止覆盖其他赋值(收审修改=1)
            if (this.nextNode == 0) {
              this.nextNode = 2
            }
            if (value.split('|').length > 1) {
              let splitValue = value.split('|')[1];
              res.outUnitId = splitValue;
              this.unitList.forEach(val => {
                if (splitValue === val.id) {
                  res.outUnitName = val.name;
                  res['unit' + this.record.variables.subType] = val
                }
              })
            } else {
              this.unitList.forEach(val => {
                if (value === val.name) {
                  res.outUnitName = val.name;
                  res['unit' + this.record.variables.subType] = val
                }
              })
            }
          }
        }

        if (this.$refs.KFB.form.formItems[x].label.trim() === '收审人') {
          if (value) {
            res.assignee = value;
          } else {
            res.assignee = "";
          }
        }
        if (this.$refs.KFB.form.formItems[x].label.trim() === '其中：工程费(万元)') {
          if (value) {
            res.resultProjectAmount = value;
          } else {
            res.resultProjectAmount = 0;
          }
        }
        if (this.$refs.KFB.form.formItems[x].label.trim() === '甲供设备材料费(万元)') {
          if (value) {
            res.resultEmFirstAmount = value;
          } else {
            res.resultEmFirstAmount = 0;
          }
        }
        if (this.$refs.KFB.form.formItems[x].label.trim() === '其他费(万元)') {
          if (value) {
            res.resultOtherAmount = value;
          } else {
            res.resultOtherAmount = 0;
          }
        }
        if (this.$refs.KFB.form.formItems[x].label.trim() === '抵扣增值税(万元)') {
          if (value) {
            res.resultAddValueAmount = value;
          } else {
            res.resultAddValueAmount = 0;
          }
        }
        if (this.$refs.KFB.form.formItems[x].label.trim() === '抵扣增值税(万元)') {
          if (value) {
            res.resultAddValueAmount = value;
          } else {
            res.resultAddValueAmount = 0;
          }
        }
        if (this.$refs.KFB.form.formItems[x].label.trim() === '审核意见') {
          if (value) {
            res.auditOpinion = value;
          } else {
            res.auditOpinion = "";
          }
        }
        if (this.$refs.KFB.form.formItems[x].label.trim() === '审减额(万元)') {
          if (value) {
            res.lessAmount = value;
          } else {
            res.lessAmount = 0;
          }
        }
        if (this.$refs.KFB.form.formItems[x].label.trim() === '审减率（%）') {
          if (value) {
            res.lessRate = value;
          } else {
            res.lessRate = 0;
          }
        }
        if (this.$refs.KFB.form.formItems[x].label.trim() === '快递单号') {
          if (value) {
            res.trackingNumber = value;
          } else {
            res.trackingNumber = '';
          }
        }
        if (this.$refs.KFB.form.formItems[x].label.trim() === '备注') {
          if (value) {
            res.trackingRemark = value;
          } else {
            res.trackingRemark = '';
          }
        }
        if (this.$refs.KFB.form.formItems[x].label.trim() === '外委开始时间') {
          if (value) {
            res.startTimeOutsourcing = value;
          }
        }
        if (this.$refs.KFB.form.formItems[x].label.trim() === '选自审或外委') {
          if (value === '2' && !this.$refs.KFB.form.formItems['inside_name'].getField().value) {
            Notification.warning({
              title: "请选择外委单位!"
            })
            return false;
          }
        }
      }
      res.nextNode = this.nextNode;
      res.isRollback = false;
      res.uploadFileList = this.fileUploadList
      return true;
    },
    initData() {
      let node = {}
      for (let x in this.$refs.KFB.form.formItems) {
        if (this.$refs.KFB.form.formItems[x].$options.propsData.label.trim() === '收审人') {
          this.userListFormId = x
        }
        if (this.$refs.KFB.form.formItems[x].$options.propsData.label.trim() === '审核人') {
          this.$set(node, [x], this.user.nickname)
        }
        if (this.$refs.KFB.form.formItems[x].$options.propsData.label.trim() === '审核时间') {
          this.$set(node, [x], parseTime(new Date()))
        }
        if (this.$refs.KFB.form.formItems[x].$options.propsData.label.trim() === '外委开始时间') {
          this.$set(node, [x], this.record.variables.startTimeOutsourcing ? this.record.variables.startTimeOutsourcing : parseTime(new Date()))
        }
        if (this.$refs.KFB.form.formItems[x].$options.propsData.label.trim() === '外委完成时间') {
          this.$set(node, [x], parseTime(new Date()))
        }
        if (this.$refs.KFB.form.formItems[x].$options.propsData.label.trim() === '审定金额(万元)') {
          this.$set(node, [x], this.record.variables.auditLessAmount)
        }
        if (this.$refs.KFB.form.formItems[x].$options.propsData.label.trim() === '外委单位名称') {
          this.$set(node, [x], this.record.variables['unit' + this.record.variables.subType] ? this.record.variables['unit' + this.record.variables.subType].name : '')
          if (this.record.name === '造价中心审核资料+外审') {
            this.$refs.KFB.disable([x]);
          }
        }
        if (this.$refs.KFB.form.formItems[x].$options.propsData.label.trim() === '外委单位地址') {
          this.$set(node, x, this.record.variables['unit' + this.record.variables.subType] ? this.record.variables['unit' + this.record.variables.subType].address : '')
        }
        if (this.$refs.KFB.form.formItems[x].$options.propsData.label.trim() === '联系人') {
          this.$set(node, x, this.record.variables['unit' + this.record.variables.subType] ? this.record.variables['unit' + this.record.variables.subType].contactName : '')
        }
        if (this.$refs.KFB.form.formItems[x].$options.propsData.label.trim() === '手机号') {
          this.$set(node, x, this.record.variables['unit' + this.record.variables.subType] ? this.record.variables['unit' + this.record.variables.subType].contactTel : '')
        }
        if (this.$refs.KFB.form.formItems[x].$options.propsData.label.trim() === '快递单号') {
          this.$set(node, x, this.record.variables['unit' + this.record.variables.subType] ? this.record.variables.trackingNumber : '')
        }
        if (this.$refs.KFB.form.formItems[x].$options.propsData.label.trim() === '退回意见') {
          if (this.record.variables.isRollback) {
            this.$set(node, [x], this.record.variables.returnData ? this.record.variables.returnData : '')
          } else {
            this.$refs.KFB.hide([x])
          }
        }
        if (this.$refs.KFB.form.formItems[x].$options.propsData.label.trim() === '备注') {
          this.$set(node, [x], this.record.variables.trackingRemark ? this.record.variables.trackingRemark : '')
        }
      }
      this.$refs.KFB.form.setFieldsValue(node)
      if (this.record.userList) {
        this.dynamicData.userList = JSON.parse(this.record.userList);
        //设置默认收审人
        if (this.dynamicData.userList.length === 1) {
          this.$refs.KFB.setData({[this.userListFormId]: this.dynamicData.userList[0].value});
        }
      } else {
        if (!this.disabled) {
          getUserList(this.record.id).then(data => {
            if (data && data.length > 0) {
              for (let i = 0; i < data.length; i++) {
                let item = {
                  label: data[i].nickName,
                  value: data[i].id,
                }
                this.dynamicData.userList.push(item)
              }
              //设置默认收审人
              if (this.dynamicData.userList.length === 1) {
                this.$refs.KFB.setData({[this.userListFormId]: this.dynamicData.userList[0].value});
              }
            }
          })
        }
      }
      if (JSON.stringify(this.jsonData).indexOf("上传结算数据") !== -1) {
        this.dialogVisible = true
      }
      if (JSON.stringify(this.jsonData).indexOf("附件信息") !== -1) {
        this.fileUploadVisible = true
      }
      getUnitList().then(data => {
        this.unitList = data
        for (let i = 0; i < data.length; i++) {
          let item = {
            label: data[i].name,
            value: data[i].name + '|' + data[i].id,
          }
          this.dynamicData.outUnitName.push(item)
        }
      })
      if (this.record.variables.userImg) {
        this.initialIndex = this.user.imgList.indexOf(this.record.variables.userImg)
      }
      //附件
      if (this.record.variables) {
        //遍历属性  如果属性值为数组  则为附件
        for (let key in this.record.variables) {
          //key里包含uploadFile字段  且属性值为数组
          if (key.indexOf('uploadFile') >= 0 && this.record.variables[key] instanceof Array &&this.record.variables[key].length > 0) {
            this.fileUploadList = this.record.variables[key]
          }
        }
      }
    },
    returnNode() {
      //返回初始人
      if (this.record.variables.startUserId) {
        this.assignee = this.record.variables.startUserId;
      }
      this.nextNode = 1;
      this.getData();
    },
    handleChange(value, key) {
      if ('inside_or_outside' === key) {
        if ('2' === value) {
          this.$refs.KFB.enable(['inside_name']);
        } else if ('1' === value) {
          this.$refs.KFB.disable(['inside_name']);
        }
      }
      if (this.$refs.KFB.form.formItems[key].$options.propsData.label.trim() === '审定金额(万元)') {
        if (value === 0 || value == null) return
        this.auditLessAmount = value;
        let node = {}
        for (let x in this.$refs.KFB.form.formItems) {
          if (this.$refs.KFB.form.formItems[x].$options.propsData.label.trim() === '审减额(万元)') {
            let lessAmount = Number.parseFloat(value) - Number.parseFloat(this.record.variables.auditLessAmount)
            this.$set(node, [x], lessAmount.toFixed(2))
          }
          if (this.$refs.KFB.form.formItems[x].$options.propsData.label.trim() === '审减率（%）') {
            let k = ([x], Number.parseFloat(value) - Number.parseFloat(this.record.variables.auditLessAmount)) / Number.parseFloat(this.record.variables.auditLessAmount) * 100;
            this.$set(node, [x], k.toFixed(4))
          }
        }
        this.$refs.KFB.form.setFieldsValue({
          ...node
        })
      }

    },
    resultUploadList(list, sheetList) {
      this.fileList = list;
      this.sheetList = sheetList;
    },
    commitResultData() {
      let data = {
        file: this.fileList[0],
        sheetList: this.sheetList,
        dataType: 1,
        projectId: this.record.variables.projectId,
        projectAuditId: this.record.variables.projectAuditId,
      }
      return data;
    },
    changeUserImg(index) {
      this.imgIndex = index
    },
    //上传附件
    addFile(current) {
      this.fileUploadList.push(current)
    },
    deleteFile(index) {
      this.fileUploadList.splice(index, 1)
    },
  },
}
</script>
<style>
/* 设置右侧文本的容器样式 */
.text-container {
  position: absolute; /* 绝对定位 */
  right: 2%;
}
.box {
  position: relative; /* 相对定位 */
}
.container{
  display: flex; /* 使用 Flexbox 布局 */
  justify-content: center; /* 水平居中对齐 */
  position: relative; /* 相对定位 */
}
.qrcode-text{
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  white-space: nowrap; /* 防止文本换行 */
  margin-top: 4%;
}
.ant-message {
    z-index: 2003 !important;
  }
</style>
