<template>
  <el-dialog title="文件预览" class="printDialog" :visible.sync="openReview" append-to-body fullscreen
             close-on-press-escape :before-close="closeReview" center style="overflow: hidden"
  >
    <iframe v-if="fileType==='pdf'" :src="pdfUri+'#toolbar=0'" :style="{height:height,width:'100%'}" ></iframe>
    <div v-else :style="{height:height,width:'100%',overflowY:'auto'}">
      <div v-if="fileType==='word'" ref="docFile"/>
      <div v-if="fileType==='excel'" >
        <el-tabs v-model="activeName" type="border-card">
          <el-tab-pane v-for="(item,index) in excelSheet" :key="index" :label="item.name" :name="item.index">
            <div class="table" v-html="item.innerHTML"></div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

  </el-dialog>
</template>

<script>
import request from '@/utils/request'

const docx = require('docx-preview');
window.JSZip = require('jszip')
const XLSX = require('xlsx');

export default {
  props: {
    closeReview: {
      type: Function,
      default: () => {
      },
      required: true
    },
    reviewFile: {
      type: Object,
      required: true,
      default: () => {
      },
    },
    openReview: {
      type: Boolean,
      default: () => false
    },
  },
  watch: {
    reviewFile: {
      immediate: true,
      handler() {
        this.initView()
      }
    }
  },
  data() {
    return {
      pdfUri: '',
      fileType: '',
      activeName: '',
      excelSheet: [],
      height: '0'
    }
  },

  created() {
    this.height = (window.innerHeight - 130) + 'px'
  },
  methods: {
    initView() {
      if (!this.reviewFile.name) {
        return
      }
      const arrFile = this.reviewFile.name.split(".");
      const fileType = arrFile[arrFile.length - 1].toLowerCase();
      const allowFileType = ['pdf', 'docx', 'xlsx', 'xls']
      if (allowFileType.indexOf(fileType) < 0) {
        this.$message.error("此类型文件无法预览!")
        this.closeReview()
        return false
      }
      if (fileType === 'pdf') {
        this.fileType = fileType;
        this.pdfUri = process.env.VUE_APP_BASE_FILE + this.reviewFile.uri;
      }
      if (fileType === 'docx') {
        this.fileType = 'word';
        request({
          url: '/general/file/download?uri=' + this.reviewFile.uri,
          type: 'get',
          responseType: 'blob'
        }).then(res => {
          docx.renderAsync(res, this.$refs.docFile)
        })
      }
      if (fileType === 'xlsx' || fileType === 'xls') {
        this.excelSheet = []
        this.fileType = 'excel';
        request({
          url: '/general/file/download?uri=' + this.reviewFile.uri,
          type: 'get',
          responseType: 'arraybuffer'
        }).then(res => {
          let workbook = XLSX.read(new Uint8Array(res), {type: "array"});
          this.activeName = '0'
          workbook.SheetNames.forEach(sheet => {
            const worksheet = workbook.Sheets[sheet];
            if (worksheet) {
              let innerHTML = ''
              try {
                innerHTML = XLSX.utils.sheet_to_html(worksheet);
              } catch (e) {
              }
              this.excelSheet.push({
                name: sheet,
                innerHTML: innerHTML
              });
            }
          })
        })
      }
    },

  }
}
</script>

<style scoped>
>>> table {
  padding: 20px 30px 0 30px;
  width: 100% !important;
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  text-align: center !important;
}

>>> table tr td {
  border: 1px solid gray !important;

}

/**整体样式 */
>>> .excel-view-container {
  background-color: #FFFFFF;
}

.download-button {
  position: fixed;
  background: #417692;
  top: 50%;
  right: 5px;
  color: white;
  transform: translate(-50%, -50%);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: center;
}

</style>
