<template>
  <el-popover
    placement="right"
    width="200"
    trigger="click">

    <el-cascader
      v-model="form"
      :options="list"
      @change="cascaderChange"
      :props="{
        multiple: false,
        value: 'id',
        label: 'name',
        children: 'children',
        disabled: 'ifOrg',
        checkStrictly: true,
        emitPath: false
    }"
      :show-all-levels="false"
      clearable></el-cascader>
    <el-button slot="reference" size="mini" type="text">{{ btnText }}</el-button>
  </el-popover>
</template>
<script>
  import {getOrgUserList} from '@/api/workflow/workflowUser'
  import {assignTasks} from '@/views/taskManagement/taskPool/config/request'

  export default {
    props: {
      record: {
        type: Object,
        default: () => ({})
      },
      btnText: {
        type: String,
        default: '指定执行人'
      }
    },
    data() {
      return {
        list: [],
        form: ''
      }
    },
    methods: {
      cascaderChange(val) {
        if (!val) return;
        assignTasks({
          userId: val,
          taskId: this.record.id
        })
          .then(res => {
            this.$emit('cascaderChange');
            this.$notify.success({
              title: '提示',
              message: '操作成功',
              type: 'success'
            });
          })
      }
    },
    mounted() {
      // let loca = sessionStorage.getItem('workflowUserList');
      // if(loca){
      //     this.list = JSON.parse( loca );
      // }else {
      getOrgUserList()
        .then(res => {
          this.buildData(res, 'children')
          this.list = res;
          sessionStorage.setItem('workflowUserList', JSON.stringify(res))
        })
    }
    // },
  }
</script>
