import Vue from 'vue'
import { getDictItem } from './request'

export default class Dict {
  constructor(dict) {
    this.dict = dict
  }

  async init(names, completeCallback) {
    if (names === undefined || name === null) {
      throw new Error('need Dict names')
    }
    const ps = []
    names.forEach(n => {
      Vue.set(this.dict.dict, n, {})
      Vue.set(this.dict.label, n, {})
      Vue.set(this.dict, n, [])
      ps.push(getDictItem(n).then(data => {
        this.dict[n].splice(0, 0, ...data)
        data.forEach(d => {
          Vue.set(this.dict.dict[n], d.code, d)
          Vue.set(this.dict.label[n], d.code, d.name)
        })
      }))
    })
    await Promise.all(ps)
    completeCallback()
  }
}
