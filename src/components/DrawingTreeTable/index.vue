<template>
  <div>
    <el-table
      :data="compareDetailTable"
      style="width: 100%;"
      height="600"
      row-key="id"
      border
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
      <template v-for="(item, key) in tableDialogFields">
        <el-table-column :label="item.label" :prop="item.fieldName" :width="item.size" :key="key"
                         :show-overflow-tooltip="true" :fixed="item.fixed">
          <template v-for="(item, key) in item.children">
            <el-table-column :label="item.label" :prop="item.fieldName" align="center" :key="key" :width="item.size"
                             :show-overflow-tooltip="true">
              <template v-for="(item, key) in item.children">
                <el-table-column :label="item.label" :prop="item.fieldName" align="center" :key="key" :width="item.size"
                                 :show-overflow-tooltip="true">
                  <template v-slot="scope">
                    {{ scope.row[ item.fieldName ] }}
                  </template>
                </el-table-column>
              </template>
            </el-table-column>
          </template>
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>
<script>
  import Sortable from 'sortablejs';
  import config from '@/views/compare/compareDetail/config/index'
  const tableDialogFields = config.getFields('tableDialog');
  export default {
    props: {
      compareDetailTable: {
        type: Array,
        default: () => {
        }
      },
    },
    mounted () {
      this.rowDrop();
    },
    data(){
      return {
        tableDialogFields: tableDialogFields
      }
    },
    methods: {
      // 行拖拽
      rowDrop () {
        // 此时找到的元素是要拖拽元素的父容器
        const tbody = document.querySelector('.el-table__fixed-body-wrapper tbody');
        const _this = this;
        Sortable.create(tbody, {
          //  指定父元素下可被拖拽的子元素
          onEnd ({ newIndex, oldIndex }) {
            const currRow = _this.tableData.splice(oldIndex, 1)[0];
            _this.tableData.splice(newIndex, 0, currRow);
          }
        });
      },
    }
  }
</script>
