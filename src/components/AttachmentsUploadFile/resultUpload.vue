<template>
  <div>
    <el-upload
      width="100%"
      style="width: 100%"
      height="200"
      ref="uploadResult"
      drag
      :disabled="disabled"
      action=""
      accept=".xls,.xlsx"
      :on-remove="deleteFile"
      :limit=1
      :http-request="httpRequest"
      :file-list="uploadList"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
    </el-upload>
    <el-divider>文件列表</el-divider>
    <el-table
      :data="sheetList"
      border
      style="width: 100%">
      <el-table-column
        label="文件名称"
        prop="name"
      >
      </el-table-column>
      <el-table-column
        label="文件类型"
      >
        <template slot-scope="scope">
          <el-select v-model="scope.row.sheetType" placeholder="请选择" @change="changeFileType(scope.row,scope.$index)"
                     :disabled="disabled">
            <el-option
              v-for="item in dict.result_file_type"
              :key="item.code"
              :label="item.name"
              :value="item.code">
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column
        label="手动归集"
      >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.autoType"
            @change='changeRowNode(scope)'
            :disabled="scope.row.disabled">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column
        prop="budgetNames"
        label="归集节点"
      >
      </el-table-column>
      <el-table-column v-if="!disabled"
                       label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="deleteTableFile(scope.$index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--树形详情弹窗-->
    <el-dialog title="请选择归集目标:" :modal="false" :close-on-click-modal="false"
               :visible.sync="treeNodeCheck.treeVisible"
               @closed="closedDialog" append-to-body>
      <el-tree
        :data="treeNodeCheck.treeBudgetDataList"
        show-checkbox
        size="small"
        node-key="id"
        :expand-on-click-node="false"
        :check-strictly="true"
        :default-expanded-keys="openTree"
        :props="treeNodeCheck.defaultProps"
        ref="treeNodeBudget">
      </el-tree>
    </el-dialog>
  </div>
</template>
<script>
import {upload} from '@/utils/upload'
import {getBudgetTree} from "../../views/compare/budget/config/request";

export default {
  props: {
    editData: {
      type: Object,
      default: () => {
      }
    },
    complete: {
      type: Function,
      default: () => {
      }
    },
    projectAuditId: {
      type: String,
      default: () => {
      }
    },
    orgTypeCd: {
      type: Number,
      default: () => {
      }
    },
    disabled: {
      type: Boolean,
      default: () => {
      }
    },
    isHiddenColumn: {
      type: Boolean,
      default: false
    },
    success: {
      type: Function,
      default: () => {
      }
    },
    fileChage: {
      type: Function,
      default: () => {
      }
    },
    projectId: {
      type: String,
      default: () => {
      }
    },
    addResultAuditFile: {
      type: Boolean,
      default: false
    },
    record: {
      type: Object,
      default: () => {
      }
    },
  },
  watch: {
    record() {
      this.getData();
    },
    editData: {
      handler: function (resultFileData) {
        let fileList = []
        if (resultFileData.sheetList != null) {
          resultFileData.sheetList.forEach(item => {
            item.sheetType = item.sheetType.toString();
          });
          this.sheetList = resultFileData.sheetList
        }
        fileList.push(resultFileData.file)
        this.uploadList = fileList
        this.fileChage(this.uploadList, this.sheetList);
      }
    }
  },
  data() {
    return {
      uploadList: [],
      sheetList: [],
      openTree: [],
      treeNodeCheck: {
        rowIndex: 0,
        treeBudgetDataList: [],
        treeVisible: false,
        treeNodeCheckedId: "",
        defaultProps: {
          label: 'nodeName',
          children: 'children'
        }
      },
    }
  },
  dicts: ['result_file_type'],
  mounted() {
    this.getData();
  },
  methods: {
    //删除文件
    deleteTableFile(index) {
      this.sheetList.splice(index, 1);
      this.fileChage(this.uploadList, this.sheetList)
    },
    deleteFile() {
      this.$refs.uploadResult.clearFiles();
      this.uploadList = [];
      this.sheetList = [];
      this.fileChage([], []);
    },
    clearFiles() {
      this.$refs.uploadResult.clearFiles();
      this.uploadList = [];
      this.sheetList = [];
    },
    httpRequest(data) {
      const loading = this.$loading({
        lock: true,
        text: '正在上传，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      upload('compare/result/upload', data.file)
        .then(res => {
          if (res.data.status === 200) {
            let result = {
              name: data.file.name,
              url: res.data.data.uri,
            }
            this.uploadList.push(result);
            this.updateTable(res.data.data.sheetNames);
            this.fileChage(this.uploadList, this.sheetList);
          } else {
            this.$message.error(res.data.message);
          }
          loading.close();
        })
        .catch(e => {
          loading.close();
          this.$message.error('上传失败！请稍后重试！');
        })
    },
    getData() {
      //获取结算数据//是否为新加标识
      if (!this.addResultAuditFile && this.record) {
        this.uploadList.push(this.record.formData.file);
        this.record.formData.sheetList.forEach(sheet => {
          sheet.disabled = true;
          sheet.sheetType = sheet.sheetType.toString();
        })
        this.sheetList = this.record.formData.sheetList;
      }
      //获取详情
      if (this.projectId) {
        this.getBudgetData(this.projectId)
      }
    },
    getBudgetData(id) {
      getBudgetTree(id).then(res => {
        this.treeNodeCheck.treeBudgetDataList = res;
        if (res && res.length > 0) {
          this.openTree.push(res[0].id);
        }
      })
    },
    changeRowNode(scope) {
      this.treeNodeCheck.rowIndex = scope.$index;
      if (scope.row.autoType) {
        this.treeNodeCheck.treeVisible = true;
        this.$nextTick(function () {
          this.$refs.treeNodeBudget.setCheckedKeys(this.getCheckedKeys(scope.$index))
        })
      } else {
        this.treeNodeCheck.treeVisible = false;
        this.sheetList[scope.$index].checkedId = [];
        this.sheetList[scope.$index].treeNodeCheckedName = '';
      }
      this.fileChage(this.uploadList, this.sheetList)
    },
    getCheckedKeys(index) {
      if (!this.sheetList[index].checkedId || this.sheetList[index].checkedId == undefined) {
        return []
      }
      return this.sheetList[index].checkedId;
    },
    //更新底部数据
    updateTable(sheetNames) {
      sheetNames.forEach(name => {
        let sheet = {
          name: name,
          autoType: false,
          budgetNames: '',
          disabled: true,
        }
        this.sheetList.push(sheet)
      })
    },
    changeFileType(row, index) {
      if (this.disabled) {
        return this.sheetList[index].disabled = true
      }
      if (row.fileType > 1) {
        this.sheetList[index].disabled = true
      } else {
        this.sheetList[index].disabled = false
      }
      this.sheetList[index].sheetIndex=index;
      this.fileChage(this.uploadList, this.sheetList)
    },
    closedDialog() {
      let checkedNode = this.$refs.treeNodeBudget.getCheckedNodes();
      if (checkedNode && checkedNode.length > 0) {
        this.sheetList[this.treeNodeCheck.rowIndex].autoType = true;
        this.sheetList[this.treeNodeCheck.rowIndex].budgetNames = '';
        this.sheetList[this.treeNodeCheck.rowIndex].budgetIds = this.$refs.treeNodeBudget.getCheckedKeys();
        checkedNode.forEach(node => {
          this.sheetList[this.treeNodeCheck.rowIndex].budgetNames = this.sheetList[this.treeNodeCheck.rowIndex].budgetNames + "," + node.nodeName;
        })
        this.sheetList[this.treeNodeCheck.rowIndex].budgetNames = this.sheetList[this.treeNodeCheck.rowIndex].budgetNames.replace(/^(\s|,)+|(\s|,)+$/g, '')
      } else {
        this.sheetList[this.treeNodeCheck.rowIndex].autoType = false;
        this.sheetList[this.treeNodeCheck.rowIndex].budgetNames = '';
      }
      this.fileChage(this.uploadList, this.sheetList)
    }
  }
}
</script>
<style scoped>
/deep/ .el-upload {
  width: 100%;
}

/deep/ .el-upload-dragger {
  width: 100%;
}
</style>

