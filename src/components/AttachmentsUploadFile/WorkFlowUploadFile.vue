<template>
  <div>
    <el-upload
      v-if="!disabled"
      width="100%"
      style="width: 100%"
      height="160"
      ref="upload"
      class="upload-demo"
      drag
      action=""
      :show-file-list="false"
      :http-request="httpRequest"
      :before-remove="beforeRemove"
      multiple>
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
    </el-upload>
    <el-divider>文件列表</el-divider>
    <el-table
      :data="uploadList"
      border
      style="width: 100%">
      <el-table-column
        prop="name"
        label="文件名称"
      >
      </el-table-column>
      <el-table-column width="80" prop="size" label="大小" header-align="center"/>
      <el-table-column
        label="操作" align="center">
        <template v-slot="scope">
          <el-button type="text" size='mini' @click="downLoad(scope.row)">下载</el-button>
          <el-button v-if="!disabled" type="text" style="color: red" @click="deleteFile(scope.$index)">删除</el-button>
          <el-button type="text" size='mini' @click="previewAttachment(scope.row)">预览</el-button>
        </template>
      </el-table-column>
    </el-table>

    <reviewFile
      :reviewFile="reviewFile"
      :open-review="showReview"
      :closeReview="closeReview"
    >
    </reviewFile>

  </div>

</template>
<script>
import {upload} from '@/utils/upload'
import request from "@/utils/request";
import {downloadFile} from "@/utils";
import reviewFile from "@/components/Review";
export default {
  props: {

    uploadList: {
      type: Array,
      default: () => []
    },
    addFile: {
      type: Function,
      default: () => {
      }
    },
    deleteFile: {
      type: Function,
      default: () => {
      }
    },
    disabled: {
      type: Boolean,
      default: true
    }
  },
  components: {
    reviewFile
  },
  data() {
    return {
      directoriesCd: '',
      reviewFile: {},
      showReview: false,
    }
  },
  dicts: ['attachment', 'catalogue'],

  methods: {
    // 预览文件
    previewAttachment(row) {
      this.reviewFile = row
      const arrFile = row.name.split(".");
      const fileType = arrFile[arrFile.length - 1].toLowerCase();
      const allowFileType = ['pdf', 'docx', 'xlsx', 'xls']
      if (allowFileType.indexOf(fileType) < 0) {
        this.$message.error('暂不支持该文件类型预览')
        return false
      }
      this.showReview = true
    },
    closeReview() {
      this.showReview = false
    },
    //下载附件
    downLoad(scope) {
      request({
        url: '/general/file/download?uri=' + scope.uri,
        type: 'get',
        responseType: 'blob'
      }).then(res => {
        downloadFile(res, scope.name)
      })
    },
    directoriesChange(val) {
      this.directoriesCd = val
    },
    beforeRemove() {
      return false;
    },
    httpRequest(data) {
      const loading = this.$loading({
        lock: true,
        text: '正在上传，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      upload('general/file/upload', data.file)
        .then(res => {
          if (res.data.status == 200) {
            let result = {
              uri: res.data.data,
              name: data.file.name,
              size: this.formatFileSize(data.file.size),
              uid: data.file.uid
            }
            this.addFile(result)
          } else {
            this.$message.error(res.data.message);
          }
          loading.close();
        })
        .catch(e => {
          loading.close();
          this.$message.error('上传失败！请稍后重试！');
        })
    },
    formatFileSize(fileSize) {
      if (fileSize < 1024) {
        return fileSize + 'B';
      } else if (fileSize < (1024 * 1024)) {
        let temp = fileSize / 1024;
        temp = temp.toFixed(2);
        return temp + 'KB';
      } else if (fileSize < (1024 * 1024 * 1024)) {
        let temp = fileSize / (1024 * 1024);
        temp = temp.toFixed(2);
        return temp + 'MB';
      } else {
        let temp = fileSize / (1024 * 1024 * 1024);
        temp = temp.toFixed(2);
        return temp + 'GB';
      }
    },
  }
}
</script>
<style scoped>
>>> .el-upload {
  width: 100%;
}

>>> .el-upload-dragger {
  width: 100%;
}
</style>

