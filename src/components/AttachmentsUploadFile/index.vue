<template>
  <div>
    <el-upload
      width="100%"
      style="width: 100%"
      height="200"
      ref="upload"
      class="upload-demo"
      drag
      action=""
      :disabled="!editStatus"
      :show-file-list="false"
      :http-request="httpRequest"
      @on-success="onSuccess"
      :before-remove="beforeRemove"
      multiple>
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
    </el-upload>
    <el-divider>文件列表</el-divider>
    <el-table
      :data="uploadList"
      border
      style="width: 100%">
      <el-table-column
        prop="name"
        label="文件名称"
      >
      </el-table-column>

      <el-table-column
        label="附件所属"
        v-if="!isHiddenColumn"
        width="180">
        <template v-slot="scope">
          <el-select v-model="scope.row.affiliationCd" placeholder="请选择">
            <el-option
              v-for="item in dict.attachment"
              :key="item.code"
              :label="item.name"
              :value="item.code">
            </el-option>
          </el-select>
        </template>
      </el-table-column>

      <el-table-column
        label="资料名录"
        v-if="!isHiddenColumn"
        width="180">
        <template v-slot="scope">
          <el-select v-model="scope.row.directoriesCd" placeholder="请选择" @change="directoriesChange">
            <el-option
              v-for="item in dict.catalogue"
              :key="item.code"
              :label="item.name"
              :value="item.code">
            </el-option>
          </el-select>
        </template>
      </el-table-column>

      <el-table-column
        label="操作" align="center">
        <template v-slot="scope">
          <el-button type="text" size='mini' @click="downLoad(scope.row)">下载</el-button>
          <el-button v-show="editStatus" type="text" style="color: red" @click="deleteFile(scope.row)">删除</el-button>
          <el-button type="text" size='mini' @click="previewAttachment(scope.row)">预览</el-button>
        </template>
      </el-table-column>
    </el-table>

    <reviewFile
      :reviewFile="reviewFile"
      :open-review="showReview"
      :closeReview="closeReview"
    >
    </reviewFile>

  </div>

</template>
<script>
import {upload} from '@/utils/upload'
import request from "@/utils/request";
import {downloadFile} from "@/utils";
import reviewFile from "@/components/Review";
export default {
  props: {
    complete: {
      type: Function,
      default: () => {
      }
    },
    isHiddenColumn: {
      type: Boolean,
      default: false
    },
    success: {
      type: Function,
      default: () => {
      }
    },
    uploadFileData: {
      type: Array,
      default: () => []
    },
    fileChage: {
      type: Function,
      default: () => {
      }
    },
    editStatus: {
      type: Boolean,
      default: true
    }
  },
  components: {
    reviewFile
  },
  data() {
    return {
      directoriesCd: '',
      uploadList: [],
      reviewFile: {},
      showReview: false,
    }
  },
  watch: {
    uploadFileData: {
      // uploadFileData 需要深度监听及默认先执行handler函数
      handler(val) {
        this.setDefaultFiles(val);
      },
      immediate: true,
    }
  },
  dicts: ['attachment', 'catalogue'],
  methods: {
    // 预览文件
    previewAttachment(row) {
      this.reviewFile = row
      const arrFile = row.name.split(".");
      const fileType = arrFile[arrFile.length - 1].toLowerCase();
      const allowFileType = ['pdf', 'docx', 'xlsx', 'xls']
      if (allowFileType.indexOf(fileType) < 0) {
        this.$message.error('暂不支持该文件类型预览')
        return false
      }
      this.showReview = true
    },
    closeReview() {
      this.showReview = false
    },
    //下载附件
    downLoad(scope) {
      request({
        url: '/general/file/download?uri=' + scope.uri,
        type: 'get',
        responseType: 'blob'
      }).then(res => {
        downloadFile(res, scope.name)
      })
    },
    directoriesChange(val) {
      this.directoriesCd = val
    },
    //删除文件
    deleteFile(record) {
      this.uploadList = this.uploadList.filter(item => item.uid != record.uid);
      this.fileChage('', this.uploadList);
    },
    setDefaultFiles(data) {
      this.uploadList = data.map(item => ({
        uri: item.uri,
        name: item.name,
        affiliationCd: item.affiliationCd == null ? item.affiliationCd : item.affiliationCd.toString(),
        directoriesCd: item.directoriesCd == null ? item.directoriesCd : item.directoriesCd.toString(),
        uid: item.id,
        isUpload: true
      }))
      this.fileChage('', this.uploadList)
    },
    clearFiles() {
      this.$refs.upload.clearFiles();
      this.uploadList = [];
    },
    onSuccess(data) {

    },
    beforeRemove() {
      return false;
    },
    httpRequest(data) {
      const loading = this.$loading({
        lock: true,
        text: '正在上传，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      upload('general/file/upload', data.file)
        .then(res => {
          if (res.data.status == 200) {
            let result = {
              uri: res.data.data,
              name: data.file.name,
              affiliationCd: '1',
              directoriesCd: this.directoriesCd,
              uid: data.file.uid
            }
            this.uploadList = [...this.uploadList, result];
            this.fileChage(result, this.uploadList)
          } else {
            this.$message.error(res.data.message);
          }
          loading.close();

        })
        .catch(e => {
          loading.close();
          this.$message.error('上传失败！请稍后重试！');
        })
    }
  }
}
</script>
<style scoped>
>>> .el-upload {
  width: 100%;
}

>>> .el-upload-dragger {
  width: 100%;
}
</style>

