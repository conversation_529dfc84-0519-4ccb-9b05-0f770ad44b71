<template>
  <div>
    <el-upload
      :http-request="httpRequest"
      action=""
      accept=".png,.jpg"
      list-type="picture-card"
      :file-list="fileList"
      :on-remove="handleRemove"
      :on-preview="handlePictureCardPreview"
      :before-remove="beforeRemove"
    >
      <i class="el-icon-plus"></i>
    </el-upload>
    <el-dialog :visible.sync="dialogVisible" append-to-body>
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>

  </div>
</template>
<script>
import {upload} from '@/utils/upload'
import request from '@/utils/request'

export default {
  props: {
    fileList: {
      type: Array,
      default: () => []
    },
    removeFile: {
      type: Function,
      default: () => {}
    },
  },
  data() {
    return {
      dialogVisible: false,
      dialogImageUrl: '',
    }
  },
  methods: {
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    clearFiles() {
      this.fileList = [];
    },
    httpRequest(data) {
      const loading = this.$loading({
        lock: true,
        text: '正在上传，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      upload('general/file/img/upload', data.file)
        .then(res => {
          if (res.data.status === 200) {
            this.fileList.push(res.data.data);
          } else {
            this.$message.error(res.data.message);
          }
          loading.close();
        })
        .catch(e => {
          loading.close();
          this.$message.error('上传失败！请稍后重试！');
        })
    },
    beforeRemove(file, fileList) {
      return this.$confirm('确定删除该文件吗?', '提示', {
        type: 'warning'
      })
    },
    handleRemove(file, fileList) {
      request({
        url: 'general/file/img/' + file.id,
        method: 'delete',
      }).then(res => {
        this.$message.success("删除成功!")
        this.removeFile(file)
        })
    },
  }
}
</script>
<style scoped>

>>> .el-dialog__header {
  background: #468EFF;
  padding: 15px;
  height: 50px;
  margin-bottom: 10px;
  color: white;
  box-shadow: 2px 2px 2px #888888;
}

.el-dialog__title {
  line-height: 25px;
  font-size: 18px;
  color: white;
}

>>>.el-upload-list__item.is-ready {
  display: none;
}
>>>.el-upload-list__item {
  transition: none !important;
}
</style>

