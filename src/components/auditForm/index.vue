<template>
  <div>
    <el-tabs v-model="activeName" type="border-card" @tab-click="tabHandleClick">
      <el-tab-pane label="审核内容" name="2">
        <projectDetails
          :id="record.variables.projectAuditId"
          :projectId="record.variables.projectId"
          :record="record"
          :visible="visible"
        />
      </el-tab-pane>
      <el-tab-pane label="审核任务" name="0">
        <template v-show="radio == 1">
          <PreviewForm
            @func="submitRollback"
            :jsonData="record.jsonData"
            :submit="submitData"
            :record="record"
            :isRollback="isRollback"
            :disabled="disabled"
            :defaultValue="defaultValue"
            :addResultAuditFile="addResultAuditFile"
          />
        </template>
      </el-tab-pane>

    </el-tabs>
  </div>
</template>
<script>
import PreviewForm from '@/components/PreviewForm'
import {getHistoryTaskPage} from '@/views/workflow/workflow/config/request'
import projectDetails from '@/views/tools/projectDetails'

export default {
  name: 'auditForm',
  props: {
    defaultValue: {
      type: Object,
      default: () => ({})
    },
    isRollback: {
      type: Boolean,
      default: false
    },
    record: {
      type: Object,
      default: () => ({})
    },
    submit: {
      type: Function,
      default: () => {
      }
    },
    visible: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    addResultAuditFile: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    record: {
      deep: true,
      immediate: true,
      handler(value) {
        this.auditRecords = {};
        this.radio = '1';
        // this.activeName = '2';
        this.auditPage = 1;
      }
    }
  },
  components: {
    PreviewForm,
    projectDetails,
  },
  data() {
    return {
      radio: '1',
      activeName: '2',
      auditRecords: {},
      auditPageSize: 10,
      auditPage: 1,
      auditLoading: false,
    }
  },
  methods: {
    submitRollback(data) {
      this.$emit('func', data);
    },
    tabHandleClick(record, index) {
      if (this.auditRecords.total) return;
      this.getAuditPage();
    },
    getAuditPage() {
      this.auditLoading = true;
      getHistoryTaskPage({
        current: this.auditPage,
        size: this.auditPageSize,
        processInstanceId: this.record.processInstanceId
      })
        .then(res => {
          res.records.map(item => {
            item.formContent = item.formContent && JSON.parse(item.formContent);
            item.formData = item.formData && JSON.parse(item.formData);
          })
          this.auditRecords = res;
          this.auditLoading = false;
        })
        .catch(err => {
          this.auditLoading = false;
        })
    },
    auditPageChange() {
      this.auditPage = this.auditPage + 1;
      this.getAuditPage();
    },
    submitData(data, result) {
      if (this.radio == 1) {
        this.submit({
          examineState: this.radio,
          ...data
        }, result);
      } else {
        this.submit({
          examineState: this.radio
        });
      }
    },

  },
}
</script>
