<template>
  <div>
    <el-upload
      width="100%"
      ref="upload"
      class="upload-demo"
      drag
      action=""
      :show-file-list="false"
      :http-request="httpRequest"
      @on-success="onSuccess"
      :before-remove="beforeRemove"
      multiple>
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
    </el-upload>
    <el-divider>文件列表</el-divider>
    <el-table
      :data="uploadList"
      border
      style="width: 100%">
      <el-table-column
        prop="name"
        label="文件名称"
        align="center"
      >
      </el-table-column>
      <el-table-column
        label="文件说明"
        width="300"
        align="center">
        <template v-slot="scope">
          <el-input v-model="scope.row.remark" placeholder="请输入..." type="textarea"></el-input>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="100"
        align="center">
        <template v-slot="scope">
          <el-button type="text" style="color: red" @click="deleteFile(scope.row)">删除</el-button>
          <el-button type="text" size='mini' @click="previewAttachment(scope.row)">预览</el-button>
        </template>
      </el-table-column>
    </el-table>
    <reviewFile
      :reviewFile="reviewFile"
      :open-review="showReview"
      :closeReview="closeReview"
    >
    </reviewFile>
  </div>

</template>
<script>
  import {upload} from '@/utils/upload'
  import reviewFile from "@/components/Review";
  export default {
    props: {
      complete: {
        type: Function,
        default: () => {
        }
      },
      success: {
        type: Function,
        default: () => {
        }
      },
      fileChage: {
        type: Function,
        default: () => {
        }
      },
    },
    components: {
      reviewFile
    },
    data() {
      return {
        uploadList: [],
        reviewFile: {},
        showReview: false,
      }
    },
    methods: {
      // 预览文件
      previewAttachment(row) {
        this.reviewFile = row
        const arrFile = row.name.split(".");
        const fileType = arrFile[arrFile.length - 1].toLowerCase();
        const allowFileType = ['pdf', 'docx', 'xlsx', 'xls']
        if (allowFileType.indexOf(fileType) < 0) {
          this.$message.error('暂不支持该文件类型预览')
          return false
        }
        this.showReview = true
      },
      closeReview() {
        this.showReview = false
      },
      //删除文件
      deleteFile(record) {
        this.uploadList = this.uploadList.filter(item => item.uid != record.uid);
        this.fileChage('', this.uploadList);
      },
      setDefaultFiles(data) {
        this.uploadList = data.map(item => ({
          src: item.uri,
          name: item.name,
          uid: item.id,
          remark: item.remark,
          isUpload: true
        }))
        this.fileChage('', this.uploadList)
      },
      clearFiles() {
        this.$refs.upload.clearFiles();
        this.uploadList = [];
      },
      onSuccess(data) {

      },
      beforeRemove() {
        return false;
      },
      httpRequest(data) {
        const loading = this.$loading({
          lock: true,
          text: '正在上传，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        upload('general/file/upload', data.file)
          .then(res => {

            if (res.data.status == 200) {
              let result = {
                src: res.data.data,
                name: data.file.name,
                uid: data.file.uid
              }
              this.uploadList = [...this.uploadList, result];
              this.fileChage(result, this.uploadList)
            } else {
              this.$message.error(res.data.message);
            }
            loading.close();

          })
          .catch(e => {
            loading.close();
            this.$message.error('上传失败！请稍后重试！');
          })
      }
    }
  }
</script>
<style scoped>
  >>> .el-upload {
    width: 100%;
  }

  >>> .el-upload-dragger {
    width: 100%;
  }
</style>
