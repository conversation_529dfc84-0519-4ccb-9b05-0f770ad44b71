<template>
  <div :class="className" :style="{height:height,width:width}"/>
</template>

<script>
  import echarts from 'echarts'

  require('echarts/theme/macarons') // echarts theme
  import {debounce} from '@/utils'

  export default {
    props: {
      className: {
        type: String,
        default: 'chart'
      },
      width: {
        type: String,
        default: '100%'
      },
      height: {
        type: String,
        default: '500px'
      },
      dataMap: {
        type: Object,
        default: () => ({barDataMap:[],pieDataMap:[]})
      },
    },
    data() {
      return {
        companyCode: [],
        tabsValue: "",
        proIndex: 0,
        racing: '',
        whetherToChange: false,
        //柱形图option
        option: '',
        chart: null,
        //饼图
        pieChart: {
          color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#D7504B', '#C6E579', '#F4E001', '#D7D8EB'],
          name: "费用占比",
          tooltip: {
            trigger: 'item',
            formatter(params) {
              let dotHtml = '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#ffffff"></span>'
              let proCount = params.marker + '项目数量：' + params.data.proCount[0]
              let pieData = params.marker + '费用：' + params.value[0]
              return params.seriesName + '<br>' + proCount + '<br>' + pieData
            }
          },
          id: 'pie',
          type: 'pie',
          startAngle: 170,
          minAngle: 15,
          labelLine: {
            position: "outer",
            alignTo: "edge",
            margin: 1,
            normal: {
              show: true,
              length: 0 //设置饼图的引导线的长度
            }
          },
          label: {
            position: "outer",
            alignTo: "edge",
            margin: 1,
            normal: {
              formatter(text) {
                return Math.round(text.percent) + '%' + '' + text.name
              },
              textStyle: {
                fontSize: 12
              }
            }
          },
          data: [],
          selected: {
            '项目数量': false
          },
          radius: '23%',
          center: ['88%', '50%']
        }
      }
    },
    watch: {
      immediate: true,
      dataMap(newVal, oldVal) {
        if (newVal != null) {
          this.initChart()
        }
        window.addEventListener('resize', this.__resizeHandler)
      },
      racing() {
        return this.whetherToChange = true;
      }
    },
    created() {
      this.option = {
        color: ['#5470c6', '#3ba272', '#ee6666', "#C9C9C9"],
        title: {},
        tooltip: {
          confine: true,
          trigger: 'axis',
          zlevel: 0,
          z: 60,
          show: true,
          showContent: true,
          alwaysShowContent: false,
          displayMode: "single",
          renderMode: "auto",
          transitionDuration: 0.6,
          triggerOn: "click",
          enterable: true,
          formatter: function (params) {
            var showHtm = params[0]['name'].split('|')[0] + '<br>';
            for (var i = 0; i < params.length; i++) {
              //x轴名称
              var name = params[i]['name'];
              //名称
              var text = params[i]['seriesName'];
              //值
              var value = params[i]['value'];
              showHtm += `<div onclick="tipClick('${text}','${name}')">${params[i].marker + text + ':' + value}</div>`
            }
            return showHtm;
          },
          shadowOffsetX: 1,
          shadowOffsetY: 2,
          borderRadius: 4,
          padding: [5, 10],
          shadowColor: "rgba(0, 0, 0,0.2)",
          shadowBlur: 10,
          backgroundColor: "#fff",
          extraCssText: "box-shadow:0 0 18px rgba(0,0,0,0.3)",
          axisPointer: {
            type: 'shadow',
            axis: "auto",
            animation: 'auto',
            animationDurationUpdate: 200,
            animationEasingUpdate: "exponentialOut",
            crossStyle: {
              color: "#999",
              width: 1,
              type: "dashed"
            },
            label: {
              show: false, //不显示指示器的文本标签
            },
          },
          textStyle: {
            color: "#666"
          }
        },
        legend: {
          // orient: 'vertical',
          left: '80',
          data: []
        },
        dataZoom: [
          // {
          //   type: 'slider',
          //   startValue: 0,
          //   handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
          //   handleSize: '100%',
          //   yAxisIndex: [0],
          //   handleStyle: {
          //     color: '#0B3C6F',
          //     shadowBlur: 3,
          //     shadowColor: 'rgba(0, 0, 0, 0.6)',
          //     shadowOffsetX: 2,
          //     shadowOffsetY: 2
          //   },
          //   bottom: '10px'
          // },
          {
            type: 'slider',//slider表示有滑动块的，inside表示内置的
            // handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            show: true,
            yAxisIndex: [0],
            start: 0,
            end: 30,
            right: '28%'
          },
          {
            type: 'inside',
            yAxisIndex: 0,
            zoomOnMouseWheel: false,  //滚轮是否触发缩放
            moveOnMouseMove: true,  //鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }
        ],
        grid: {
          left: '1%',
          right: '4%',
          top: '20%',
          bottom: '3%',
          containLabel: true,
          width: '68%'
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          axisLabel: {
            formatter: function (value) {
              return value.split("|")[0];
            }
          },
          data: [],
          inverse: true
        },
        series: [],
      }
    },
    beforeDestroy() {
      if (!this.chart) {
        return
      }
      window.removeEventListener('resize', this.__resizeHandler)
      this.chart.dispose()
      this.chart = null
    },
    mounted() {
      window['tipClick'] = (val, xName) => {
        this.tipClick(val, xName)
      }
      this.__resizeHandler = debounce(() => {
        if (this.chart) {
          this.chart.resize()
        }
      }, 100)
    },
    methods: {
      tipClick(val, xName) {
        this.$emit('tabsValue', val, xName)
      },
      initChart() {
        let c = this;
        var racing = ''
        this.chart = echarts.init(this.$el, 'macarons')
        var myChart = this.chart
        let barDataMap = this.dataMap.barDataMap
        let pieDataMap = this.dataMap.pieDataMap
        this.option.series = []
        this.option.legend.data = []
        this.option.yAxis.data = []
        this.pieChart.data = []
        barDataMap.forEach(item => {
          this.option.series.push({name: item.projectType, type: 'bar', data: item.barData})
          this.option.legend.data.push(item.projectType)
        })
        pieDataMap.forEach(item => {
          this.pieChart.data.push({name: item.projectType, value: item.PieData, proCount: item.proCount})
        })
        this.pieChart.data.pop()
        this.option.series.push(this.pieChart)
        this.option.yAxis.data = this.dataMap.companyList
        this.companyCode = this.dataMap.companyCode
        myChart.setOption(this.option)
        myChart.on('updateAxisPointer', function (event) {
          c.proIndex = event.dataindex
          var xAxisInfo = event.axesInfo[0];
          if (xAxisInfo) {
            var dimension = xAxisInfo.value;
            if (dimension != racing) {
              racing = dimension
              myChart.setOption({
                series: {
                  id: 'pie',
                  tooltip: {
                    trigger: 'item',
                    formatter(params) {
                      let dotHtml = '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#ffffff"></span>'
                      let proCount = params.marker + '项目数量：' + params.data.proCount[dimension]
                      let pieData = params.marker + '费用：' + params.value[dimension]
                      return params.seriesName + '<br>' + proCount + '<br>' + pieData
                    }
                  },
                  label: {
                    formatter: '{d}%{b}'
                  },
                  encode: {
                    value: dimension,
                    tooltip: dimension
                  }
                }
              });
            }
          }
        });

      },
    }
  }
</script>
