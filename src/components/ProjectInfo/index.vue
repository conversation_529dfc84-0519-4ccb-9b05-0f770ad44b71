<template>
    <el-form ref="form" :disabled="true" :model="form" :inline="true" :rules="rules" size="small" label-width="200px" style="width:1150px;margin:0 auto;">
                        <template v-for="(item, key) in formFields" >
                            <template v-if="item.type == 'radio'">
                                <el-form-item  label="状态" :prop="item.fieldName" :key="key" :style="formInputStyle">
                                    <el-radio  v-for="item in dict.dept_status" :key="item.id" v-model="form.enabled" :label="item.value">{{ item.label }}</el-radio>
                                </el-form-item>
                            </template>
                            <template v-if="item.type == 'column'">
                                <el-form-item :prop="item.fieldName" :key="key" :style="item.style">
                                    <el-alert
                                        style="width: 100%;"
                                        :title="item.label"
                                        type="info"
                                        :closable="false">
                                    </el-alert>
                                </el-form-item>
                            </template>


                            <template v-if="item.fieldName == 'projectPlannedInvestments'" >
                                <div v-for="(citem, index) in form[item.fieldName]"  :key="index+100">
                                    <template v-if="index == 0">
                                        <el-form-item label="投资计划下达年份" prop="planApprovalDate"  :key="index+3"   label-width="100">
                                            <el-date-picker
                                            style="width:215px"
                                            v-model="citem['planApprovalDate']"
                                            value-format="yyyy-MM-dd HH:mm:ss"
                                            type="date"
                                            @change="planApprovalDateChange(citem['planApprovalDate'])"
                                            placeholder="选择日期时间">
                                            </el-date-picker>
                                        </el-form-item>
                                        <el-form-item label="计划投资编号" prop="fundPlanCode"  :key="index+1"  label-width="180">
                                            <el-input v-model="citem['fundPlanCode']" style="width:215px" @blur="fundPlanCodeChange(citem['fundPlanCode'])"/>
                                        </el-form-item>
                                        <el-form-item label="计划投资额(万元)" prop="planApprovalAmount" :key="index+2"  label-width="180">
                                            <el-input v-model="citem['planApprovalAmount']" style="width:216px" @blur="calculation(citem['planApprovalAmount'])" />
                                        </el-form-item>
                                     </template>
                                    <template v-if="index != 0">
                                        <el-form-item label="投资计划下达年份" style="padding-left: 12px" :key="index+3"   label-width="100">
                                            <el-date-picker
                                            style="width: 215px"
                                            v-model="citem['planApprovalDate']"
                                            value-format="yyyy-MM-dd HH:mm:ss"
                                            type="date"

                                            placeholder="选择日期时间">
                                            </el-date-picker>
                                        </el-form-item>
                                        <el-form-item label="计划投资编号" style="padding-left: 6px" :key="index+1"  label-width="180">
                                            <el-input v-model="citem['fundPlanCode']" style="width:215px"/>
                                        </el-form-item>
                                        <el-form-item label="计划投资额(万元)" style="padding-left: 10px" :key="index+2"  label-width="180">
                                            <el-input v-model="citem['planApprovalAmount']" style="width:216px" @change="calculation"/>
                                        </el-form-item>

                                     </template>
                                     <el-button  v-if="index == 0"  type="primary" @click="addPlan(form[item.fieldName])">添加</el-button>
                                     <el-button  v-if="index != 0"  type="danger" @click="delPlan(form[item.fieldName], citem)">删除</el-button>
                                </div>

                            </template>

                            <template v-if="item.type == 'input' && !item.other">
                                <el-form-item :label="item.label" :prop="item.fieldName"  :key="key" >
                                    <el-input v-model="form[item.fieldName]" :disabled="item.disabled"  :style="formInputStyle"/>
                                </el-form-item>
                            </template>

                             <template v-if="item.type == 'textarea'">
                                <el-form-item :label="item.label" :prop="item.fieldName"  :key="key" >
                                    <el-input type="textarea" v-model="form[item.fieldName]" rows="5"  style="width: 812px;"/>
                                </el-form-item>
                            </template>

                            <template v-if="item.type == 'date'">
                                <el-form-item :label="item.label" :prop="item.fieldName"  :key="key" >
                                    <el-date-picker
                                    v-model="form[item.fieldName]"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    type="date"
                                    :style="formInputStyle"
                                    placeholder="选择日期时间">
                                    </el-date-picker>
                                </el-form-item>
                            </template>

                            <template v-if="item.type == 'select' && item.fieldName == 'projectCategory1Cd'">
                                <el-form-item :label="item.label" :prop="item.fieldName"  :key="key" >
                                    <el-select v-model="form[item.fieldName]" style="width:143px" placeholder="请选择">
                                            <el-option
                                            v-for="(item) in dict.project_category_one"
                                            :key="item.code"
                                            :label="item.name"
                                            :value="item.code"
                                            />
                                    </el-select>
                                </el-form-item>
                            </template>



                            <template v-if="item.type == 'select' && item.fieldName == 'projectCategory2Cd'">
                                <el-form-item :label="item.label" :prop="item.fieldName"  :key="key" >
                                    <el-select v-model="form[item.fieldName]" style="width:143px" placeholder="请选择">
                                            <el-option
                                            v-for="(item) in dict.project_types"
                                            :key="item.code"
                                            :label="item.name"
                                            :value="item.code"
                                            />
                                    </el-select>
                                </el-form-item>
                            </template>

                            <template v-if="item.type == 'select' && item.fieldName == 'buildThreeOrgName'">
                                <el-form-item :label="item.label" :prop="item.fieldName"  :key="key" >
                                    <el-select
                                        v-model="form[item.fieldName]"
                                        :style="formInputStyle"
                                        placeholder="请选择"

                                    >
                                            <el-option
                                            v-for="(item) in buildThreeLevelOrg"
                                            :key="item.parentCode"
                                            :label="item.name"
                                            :value="item.parentCode"
                                            />
                                    </el-select>
                                </el-form-item>
                            </template>

                            <template v-if="item.type == 'select' && item.fieldName == 'progressCd'">
                                <el-form-item :label="item.label" :prop="item.fieldName"  :key="key" >
                                    <el-select v-model="form[item.fieldName]" :style="formInputStyle" placeholder="请选择">
                                            <el-option
                                            v-for="(item) in dict.project_schedule"
                                            :key="item.code"
                                            :label="item.name"
                                            :value="item.code"
                                            />
                                    </el-select>
                                </el-form-item>
                            </template>
                            <template v-if="item.type == 'select' && item.fieldName == 'projectTypeCd'">
                                <el-form-item :label="item.label" :prop="item.fieldName"  :key="key" >
                                    <el-select v-model="form[item.fieldName]" :style="formInputStyle" placeholder="请选择">
                                            <el-option
                                            v-for="(item) in dict.project_type_new"
                                            :key="item.code"
                                            :label="item.name"
                                            :value="item.code"
                                            />
                                    </el-select>
                                </el-form-item>
                            </template>



                            <!-- <template v-if="item.type == 'select'">
                                <el-form-item :label="item.label" :prop="item.fieldName"  :key="key" >
                                    <el-select v-model="form[item.fieldName]" :style="formInputStyle" placeholder="请选择">
                                            <el-option
                                            v-for="(item) in dict.project_types"
                                            :key="item.code"
                                            :label="item.name"
                                            :value="item.code"
                                            />
                                    </el-select>
                                </el-form-item>
                            </template> -->

                        </template>
                    </el-form>
</template>
<script>
import crudDept from '@/views/projectManagement/projectInfo/config/request'
export default {
    props: {
        form: {
            type: Object,
            default: ()=>({})
        },
        rules: {
            type: Object,
            default: ()=>({})
        },
        formFields: {
            type: Array,
            default: ()=>[]
        }
    },
    data() {
        return {
            buildThreeLevelOrg: []
        }
    },
    methods: {
       calculation(val){

                //this.$set(this.form, 'planApprovalAmount', val)
                //this.form = {...this.form};
                let sum = 0;
                this.form.projectPlannedInvestments.map((item, index)=>{
                    sum += item.planApprovalAmount ? parseInt(item.planApprovalAmount) : 0
                })
                this.form.cumulativeInvestmentAmount = sum
            },
    },
    mounted() {
      console.log(this.form)
        // crudDept.getBuildThreeLevelOrg({})
        //     .then(res=>{
        //         this.buildThreeLevelOrg = res;
        //     })
    },
     // 设置数据字典
        dicts: ['project_category_one', 'project_types', 'project_type_new', 'project_schedule', 'project_year'],
}
</script>
