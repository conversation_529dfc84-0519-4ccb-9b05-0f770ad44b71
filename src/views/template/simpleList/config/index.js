export default {
    //获取某类型字段列表
    getFields(type){
        return  this.parames.filter(item=>{
           return item.whereShow.some((citem)=>citem == type)
       })
    },
    //加工页面formData
    getDefaultForm(data){
        var json = {};
        data.map(item=>{
            json[ item.fieldName ] = item.defaultVal;
        })
        return json;
    },
    //页面请求地址
    requestUrl: '/user/org',

    //页面字段参数，可根据实际需求扩展
    parames: [
        {
            label: '名称',                              //字段标签名称
            fieldName: 'name',                         //字段名称
            defaultVal:'',                             //字段默认参数
            type: 'input',                             //自定义扩展参数form类型，可自己扩展
            whereShow: [ 'tableList', 'form']          //在form表单显示还是在table列表显示
        },
        {
            label: '状态',
            fieldName: 'enabled',
            defaultVal: '',
            type: 'input',
            whereShow: [ 'tableList', 'form']
        },
        {
            label: 'id',
            fieldName: 'id',
            defaultVal:'test',
            type: 'input',
            whereShow: [ 'tableList']
        },
        {
            label: '上级部门',
            fieldName: 'pid',
            defaultVal: 1,
            type: 'input',
            whereShow: [ 'tableList', 'form']
        },
        
        {
            label: '创建时间',
            fieldName: 'createTime',
            defaultVal: 1,
            type: 'input',
            whereShow: [ 'tableList']
        },
      
    ]
}