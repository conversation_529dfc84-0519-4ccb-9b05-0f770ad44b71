<template>
<div>
  <KFormDesign></KFormDesign>
</div>
</template>

<script>
//const  KFormDesign = r => require.ensure( [], () => r (require('../../../components/k-form-design/packages')))
import KFormDesign from '../../../components/k-form-design/packages'
import '../../../components/k-form-design/styles/form-design.less'
import Vue from 'vue'
Vue.use( KFormDesign )

export default {
  name: "k",
  created() {
    
  },
}
</script>
<style>
  .content aside{
    width: 300px !important;
    background:#fff;
  }
</style>