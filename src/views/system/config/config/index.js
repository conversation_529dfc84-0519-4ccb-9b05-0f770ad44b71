export default {
    //获取某类型字段列表
    getFields(type){
        return  this.parames.filter(item=>{
            return item.whereShow.some((citem)=>citem == type)
    })
    },
    //加工页面formData
    getDefaultForm(data){
        var json = {};
        data.map(item=>{
            json[ item.fieldName ] = item.defaultVal;
    })
        return json;
    },
    //页面请求地址
    requestUrl: '/system/config',

    //页面字段参数，可根据实际需求扩展
    parames: [
        {
            label: '配置标识',
            fieldName: 'configLabel',
            defaultVal: '',
            type: 'input',
            whereShow: [ 'form', 'add', 'edit']
        },
        {
            label: '可调整的基础值',
            fieldName: 'configValue',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '备注',
            fieldName: 'remark',
            defaultVal: '',
            type: 'textarea',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '是否启用',
            fieldName: 'ifEnabled',
            defaultVal: '1',
            type: 'radio',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },

        {
            label: '数据版本',
            fieldName: 'dataVersion',
            defaultVal: '',
            type: 'input',
            whereShow: []
        },
    ]
}
