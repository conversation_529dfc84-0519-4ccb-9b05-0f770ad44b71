<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.blurry" clearable size="small" placeholder="模糊搜索" style="width: 200px;" class="filter-item" @keyup.enter.native="toQuery" />
        <el-date-picker
          v-model="query.createTime"
          :default-time="['00:00:00','23:59:59']"
          type="daterange"
          range-separator=":"
          size="small"
          class="date-item"
          value-format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
        <rrOperation />
      </div>
      <crudOperation :permission="permission" />
    </div>
    <!--表单渲染-->
    <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="580px">
      <el-form ref="form"  :inline="true" :model="form" :rules="rules" size="small" label-width="80px">
        <el-form-item label="菜单类型" prop="typeCd">

          <el-radio-group v-model="form.typeCd" size="mini" style="width: 178px">
            <el-radio-button label="0">目录</el-radio-button>
            <el-radio-button label="1">菜单</el-radio-button>
            <el-radio-button label="2">按钮</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-show="form.typeCd.toString() !== '2'" label="菜单图标" prop="icon">
          <el-popover
            placement="bottom-start"
            width="450"
            trigger="click"
            @show="$refs['iconSelect'].reset()"
          >
            <IconSelect ref="iconSelect" @selected="selected" />
            <el-input slot="reference" v-model="form.icon" style="width: 450px;" placeholder="点击选择图标" readonly>
              <svg-icon v-if="form.icon" slot="prefix" :icon-class="form.icon" class="el-input__icon" style="height: 32px;width: 16px;" />
              <i v-else slot="prefix" class="el-icon-search el-input__icon" />
            </el-input>
          </el-popover>
        </el-form-item>
        <el-form-item v-show="form.typeCd.toString() !== '2'" label="外链菜单" prop="ifFrame">
          <el-radio-group v-model="form.ifFrame" size="mini">
            <el-radio-button :label="true">是</el-radio-button>
            <el-radio-button :label="false">否</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-show="form.typeCd.toString() === '1'" label="菜单缓存" prop="ifCache">
          <el-radio-group v-model="form.ifCache" size="mini">
            <el-radio-button :label="true">是</el-radio-button>
            <el-radio-button :label="false">否</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-show="form.typeCd.toString() !== '2'" label="菜单可见" prop="ifHidden">
          <el-radio-group v-model="form.ifHidden" size="mini">
            <el-radio-button :label="false">是</el-radio-button>
            <el-radio-button :label="true">否</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-show="form.typeCd.toString() !== '2'" label="菜单标题" prop="title">
          <el-input v-model="form.title" :style=" form.typeCd.toString() === '0' ? 'width: 450px' : 'width: 178px'" placeholder="菜单标题" />
        </el-form-item>
        <el-form-item v-show="form.typeCd.toString() === '2'" label="按钮名称" prop="title">
          <el-input v-model="form.title" placeholder="按钮名称" style="width: 178px;" />
        </el-form-item>
        <el-form-item v-show="form.typeCd.toString() !== '0'" label="权限标识" prop="permissionCode">
          <el-input v-model="form.permissionCode" :disabled="form.ifFrame" placeholder="权限标识" style="width: 178px;" />
        </el-form-item>
        <el-form-item v-if="form.typeCd.toString() !== '2'" label="路由地址" prop="path">
          <el-input v-model="form.path" placeholder="路由地址" style="width: 178px;" />
        </el-form-item>
        <el-form-item label="菜单排序" prop="seq">
          <el-input-number v-model.number="form.seq" :min="0" :max="999" controls-position="right" style="width: 178px;" />
        </el-form-item>
        <el-form-item v-show="!form.ifFrame && form.typeCd.toString() === '1'" label="组件名称" prop="componentName">
          <el-input v-model="form.componentName" style="width: 178px;" placeholder="匹配组件内Name字段" />
        </el-form-item>
        <el-form-item v-show="!form.ifFrame && form.typeCd.toString() === '1'" label="组件路径" prop="component">
          <el-input v-model="form.component" style="width: 178px;" placeholder="组件路径" />
        </el-form-item>
        <el-form-item label="上级类目" prop="pid">
          <treeselect :normalizer="normalizer" v-model="form.pid" :options="menus" style="width: 450px;" placeholder="选择上级类目" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>
    <!--表格渲染-->
    <el-table ref="table" v-loading="crud.loading" :data="crud.data" :tree-props="{children: 'children', hasChildren: 'hasChildren'}" row-key="id" @select="crud.selectChange" @select-all="crud.selectAllChange" @selection-change="crud.selectionChangeHandler" border>
      <el-table-column type="selection" width="55" />
      <el-table-column :show-overflow-tooltip="true" label="菜单名称" width="125px" prop="title" />
      <el-table-column prop="icon" label="图标" align="center" width="60px">
        <template v-slot="scope">
          <svg-icon :icon-class="scope.row.icon ? scope.row.icon : ''" />
        </template>
      </el-table-column>
      <el-table-column prop="seq" align="center" label="排序">
        <template v-slot="scope">
          {{ scope.row.seq }}
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="path" label="路由地址" />
      <el-table-column :show-overflow-tooltip="true" prop="permissionCode" label="权限标识" />
      <el-table-column :show-overflow-tooltip="true" prop="component" label="组件路径" />
      <el-table-column prop="ifFrame" label="外链" width="75px">
        <template v-slot="scope">
          <span v-if="scope.row.ifFrame">是</span>
          <span v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column prop="ifCache" label="缓存" width="75px">
        <template v-slot="scope">
          <span v-if="scope.row.ifCache">是</span>
          <span v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column prop="ifHidden" label="可见" width="75px">
        <template v-slot="scope">
          <span v-if="scope.row.ifHidden">否</span>
          <span v-else>是</span>
        </template>
      </el-table-column>
      <el-table-column prop="createDate" label="创建日期" width="135px">
        <template v-slot="scope">
          <span>{{ parseTime(scope.row.createDate) }}</span>
        </template>
      </el-table-column>
      <el-table-column  label="操作" width="130px" align="center" fixed="right" v-permission="['admin','system:menu:del', 'system:menu:edit']">
        <template v-slot="scope">
          <udOperation
            :data="{
              ...scope.row,
              ifCache: !!scope.row.ifCache,
              ifFrame: !!scope.row.ifFrame,
              ifHidden: !!scope.row.ifHidden,
              typeCd: (scope.row.typeCd - 1 ).toString()
            }"
            :permission="permission"
            msg="确定删除吗,如果存在下级节点则一并删除，此操作不能撤销！"
          />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
//import crudMenu from '@/api/system/menu'
import IconSelect from '@/components/IconSelect'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import crudMenu from './config/request'
// crud交由presenter持有
const defaultForm = { id: null, title: null, seq: 999, path: null, component: null, componentName: null, ifFrame: false, roles: [], pid: 0, icon: null, ifCache: false, ifHidden: false, typeCd: 0, permissionCode: null }
export default {
  name: 'Menu',
  components: { Treeselect, IconSelect, crudOperation, rrOperation, udOperation },
  cruds() {
    return CRUD({ title: '菜单', url: '/system/resource', crudMethod: { ...crudMenu }})
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      menus: [],
      permission: {
        add: ['admin', 'system:menu:add'],
        edit: ['admin', 'system:menu:edit'],
        del: ['admin', 'system:menu:del']
      },
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        path: [
          { required: true, message: '请输入地址', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    normalizer(node){
       return {
          id: node.id,
          label:node.title || node.label,
          children: node.children
      }
    },
    [CRUD.HOOK.afterValidateCU](crud){
      this.crud.formData = {
        ...crud.form,
        ifCache: crud.form.ifCache ? 1 : 0,
        ifFrame: crud.form.ifFrame ? 1 : 0,
        ifHidden: crud.form.ifHidden ? 1 : 0,
        typeCd: parseInt(crud.form.typeCd) + 1
      };
    },
    // 新增与编辑前做的操作
    [CRUD.HOOK.afterToCU](crud, form) {
      crudMenu.getTree().then(res => {
        this.menus = []
        const menu = { id: 0, label: '顶级类目', children: [] }
        menu.children = res;
        this.buildData( res, 'children' );
        this.menus.push(menu)
      })
    },
    // 选中图标
    selected(name) {
      this.form.icon = name
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  >>> .el-input-number .el-input__inner {
    text-align: left;
  }
</style>
