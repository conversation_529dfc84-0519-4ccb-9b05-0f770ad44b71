import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;

export function getPage(params) {
    return request({
        url: requestUrl,
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: requestUrl,
        method: 'post',
        data
    })
}

export function del(ids) {
    return request({
        url: requestUrl,
        method: 'delete',
        data: ids
    })
}

export function edit(data) {
    return request({
        url: requestUrl,
        method: 'put',
        data
    })
}

export function getMenusTree(data) {
    return request({
        url: '/system/role/getMayAuthorizeResource',
        method: 'get',
        data
    })
}

export function editMenu(data){
    return request({
        url: '/system/role/saveAuthorizeResource',
        method: 'put',
        data
    })
}

export default { add, edit, del, getPage, getMenusTree, editMenu }
