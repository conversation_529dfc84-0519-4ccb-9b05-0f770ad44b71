export default {
    //获取某类型字段列表
    getFields(type){
        return  this.parames.filter(item=>{
            return item.whereShow.some((citem)=>citem == type)
    })
    },
    //加工页面formData
    getDefaultForm(data){
        var json = {};
        data.map(item=>{
            json[ item.fieldName ] = item.defaultVal;
    })
        return json;
    },
    //页面请求地址
    requestUrl: '/system/systemLog',

    //页面字段参数，可根据实际需求扩展
    parames: [
        {
            label: '主键ID',
            fieldName: 'id',
            defaultVal: '',
            type: 'input',
            whereShow: []
        },
        {
            label: '模块',
            fieldName: 'module',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '描述',
            fieldName: 'description',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '日志级别',
            fieldName: 'logLevel',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '请求地址',
            fieldName: 'uri',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '请求方法',
            fieldName: 'method',
            defaultVal: '',
            type: 'input',
            whereShow: []
        },
        {
            label: '请求参数',
            fieldName: 'params',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '请求IP',
            fieldName: 'ip',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '耗时',
            fieldName: 'elapsedTime',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '来源',
            fieldName: 'source',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '异常信息',
            fieldName: 'exceptionDetail',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '操作用户',
            fieldName: 'createUserName',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '公司名称',
            fieldName: 'orgName',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '操作时间',
            fieldName: 'createDate',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '更新用户姓名',
            fieldName: 'updateUserName',
            defaultVal: '',
            type: 'input',
            whereShow: []
        },
        {
            label: '更新时间',
            fieldName: 'updateDate',
            defaultVal: '',
            type: 'input',
            whereShow: []
        },
        {
            label: '数据版本',
            fieldName: 'dataVersion',
            defaultVal: '',
            type: 'input',
            whereShow: []
        },
    ]
}
