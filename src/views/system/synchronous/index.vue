<template>
  <div class="app-container">
    <div style="text-align:center">
      <el-button @click="syncOrg" type="primary" style="width: 400px;margin-top: 30px" size="medium" plain>同步组织
      </el-button>
    </div>
    <div style="text-align:center">
      <el-button @click="syncPostToRole" type="primary" style="width: 400px;margin-top: 30px" size="medium" plain>同步角色
      </el-button>
    </div>
    <div style="text-align:center">
      <el-button @click="syncUser" type="primary" style="width: 400px;margin-top: 30px" size="medium" plain>同步用户
      </el-button>
    </div>
    <div style="text-align:center">
      <el-button @click="syncProject" type="primary" style="width: 400px;margin-top: 30px" size="medium" plain>同步项目
      </el-button>
    </div>

    <div style="text-align:center">
      <el-button @click="syncFlowTask" type="primary" style="width: 400px;margin-top: 30px" size="medium" plain>同步审核管理
      </el-button>
    </div>

    <div style="text-align:center">
      <el-button @click="syncMonthly" type="primary" style="width: 400px;margin-top: 30px" size="medium" plain>同步月报
      </el-button>
    </div>

    <div style="text-align:center">
      <el-button @click="syncOrganizeQualification" type="primary" style="width: 400px;margin-top: 30px" size="medium"
                 plain>同步企业资质
      </el-button>
    </div>

    <div style="text-align:center">
      <el-button @click="syncOrganizeUser" type="primary" style="width: 400px;margin-top: 30px" size="medium" plain>
        同步个人资质
      </el-button>
    </div>

    <div style="text-align:center">
      <el-button @click="syncJstUser" type="primary" style="width: 400px;margin-top: 30px" size="medium" plain>同步即时通信
      </el-button>
    </div>

    <div style="text-align:center">
      <el-button @click="updateWorkflowBpmn" type="primary" style="width: 400px;margin-top: 30px" size="medium" plain>
        更新流程-新增选外委或自审节点
      </el-button>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'
import axios from "axios";
import {getToken} from '@/utils/auth'
import {MessageBox, Notification} from "element-ui";
import {picAdaptation} from "../../../utils";

// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
  timeout: 12000000 // 请求超时时间
})

// request拦截器
service.interceptors.request.use(
    config => {
      if (getToken()) {
        config.headers['xToken'] = getToken()
      }
      config.headers['Content-Type'] = 'application/json'
      return config
    },
    error => {
      // Do something with request error
      console.log(error) // for debug
      Promise.reject(error)
    }
)
// response 拦截器
service.interceptors.response.use(
    response => {
      const code = response.status
      if (code < 200 || code > 300) {
        Notification.error({
          title: response.message
        })
        return Promise.reject('error')
      } else {
        // TODO by tangjf
        // return response.data
        const apiRes = response.data;
        const contentType = response.headers['content-type'];
        if (contentType.indexOf('application/json') > -1) {
          // 数据接口返回值处理
          if (apiRes.success && apiRes.status == 200) {
            return apiRes.data
            return picAdaptation(apiRes.data);
          } else {
            Notification.error({
              title: apiRes.message
            })
            return Promise.reject('error')
          }
        }
      }
    },
    error => {
      let code = 0
      try {
        code = error.response.data.status
      } catch (e) {
        if (error.toString().indexOf('Error: timeout') !== -1) {
          Notification.error({
            title: '网络请求超时',
            duration: 5000
          })
          return Promise.reject(error)
        }
      }
      if (code) {

      } else {
        Notification.error({
          title: '接口请求失败',
          duration: 5000
        })
      }
      return Promise.reject(error)
    }
)
export default {
  name: "index",
  data() {
    return {}
  },
  methods: {
    syncProject() {
      const loading = this.$loading({
        lock: true,
        text: '正在同步项目，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      service({url: '/system/sync/syncProject', method: 'get'})
          .then(res => {
            if (res == 60000) {
              this.$message.success("同步完成")
            } else {
              this.$message.error("失败")
            }
            loading.close()
          }).catch(e => {
        loading.close()
      })
    },
    syncOrg() {
      const loading = this.$loading({
        lock: true,
        text: '正在同步组织，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0,0,0,0.7)'
      });
      service({url: '/system/sync/syncOrg', method: 'get'})
          .then(res => {
            console.log(res)
            if (res == 60000) {
              this.$message.success("同步完成")
            } else {
              this.$message.error("失败")
            }
            loading.close()
          }).catch(e => {
        loading.close()
      })
    },
    syncPostToRole() {
      const loading = this.$loading({
        lock: true,
        text: '正在同步角色，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0,0,0,0.7)'
      });
      service({url: '/system/sync/syncPostToRole', method: 'get'})
          .then(res => {
            if (res == 60000) {
              this.$message.success("同步完成")
            } else {
              this.$message.error("失败")
            }
            loading.close()
          }).catch(e => {
        loading.close()
      })
    },
    syncUser() {
      const loading = this.$loading({
        lock: true,
        text: '正在同步用户，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0,0,0,0.7)'
      });
      service({url: '/system/sync/syncUser', method: 'get'})
          .then(res => {
            if (res == 60000) {
              this.$message.success("同步完成")
            } else {
              this.$message.error("失败")
            }
            loading.close()
          }).catch(e => {
        loading.close()
      })
    },
    syncFlowTask() {
      const loading = this.$loading({
        lock: true,
        text: '正在同步审核管理，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0,0,0,0.7)'
      });
      service({url: '/system/sync/syncFlowTask', method: 'get'})
          .then(res => {
            if (res == 60000) {
              this.$message.success("同步完成")
            } else {
              this.$message.error("失败")
            }
            loading.close()
          }).catch(e => {
        loading.close()
      })
    },
    syncMonthly() {
      const loading = this.$loading({
        lock: true,
        text: '正在同步月报，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0,0,0,0.7)'
      });
      service({url: '/system/sync/syncMonthly', method: 'get'})
          .then(res => {
            if (res == 60000) {
              this.$message.success("同步完成")
            } else {
              this.$message.error("失败")
            }
            loading.close()
          }).catch(e => {
        loading.close()
      })
    },
    syncOrganizeQualification() {
      const loading = this.$loading({
        lock: true,
        text: '正在同步企业资质，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0,0,0,0.7)'
      });
      service({url: '/system/sync/syncOrganizeQualification', method: 'get'})
          .then(res => {
            if (res == 60000) {
              this.$message.success("同步完成")
            } else {
              this.$message.error("失败")
            }
            loading.close()
          }).catch(e => {
        loading.close()
      })
    },
    syncOrganizeUser() {
      const loading = this.$loading({
        lock: true,
        text: '正在同步个人资质，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0,0,0,0.7)'
      });
      service({url: '/system/sync/syncOrganizeUser', method: 'get'})
          .then(res => {
            if (res == 60000) {
              this.$message.success("同步完成")
            } else {
              this.$message.error("失败")
            }
            loading.close()
          }).catch(e => {
        loading.close()
      })
    },
    syncJstUser() {
      const loading = this.$loading({
        lock: true,
        text: '正在同步即时通信，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0,0,0,0.7)'
      });
      service({url: '/system/sync/syncJstUser', method: 'get'})
          .then(res => {
            if (res == 60000) {
              this.$message.success("同步完成")
            } else {
              this.$message.error("失败")
            }
            loading.close()
          }).catch(e => {
            loading.close()
      })
    },
    updateWorkflowBpmn() {
      const loading = this.$loading({
        lock: true,
        text: '正在更新，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0,0,0,0.7)'
      });
      service({url: '/system/sync/updateWorkflowBpmn', method: 'get'})
          .then(res => {
            if (res == 60000) {
              this.$alert('同步完成', '消息', {
                confirmButtonText: '确定',
                callback: action => {
                }
              });
            } else {
              this.$alert('同步失败:'+err, '消息', {
                confirmButtonText: '确定',
                callback: action => {
                }
              });
            }
            loading.close()
          }).catch(e => {
          loading.close()
      })
    }
  }
}
</script>

<style scoped>

</style>
