﻿export default {
    //获取某类型字段列表
    getFields(type){
        return  this.parames.filter(item=>{
            return item.whereShow.some((citem)=>citem == type)
    })
    },
    //加工页面formData
    getDefaultForm(data){
        var json = {};
        data.map(item=>{
            json[ item.fieldName ] = item.defaultVal;
    })
        return json;
    },
    //页面请求地址
    requestUrl: '/system/orgBuild',

    //页面字段参数，可根据实际需求扩展
    parames: [
        {
            label: '名称',
            fieldName: 'name',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '编码（生成组织编码的前缀）',
            fieldName: 'code',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '类型编码',
            fieldName: 'typeCd',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '是否允许构建',
            fieldName: 'ifAllowBuild',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '系统角色',
            fieldName: 'sysRoleName',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '描述',
            fieldName: 'remark',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
    ]
}