<template>
    <div class="app-container">
        <!--工具栏-->
        <div class="head-container">
            <!-- <div> -->
                <!-- 搜索 -->
                <!-- <el-input v-model="query.name" clearable size="small" placeholder="输入部门名称搜索" style="width: 200px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
                <el-date-picker
                        v-model="query.createTime"
                        :default-time="['00:00:00','23:59:59']"
                        type="daterange"
                        range-separator=":"
                        size="small"
                        class="date-item"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                />
                <el-select v-model="query.enabled" clearable size="small" placeholder="状态" class="filter-item" style="width: 90px" @change="crud.toQuery">
                   <el-option v-for="item in dict.disable_enable" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
                <rrOperation />
            </div> -->
            <!-- <crudOperation :permission="permission" /> -->
        </div>
        <!--表单组件-->
        <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
            <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
                <template v-for="(item, key) in formFields" >
                    <template v-if="item.type == 'radio'">

                        <el-form-item  label="状态" :prop="item.fieldName" :key="key">
                            <el-radio v-for="item in dict.disable_enable" :key="item.code" v-model="form.ifEnabled" :label="item.code">{{ item.name }}</el-radio>
                        </el-form-item>
                    </template>
                    <template v-if="item.type == 'input'">
                        <el-form-item :label="item.label" :prop="item.fieldName"  :key="key">
                            <el-input v-model="form[item.fieldName]" style="width: 370px;" />
                        </el-form-item>
                    </template>

                    <template v-if="item.type == 'textarea'">
                        <el-form-item :label="item.label" :prop="item.fieldName"  :key="key">
                            <el-input :type="item.type" v-model="form[item.fieldName]" style="width: 370px;" />
                        </el-form-item>
                    </template>
                </template>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="text" @click="crud.cancelCU">取消</el-button>
                <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
            </div>
        </el-dialog>
        <!--表格渲染-->
        <el-table ref="table" v-loading="crud.loading" :tree-props="{children: 'children', hasChildren: 'hasChildren'}" default-expand-all :data="crud.data" row-key="id" @select="crud.selectChange" @select-all="crud.selectAllChange" @selection-change="crud.selectionChangeHandler" border>

            <template v-for="(item, key) in tableListFields">
                <el-table-column :label="item.label" :prop="item.fieldName" :key="key">
                    <template v-slot="scope">
                        <template v-if="item.fieldName == 'ifAllowBuild'">
                            {{ showCurrentData(dict.yes_no, scope.row[ item.fieldName ]) }}
                        </template>
                        <template v-else>
                            {{ scope.row[ item.fieldName ] }}
                        </template>
                    </template>
                </el-table-column>
            </template>

            <!-- <el-table-column v-permission="['admin','system:config:edit','system:config:del']" label="操作" width="130px" align="center" fixed="right">
                <template v-slot="scope">
                    <udOperation
                            :data="{
                                ...scope.row
                            }"
                            :permission="permission"
                            :disabledDle="scope.row.id === 1"
                            msg="确定删除吗,如果存在下级节点则一并删除，此操作不能撤销！"
                    />
                </template>
            </el-table-column> -->
        </el-table>
        <!--分页组件-->
        <!-- <pagination /> -->
    </div>
</template>

<script>
    import crudDept from './config/request'
    import Treeselect from '@riophae/vue-treeselect'
    import '@riophae/vue-treeselect/dist/vue-treeselect.css'
    import CRUD, { presenter, header, form, crud } from '@crud/crud'
    import rrOperation from '@crud/RR.operation'
    import crudOperation from '@crud/CRUD.operation'
    import udOperation from '@crud/UD.operation'
    import pagination from '@crud/Pagination'

    //注入配置文件
    import config from './config/index'

    const formFields = config.getFields('form');
    const tableListFields = config.getFields('tableList');
    const  defaultForm = config.getDefaultForm( formFields );

    export default {
        name: 'simpleList',
        components: { Treeselect, crudOperation, rrOperation, udOperation, pagination },
        cruds() {
            return CRUD({ title: '配置', url: config.requestUrl, crudMethod: { ...crudDept }})
        },
        mixins: [presenter(), header(), form(defaultForm), crud()],
        // 设置数据字典
        dicts: ['yes_no'],
        data() {
            return {
                depts: [],
                formFields: formFields,
                tableListFields: tableListFields,
                rules: {
                    name: [
                        { required: true, message: '请输入名称', trigger: 'blur' }
                    ]
                },
                permission: {
                    add: ['admin', 'system:config:add'],
                    edit: ['admin', 'system:config:edit'],
                    del: ['admin', 'system:config:del']
                },
                enabledTypeOptions: [
                    { key: 'true', display_name: '正常' },
                    { key: 'false', display_name: '禁用' }
                ]
            }
        },
        methods: {
            // 新增与编辑前做的操作
            [CRUD.HOOK.afterToCU](crud, form) {
                form.ifEnabled = form.ifEnabled.toString();
            //     form.enabled = `${form.enabled}`
            //     // 获取所有部门
            //     crudDept.getPage({ enabled: true }).then(res => {
            //         this.depts = res.content
            // })
            },
            // 提交前的验证
            [CRUD.HOOK.afterValidateCU]() {
                // if (!this.form.pid && this.form.id !== 1) {
                //     this.$message({
                //         message: '上级部门不能为空',
                //         type: 'warning'
                //     })
                //     return false
                // }
                return true
            },
            // 改变状态
            changeEnabled(data, val) {
                this.$confirm('此操作将 "' + this.dict.label.dept_status[val] + '" ' + data.name + '部门, 是否继续？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    crudDept.edit(data).then(res => {
                    this.crud.notify(this.dict.label.dept_status[val] + '成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
            }).catch(err => {
                    data.enabled = !data.enabled
                console.log(err.response.data.message)
            })
            }).catch(() => {
                    data.enabled = !data.enabled
            })
            },
            checkboxT(row, rowIndex) {
                return row.id !== 1
            }
        }
    }
</script>

<style scoped>
</style>
