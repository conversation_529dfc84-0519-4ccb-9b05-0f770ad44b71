export default {
    //获取某类型字段列表
    getFields(type){
        return  this.parames.filter(item=>{
            return item.whereShow.some((citem)=>citem == type)
    })
    },
    //加工页面formData
    getDefaultForm(data){
        var json = {};
        data.map(item=>{
            json[ item.fieldName ] = item.defaultVal;
    })
        return json;
    },
    //页面请求地址
    requestUrl: '/system/schedule',

    //页面字段参数，可根据实际需求扩展
    parames: [
        {
            label: '任务组',
            fieldName: 'jobGroup',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'edit']
        },
        {
            label: '任务名称',
            fieldName: 'jobName',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '任务类',
            fieldName: 'jobClass',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '任务执行时间',
            fieldName: 'jobTime',
            defaultVal: '',
            type: 'cron',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '任务状态',
            fieldName: 'jobStatus',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '执行结果',
            fieldName: 'success',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '上一次执行时间',
            fieldName: 'lastRunTime',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '下一次执行时间',
            fieldName: 'nextRunTime',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
    ]
}