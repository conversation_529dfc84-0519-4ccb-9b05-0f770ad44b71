import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;

export function getPage(params) {
    return request({
        url: requestUrl,
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: requestUrl + '/save',
        method: 'post',
        data
    })
}

export function del(ids) {
    return request({
        url: requestUrl + '/remove',
        method: 'delete',
        data: ids
    })
}

export function edit(data) {
    return request({
        url: requestUrl+'/update',
        method: 'put',
        data
    })
}

//暂停指定的定时任务
export function pause(params) {
    return request({
        url: requestUrl+'/pause',
        method: 'put',
        data: params
    })
}

//删除指定的定时任务
export function remove(params) {
    return request({
        url: requestUrl+'/remove',
        method: 'delete',
        data: params
    })
}

//恢复指定的定时任务
export function resume(params) {
    return request({
        url: requestUrl+'/resume',
        method: 'put',
        data: params
    })
}

//保存指定的定时任务
export function save(params) {
    return request({
        url: requestUrl+'/save',
        method: 'post',
        data: params
    })
}

//立即执行指定的定时任务
export function trigger(params) {
    return request({
        url: requestUrl+'/trigger',
        method: 'put',
        data: params
    })
}

//更新任务时间
export function update(params) {
    return request({
        url: requestUrl+'/update',
        method: 'put',
        data: params
    })
}



export default { add, edit, del, getPage, pause, remove, resume, save, trigger, update }
