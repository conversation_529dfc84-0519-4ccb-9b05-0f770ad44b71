<template>
  <div class="app-container">
    <el-collapse accordion v-model="collapseActiveNames" @change="changeCollapse">
      <el-collapse-item v-for="(item, index) in pageList" :key="item.query.type" :name="item.query.type">
        <template slot="title">
          {{ item.query.name }}
        </template>


        <el-table ref="table" default-expand-all :data="dataList[index]" row-key="id" border>
          <template
            v-for="(item, key) in tableListFields.filter(item=>item.fieldName != 'fileName' && item.fieldName != 'fileUri')">
            <el-table-column :label="item.label" :prop="item.fieldName" :key="key" align="center">
              <template v-slot="scope">
                <template v-if="item.fieldName == 'title'">
                  <router-link :to="'/announceDetails/announceDetails?id='+scope.row.id">
                    <el-button type="text" size="small">{{ scope.row[ item.fieldName ] }}</el-button>
                  </router-link>
                </template>
                <template v-else-if="item.fieldName == 'typeCd'">
                  {{ showCurrentData(dict.announcement_type, scope.row[ item.fieldName ]) }}
                </template>
                <template v-else-if="item.fieldName == 'statusCd'">
                  {{ showCurrentData(status, scope.row[ item.fieldName ]) }}
                </template>
                <template v-else>
                  {{scope.row[ item.fieldName ]}}
                </template>
              </template>
            </el-table-column>
          </template>
        </el-table>
        <el-pagination
          :page-size="pageList[index].size"
          :current-page="pageList[index].current"
          layout="prev, pager, next"
          @current-change="auditPageChange(index)"
          :total="pageList[index].total">
        </el-pagination>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
  import crudRequest from './config/request'
  import '@riophae/vue-treeselect/dist/vue-treeselect.css'
  //注入配置文件
  import config from './config/index'
  import {getDictItem} from "../../../components/Dict/request";

  const formFields = config.getFields('form');
  const tableListFields = config.getFields('tableListList');
  export default {
    // 设置数据字典
    dicts: [],
    data() {
      return {
        tableListFields: tableListFields,
        formInputStyle: 'width:171px',
        status: [
          {
            name: '未发布',
            code: 1
          },
          {
            name: '已发布',
            code: 2
          }
        ],
        collapseActiveNames: 0,
        dataList: [],
        pageList: [],
      }
    },
    mounted() {
      //初始化page对象
      getDictItem('announcement_type').then(res => {
        for (let i = 0; i < res.length; i++) {
          let pageQuery = {
            size: 10,
            current: 1,
            total: 0,
            query: {
              type: Number(res[i].code),
              name: res[i].name,
            }
          }
          this.pageList.push(pageQuery)
        }
        //初始化数据第一个
        this.changeCollapse(0);
      })

    },
    methods: {
      auditPageChange(index) {
        crudRequest.pageByType(this.pageList[index]).then(res => {
          this.dataList[index] = res.records;
          this.pageList[code].current = res.current;
          this.pageList[code].total = res.total;
        })
      },
      changeCollapse(code) {
        if (code){
          crudRequest.pageByType(this.pageList[code]).then(res => {
            this.dataList[code] = res.records;
            this.pageList[code].current = res.current;
            this.pageList[code].total = res.total;
          })
        }
      }
    },


  }
</script>


