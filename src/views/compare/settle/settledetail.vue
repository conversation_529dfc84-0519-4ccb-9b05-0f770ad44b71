<!--搜索与重置-->
<template>
  <!--结算详情弹窗-->
  <el-dialog title="结算详情" :visible.sync="showDialog" width="1020px" :before-close="closeDialog">
    <el-tabs v-model="tab.defaultTab" type="card" @tab-click="dialogHandleClick">
      <el-tab-pane label="安装费" name="0">
        <el-table
          :data="tab.resultDetailInstallTable"
          style="width: 100%;"
          height="600"
          highlight-current-row
          default-expand-all
          row-key="id"
          border
          lazy
          :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
          <el-table-column prop="nodeNub" label="序号" header-align="center"/>
          <el-table-column prop="name" label="名称" header-align="center"/>
          <template v-if="showCompare">
            <el-table-column label="初审" header-align="center">
              <el-table-column prop="unit" label="单位" header-align="center"/>
              <el-table-column prop="projectCount" label="工程量" header-align="center"/>
              <el-table-column prop="auditAmount" label="金额" header-align="center" align="right">
                <template slot-scope="scope">
                  {{ parseDecimal4(scope.row.auditAmount) }}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="一审" header-align="center">
              <el-table-column prop="auditAmountOne" label="金额" header-align="center" align="right">
                <template slot-scope="scope">
                  {{ parseDecimal4(scope.row.auditAmountOne) }}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="二审" header-align="center">
              <el-table-column prop="auditAmountTwo" label="金额" header-align="center" align="right">
                <template slot-scope="scope">
                  {{ parseDecimal4(scope.row.auditAmountTwo) }}
                </template>
              </el-table-column>
            </el-table-column>
          </template>
          <template v-else>
            <el-table-column prop="unit" label="单位" header-align="center"/>
            <el-table-column prop="projectCount" label="工程量" header-align="center"/>
            <el-table-column prop="auditAmount" label="金额" header-align="center" align="right">
              <template slot-scope="scope">
                {{ parseDecimal4(scope.row.auditAmount) }}
              </template>
            </el-table-column>
          </template>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="建筑费" name="1">
        <el-table
          :data="tab.resultDetailBuildTable"
          style="width: 100%;"
          height="500"
          highlight-current-row
          default-expand-all
          row-key="id"
          border
          :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
          <el-table-column prop="nodeNub" label="序号" header-align="center"/>
          <el-table-column prop="name" label="名称" header-align="center"/>
          <template v-if="showCompare">
            <el-table-column label="初审" header-align="center">
              <el-table-column prop="unit" label="单位" header-align="center"/>
              <el-table-column prop="projectCount" label="工程量" header-align="center"/>
              <el-table-column prop="auditAmount" label="金额" header-align="center" align="right">
                <template slot-scope="scope">
                  {{ parseDecimal4(scope.row.auditAmount) }}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="一审" header-align="center">
              <el-table-column prop="auditAmountOne" label="金额" header-align="center" align="right">
                <template slot-scope="scope">
                  {{ parseDecimal4(scope.row.auditAmountOne) }}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="二审" header-align="center">
              <el-table-column prop="auditAmountTwo" label="金额" header-align="center" align="right">
                <template slot-scope="scope">
                  {{ parseDecimal4(scope.row.auditAmountTwo) }}
                </template>
              </el-table-column>
            </el-table-column>
          </template>
          <template v-else>
            <el-table-column prop="unit" label="单位" header-align="center"/>
            <el-table-column prop="projectCount" label="工程量" header-align="center"/>
            <el-table-column prop="auditAmount" label="金额" header-align="center">
              <template slot-scope="scope">
                {{ parseDecimal4(scope.row.auditAmount) }}
              </template>
            </el-table-column>
          </template>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="其他费信息" name="2">
        <el-table
          ref="other"
          :data="tab.resultOtherDetailData"
          style="width: 100%;"
          height="600"
          highlight-current-row
          row-key="id"
          border
          default-expand-all
          :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
          <el-table-column prop="nodeNub" label="序号" header-align="center"/>
          <el-table-column prop="name" label="名称" header-align="center"/>
          <template v-if="showCompare">
            <el-table-column label="初审" header-align="center">
              <el-table-column prop="amountBasis" label="取费依据" header-align="center"/>
              <el-table-column prop="remark" label="说明" header-align="center"/>
              <el-table-column prop="otherTotalPay" label="金额" header-align="center" align="right">
                <template slot-scope="scope">
                  {{ parseDecimal4(scope.row.otherTotalPay) }}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="一审" header-align="center">
              <el-table-column prop="otherTotalPayOne" label="金额" header-align="center" align="right">
                <template slot-scope="scope">
                  {{ parseDecimal4(scope.row.otherTotalPayOne) }}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="二审" header-align="center">
              <el-table-column prop="otherTotalPayTwo" label="金额" header-align="center" align="right">
                <template slot-scope="scope">
                  {{ parseDecimal4(scope.row.otherTotalPayTwo) }}
                </template>
              </el-table-column>
            </el-table-column>
          </template>
          <template v-else>
            <el-table-column prop="amountBasis" label="取费依据" header-align="center"/>
            <el-table-column prop="remark" label="说明" header-align="center"/>
            <el-table-column prop="otherTotalPay" label="金额" header-align="center" align="right">
              <template slot-scope="scope">
                {{ parseDecimal4(scope.row.otherTotalPay) }}
              </template>
            </el-table-column>
          </template>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="设备(甲供)信息" name="3">
        <el-table
          :data="tab.resultFirstDetailData"
          style="width: 100%;"
          height="600"
          highlight-current-row
          row-key="id"
          border
          default-expand-all
          :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
          <el-table-column prop="nodeNub" label="序号" header-align="center"/>
          <el-table-column prop="emName" label="名称" header-align="center"/>
          <template v-if="showCompare">
            <el-table-column label="初审" header-align="center">
              <el-table-column prop="unit" label="单位" header-align="center"/>
              <el-table-column prop="count" label="数量" header-align="center"/>
              <el-table-column prop="emBuyPay" label="购置单价" header-align="center" align="right">
                <template slot-scope="scope">
                  {{ parseDecimal4(scope.row.emBuyPay) }}
                </template>
              </el-table-column>
              <el-table-column prop="emUsedCount" label="使用量" header-align="center"/>
              <el-table-column prop="emReturnCount" label="退款量" header-align="center"/>
              <el-table-column prop="emDifference" label="差额" header-align="center" align="right">
                <template slot-scope="scope">
                  {{ parseDecimal4(scope.row.emDifference) }}
                </template>
              </el-table-column>
              <el-table-column prop="emTotalPay" label="金额" header-align="center" align="right">
                <template slot-scope="scope">
                  {{ parseDecimal4(scope.row.emTotalPay) }}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="一审" header-align="center">
              <el-table-column prop="emTotalPayOne" label="金额" header-align="center" align="right">
                <template slot-scope="scope">
                  {{ parseDecimal4(scope.row.emTotalPayOne) }}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="二审" header-align="center">
              <el-table-column prop="emTotalPayTwo" label="金额" header-align="center" align="right">
                <template slot-scope="scope">
                  {{ parseDecimal4(scope.row.emTotalPayTwo) }}
                </template>
              </el-table-column>
            </el-table-column>
          </template>
          <template v-else>
            <el-table-column prop="unit" label="单位" header-align="center"/>
            <el-table-column prop="count" label="数量" header-align="center"/>
            <el-table-column prop="emBuyPay" label="购置单价" header-align="center" align="right">
              <template slot-scope="scope">
                {{ parseDecimal4(scope.row.emBuyPay) }}
              </template>
            </el-table-column>
            <el-table-column prop="emUsedCount" label="使用量" header-align="center"/>
            <el-table-column prop="emReturnCount" label="退款量" header-align="center"/>
            <el-table-column prop="emDifference" label="差额" header-align="center" align="right">
              <template slot-scope="scope">
                {{ parseDecimal4(scope.row.emDifference) }}
              </template>
            </el-table-column>
            <el-table-column prop="emTotalPay" label="金额" header-align="center" align="right">
              <template slot-scope="scope">
                {{ parseDecimal4(scope.row.emTotalPay) }}
              </template>
            </el-table-column>
          </template>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="设备(乙供)信息" name="4">
        <el-table
          :data="tab.resultSecondDetailData"
          style="width: 100%;"
          height="600"
          highlight-current-row
          row-key="id"
          border
          default-expand-all
          :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
          <el-table-column prop="nodeNub" label="序号" header-align="center"/>
          <el-table-column prop="emName" label="名称" header-align="center"/>
          <template v-if="showCompare">
            <el-table-column label="初审" header-align="center">
              <el-table-column prop="unit" label="单位" header-align="center"/>
              <el-table-column prop="count" label="数量" header-align="center"/>
              <el-table-column prop="emBuyPay" label="购置单价" header-align="center" align="right">
                <template slot-scope="scope">
                  {{ parseDecimal4(scope.row.emBuyPay) }}
                </template>
              </el-table-column>
              <el-table-column prop="emTotalPay" label="金额" header-align="center" align="right">
                <template slot-scope="scope">
                  {{ parseDecimal4(scope.row.emTotalPay) }}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="一审" header-align="center">
              <el-table-column prop="emTotalPayOne" label="金额" header-align="center" align="right">
                <template slot-scope="scope">
                  {{ parseDecimal4(scope.row.emTotalPayOne) }}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="二审" header-align="center">
              <el-table-column prop="emTotalPayTwo" label="金额" header-align="center" align="right">
                <template slot-scope="scope">
                  {{ parseDecimal4(scope.row.emTotalPayTwo) }}
                </template>
              </el-table-column>
            </el-table-column>
          </template>
          <template v-else>
            <el-table-column prop="unit" label="单位" header-align="center"/>
            <el-table-column prop="count" label="数量" header-align="center"/>
            <el-table-column prop="emBuyPay" label="购置单价" header-align="center" align="right">
              <template slot-scope="scope">
                {{ parseDecimal4(scope.row.emBuyPay) }}
              </template>
            </el-table-column>
            <el-table-column prop="emTotalPay" label="金额" header-align="center" align="right">
              <template slot-scope="scope">
                {{ parseDecimal4(scope.row.emTotalPay) }}
              </template>
            </el-table-column>
          </template>
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>
<script>

import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import {getResultDetailTable} from "@/views/compare/result/config/request";
import {getSettleDetail} from "@/views/compare/settle/config/request";

export default {
  name: 'settleDetail',
  props: {
    resultId: {
      type: String,
      default: () => ''
    },
    dataType: {
      type: Number,
      default: () => 3
    },
    showCompare: {
      type: Boolean,
      default: () => false
    },
    showDialog: {
      type: Boolean,
      default: () => false
    },

  },
  watch: {
    showDialog: {
      immediate: true,
      handler() {
        if (this.showDialog) {
          this.initDialog()
        }
      }
    },
  },
  data() {
    return {
      tab: {
        defaultTab: "0",
        resultDetailInstallTable: [],
        resultDetailBuildTable: [],
        resultOtherDetailData: [],
        resultFirstDetailData: [],
        resultSecondDetailData: [],
      }
    }
  },
  methods: {
    resultDetailCompare() {
      if (!this.resultId) return
      getResultDetailTable(this.resultId).then(res => {
        this.initResData(res);
      })
    },
    initDialog() {
      if (this.showCompare) {
        this.resultDetailCompare();
      } else {
        this.resultDetail()
      }
    },
    initResData(res) {
      this.tab.resultDetailInstallTable = res ? res.resultInstallList : [];
      this.tab.resultDetailBuildTable = res ? res.resultBuildList : [];
      this.tab.resultOtherDetailData = res ? res.otherList : [];
      this.tab.resultFirstDetailData = res ? res.firstList : [];
      this.tab.resultSecondDetailData = res ? res.secondList : [];
    },
    dialogHandleClick({name}) {
      this.$nextTick(() => {
        this.$refs['other'].doLayout()
      })
    },
    resultDetail() {
      if (!this.resultId) return
      getSettleDetail(this.resultId, this.dataType).then(res => {
        this.initResData(res);
      })
    },
    closeDialog() {
      this.$emit('close-compare');
    }
  }
}
</script>

