<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <el-input v-model="query.projectName" clearable size="small" placeholder="请输入项目名称" style="width: 200px;"
                  class="filter-item" @change="crud.toQuery"/>
        <!-- 搜索 -->
        <el-input v-model="query.auditNo" clearable size="small" placeholder="请输入报审编号" style="width: 200px;"
                  class="filter-item" @change="crud.toQuery"/>
        <rrOperation/>
      </div>
      <crudOperation/>
    </div>
    <!--表格渲染-->
    <el-table ref="table" v-loading="crud.loading" :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              default-expand-all :data="crud.data" row-key="id" @select="crud.selectChange"
              @select-all="crud.selectAllChange" @selection-change="crud.selectionChangeHandler">
      <el-table-column prop="auditCode" label="报审编号" width="120" header-align="center"/>
      <el-table-column prop="projectName" label="项目名称" width="320" header-align="center"/>
      <el-table-column prop="buildOrgName" label="建设单位" min-width="240" header-align="center"/>
      <el-table-column prop="budgetFileName" label="概算文件" min-width="100" header-align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.budgetFileInfo ? scope.row.budgetFileInfo.name : '' }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="buildTotalInvestmentAmount" label="报审金额" min-width="80" header-align="center"
                       align="right">
        <template slot-scope="scope">
          <span>{{ parseDecimal4(scope.row.buildTotalInvestmentAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="resultAmount" label="审定金额" min-width="80" header-align="center" align="right">
        <template slot-scope="scope">
          <span>{{ parseDecimal4(scope.row.resultAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="firstFileName" label="初审结算文件" min-width="200" align="center">
        <template slot-scope="scope">
          <template v-if="scope.row.firstAuditFileInfo">
            <el-popconfirm
              confirm-button-text='预览'
              cancel-button-text='重新上传'
              title="请选择"
              :hide-icon="true"
              @confirm="reviewSettleData(scope.row,3)"
              @cancel="uploadSettleData(scope.row,3)"
            >
              <el-button type="text" size="small" slot="reference">
                {{ scope.row.firstAuditFileInfo.name }}
              </el-button>
            </el-popconfirm>
          </template>
          <template v-else>
            <el-button type="text" size="small" @click="uploadSettleData(scope.row,3)">
              上传
            </el-button>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="secondFileName" label="一审结算文件" min-width="200" align="center">
        <template slot-scope="scope">
          <template v-if="scope.row.secondAuditFileInfo">
            <el-popconfirm
              confirm-button-text='预览'
              cancel-button-text='重新上传'
              title="请选择"
              :hide-icon="true"
              @confirm="reviewSettleData(scope.row,2)"
              @cancel="uploadSettleData(scope.row,2)"
            >
              <el-button type="text" slot="reference" size="small">
                {{ scope.row.secondAuditFileInfo.name }}
              </el-button>
            </el-popconfirm>
          </template>
          <template v-else>
            <el-button type="text" size="small" @click="uploadSettleData(scope.row,2)">
              上传
            </el-button>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="thirdFileName" label="二审结算文件" min-width="200" align="center">
        <template slot-scope="scope">
          <template v-if="scope.row.thirdAuditFileInfo">
            <el-popconfirm
              confirm-button-text='预览'
              cancel-button-text='重新上传'
              title="请选择"
              :hide-icon="true"
              @confirm="reviewSettleData(scope.row,1)"
              @cancel="uploadSettleData(scope.row,1)"
            >
              <el-button type="text" size="small"  slot="reference">{{
                  scope.row.thirdAuditFileInfo.name
                }}
              </el-button>
            </el-popconfirm>
          </template>
          <template v-else>
            <el-button type="text" size="small" @click="uploadSettleData(scope.row,1)">
              上传
            </el-button>
          </template>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="getSettleCompareData(scope.row)">对比详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination/>
    <!--结算上传-->
    <resultAddUpload
      :fileChage="resultSheetChange"
      ref="resultUpload"
      :projectAuditId="selectedRow.id"
      :orgTypeCd="settleDataTye"
      :disabled="false"
      :projectId='selectedRow.projectId'
      :showDialogVisible="showAddUploadVisible"
      @closed-father-dialog="()=> showAddUploadVisible=false"
      :title="title"
      :uploadDataButton="uploadDataButton"
    />
    <!--结算详情-->
    <settledetail
      :result-id='reviewRow.resultId'
      :data-type="settleDataTye"
      :show-dialog="showDetail"
      :show-compare="showCompare"
      @close-compare="closeCompare"
    />
  </div>
</template>

<script>
import crudRequest from './config/request'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import {uploadParams} from "@/utils/upload";
import resultAddUpload from "@/components/AttachmentsUploadFile/resultAddUpload.vue";
import settledetail from "@/views/compare/settle/settledetail.vue";

const formFields = [];
const tableListFields = [];
const defaultForm = [];
export default {
  name: 'simpleList',
  components: {resultAddUpload, crudOperation, rrOperation, udOperation, pagination, settledetail},
  cruds() {
    return CRUD({
      title: '结算', url: '/project/settle', crudMethod: {...crudRequest},
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 设置数据字典
  dicts: ['result_project_status'],
  data() {
    return {
      formFields: formFields,
      tableListFields: tableListFields,
      permission: {
        add: ['admin', 'compare:budget:add'],
        edit: ['admin', 'compare:budget:edit'],
        del: ['admin', 'compare:budget:del']
      },
      title: "结算数据导入",
      selectedRow: {},
      settleDataTye: 0,
      uploadData: {},
      showCompare: false,
      reviewRow: {},
      showDetail: false,
      showAddUploadVisible: false,
    }
  },
  methods: {
    httpRequest(data, row, type) {
      uploadParams('project/settle/upload', data.file, {'auditId': row.id, 'type': type})
        .then(res => {
          let fileInfo = {
            name: data.file.name,
            url: res.data.data.uri,
          }
          switch (type) {
            case 1:
              this.$set(row, 'firstAuditFileInfo', fileInfo)
              break
            case 2:
              this.$set(row, 'secondAuditFileInfo', fileInfo)
              break
            default:
              this.$set(row, 'thirdAuditFileInfo', fileInfo)
          }
        })
        .catch(e => {
          this.$message.error('上传失败！请稍后重试！');
        })
    },
    uploadSettleData(row, type) {
      this.selectedRow = row;
      this.settleDataTye = type;
      this.showAddUploadVisible = true;
    },
    resultSheetChange(fileList, sheetList) {
      this.uploadData = {
        file: fileList[0],
        sheetList: sheetList,
        dataType: this.settleDataTye,
        projectId: this.selectedRow.projectId,
        projectAuditId: this.selectedRow.id,
        projectResultId: this.selectedRow.resultId,
        type: 1
      }
    },
    reviewSettleData(row, type) {
      this.showDetail = true
      this.reviewRow = row
      this.settleDataTye = type
      this.showCompare = false
    },
    getSettleCompareData(row) {
      this.showDetail = true
      this.reviewRow = row
      this.showCompare = true
    },
    uploadDataButton() {
      crudRequest.add(this.uploadData).then(res => {
        this.showAddUploadVisible = false;
        this.$message.success('上传成功');
        this.crud.toQuery()
      })
    },
    closeCompare() {
      this.showDetail = false
    },
  }
}
</script>

