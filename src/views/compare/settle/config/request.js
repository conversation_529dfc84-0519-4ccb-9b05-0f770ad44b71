import request from '@/utils/request'
const requestUrl = '/project/settle';
import qs from 'qs'

export function add(data) {
    return request({
        url: requestUrl+'/addSettleData',
        method: 'post',
        data
    })
}

export function getSettleDetail(resultId,type) {
  return request({
    url: '/compare/result/settleDetail/'+resultId+'/'+type,
    method: 'get',
  })
}


export default {add,getSettleDetail}
