<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.projectName" clearable size="small" placeholder="请输入项目名称" style="width: 240px;"
                  class="filter-item" @keyup.enter.native="crud.toQuery"/>
        <el-date-picker
          v-model="query.createTime"
          :default-time="['00:00:00','23:59:59']"
          type="daterange"
          range-separator=":"
          size="small"
          class="date-item"
          value-format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />

        <rrOperation/>
      </div>
      <crudOperation :permission="permission"/>
    </div>
    <!--表单组件-->
    <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU"
               :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
      <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
        <template v-for="(item, key) in formFields">
          <template v-if="item.type == 'radio'">
            <el-form-item label="状态" :prop="item.fieldName" :key="key">
              <el-radio v-for="item in dict.dept_status" :key="item.id" v-model="form.enabled" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-form-item>
          </template>
          <template v-if="item.type == 'input'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-input v-model="form[item.fieldName]" :style="formInputStyle"/>
            </el-form-item>
          </template>
          <template v-if="item.fieldName ==='projectName'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]" :style="formInputStyle" placeholder="请选择"
                         @change="selectChanged" filterable>
                <el-option
                  v-for="item in projectList"
                  :key="item.id"
                  :label="item.projectName"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-if="item.type == 'upload'">
            <el-form-item label="概算上传">
              <el-upload
                ref="upload"
                action=""
                :limit=1
                :http-request="httpRequest"
                accept=".xls,.xlsx"
                :before-upload="beforeUploadFile"
                :on-error="handleError"
              >
                <el-button size="small" plain>选择文件</el-button>
                <div slot="tip" class="el-upload__tip">只能上传Excel文件，且不超过10M</div>
              </el-upload>
            </el-form-item>
          </template>
          <template v-if="item.fieldName === 'budgetSheetName' ||item.fieldName === 'otherSheetName'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]" :style="formInputStyle" placeholder="请选择">
                <el-option
                  v-for="item in sheetList"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-if="item.type == 'textarea'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-input :type="item.type" v-model="form[item.fieldName]" style="width: 370px;"/>
            </el-form-item>
          </template>
        </template>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>
    <!--表格渲染-->
    <el-table ref="table" v-loading="crud.loading" :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              default-expand-all :data="crud.data" row-key="id" @select="crud.selectChange"
              @select-all="crud.selectAllChange" @selection-change="crud.selectionChangeHandler" border>
      <el-table-column :selectable="checkboxT" type="selection" width="55"/>
      <template v-for="(item, key) in tableListFields">
        <el-table-column :label="item.label" :prop="item.fieldName" :key="key">
          <template v-slot="scope">
            {{ item.fieldName == 'createTime' ? parseTime(scope.row[item.fieldName]) : scope.row[item.fieldName] }}
          </template>
        </el-table-column>
      </template>

      <el-table-column v-permission="['admin','compare:budget:edit','compare:budget:del']" label="操作" width="200px"
                       align="center" fixed="right">
        <template v-slot="scope">
          <el-button type="text" @click="budgetDetailButton(scope.row.projectId)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--概算详情弹窗-->
    <el-dialog title="概算详情" :visible.sync="dialogTableVisible" width="1150px">
      <el-table
        :data="budgetDetailTree"
        style="width: 100%;"
        height="600"
        highlight-current-row
        row-key="id"
        border
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
        <template v-for="(item, key) in tableDialogFields">
          <el-table-column :label="item.label" :prop="item.fieldName" v-if="item.label==='名称'" header-align="center"
                           align="left" :key="key" :width="item.size" :show-overflow-tooltip="true" fixed>
            <template v-slot="scope">
              {{ item.fieldName === 'createTime' ? parseTime(scope.row[item.fieldName]) : scope.row[item.fieldName] }}
            </template>
          </el-table-column>
          <el-table-column v-if="item.label!=='名称'" :label="item.label" align="center" :prop="item.fieldName"
                           :key="key" :width="item.size">
            <template v-slot="scope">
              {{ scope.row[item.fieldName] === '0.0000' ? '' : scope.row[item.fieldName] }}
            </template>
          </el-table-column>
        </template>
      </el-table>
    </el-dialog>

    <!--分页组件-->
    <pagination/>
  </div>
</template>

<script>
import crudRequest from './config/request'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import {upload} from '@/utils/upload'
import {getToken} from '@/utils/auth'
import {getPage} from '../../projectManagement/projectInfo/config/request'
import {getBudgetTree} from './config/request'
//注入配置文件
import config from './config/index'
import configDetail from '../budgetDetail/config/index'

const formFields = config.getFields('form');
const tableListFields = config.getFields('tableList');
const tableDialogFields = configDetail.getFields('tableDialog');
const defaultForm = config.getDefaultForm(formFields);

export default {
  name: 'simpleList',
  components: {crudOperation, rrOperation, udOperation, pagination},
  cruds() {
    return CRUD({
      title: '概算', url: config.requestUrl, crudMethod: {...crudRequest},
      optShow: {
        add: true,
        edit: false,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 设置数据字典
  dicts: ['project_types'],
  data() {
    return {
      formFields: formFields,
      tableListFields: tableListFields,
      tableDialogFields: tableDialogFields,
      rules: {
        projectName: [
          {required: true, message: '请选择项目', trigger: 'blur'}
        ],
        uri: [
          {required: true, message: '请上传概算文件', trigger: 'blur'}
        ],
        budgetSheetName: [
          {required: true, message: '请选择总概算表', trigger: 'blur'}
        ],
        otherSheetName: [
          {required: true, message: '请选择其他费', trigger: 'blur'}
        ],
      },
      headers: {
        xToken: getToken()
      },
      permission: {
        add: ['admin', 'compare:budget:add'],
        edit: ['admin', 'compare:budget:edit'],
        del: ['admin', 'compare:budget:del']
      },
      enabledTypeOptions: [
        {key: 'true', display_name: '正常'},
        {key: 'false', display_name: '禁用'}
      ],
      budgetDetailTree: [],
      dialogTableVisible: false,
      sheetList: [],
      projectList: [],
    }
  },
  methods: {
    checkboxT(row, rowIndex) {
      return row.id !== 1
    },
    // 监听上传失败
    handleError(e, file, fileList) {
      const msg = JSON.parse(e.message)
      this.$notify({
        title: msg.message,
        type: 'error',
        duration: 2500
      })
      this.loading = false
    },
    beforeUploadFile(file) {
      let isLt2M = true
      isLt2M = file.size / 1024 / 1024 < 100
      if (!isLt2M) {
        this.loading = false
        this.$message.error('上传文件大小不能超过 100MB!')
      }
      this.form.name = file.name
      return isLt2M
    },
    // 新增与编辑前做的操作
    [CRUD.HOOK.afterToCU](crud, form) {
      this.clearFile();
      getPage({
        current: 1,
        size: 100
      })
        .then(res => {
          this.projectList = res.records;
        })
    },
    selectChanged(item) {
      this.form.projectId = item.id;
      this.form.projectName = item.projectName;
    },
    //展开详情
    budgetDetailButton(projectId) {
      this.dialogTableVisible = true;
      getBudgetTree(projectId).then(res => {
        this.budgetDetailTree = res;
      })
    },
    clearFile() {
      const mainImg = this.$refs.upload;
      if (mainImg && mainImg.length) {
        mainImg.forEach(item => {
          // item.uploadFiles.length = 0;
          item.clearFiles();
        });
      }
    },
    httpRequest(data) {
      const loading = this.$loading({
        lock: true,
        text: '正在上传，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      upload('compare/budget/upload', data.file)
        .then(res => {
          console.log(res)
          if (res.data.status === 200) {
            this.crud.notify('上传成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
            this.form.fileName = data.file.name;
            this.form.uri = res.data.data.uri;
            this.sheetList = res.data.data.sheetNames;
          } else {
            this.$message.error(res.data.message);
          }
          loading.close();

        })
        .catch(e => {
          loading.close();
          this.$message.error('上传失败！请稍后重试！');
        })
    }
  }

}
</script>

