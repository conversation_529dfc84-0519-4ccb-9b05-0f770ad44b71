import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;
const porjectUrl = config.porjectUrl;

export function getPage(params) {
    return request({
        url: requestUrl,
        method: 'get',
        params
    })
}

export function getListFuzzy(params) {
    return request({
        url: porjectUrl,
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: requestUrl,
        method: 'post',
        data
    })
}

export function del(ids) {
    return request({
        url: requestUrl,
        method: 'delete',
        data: ids
    })
}

export function edit(data) {
    return request({
        url: requestUrl,
        method: 'put',
        data
    })
}

export function getBudgetTree(params) {
    return request({
        url: requestUrl+"/tableDetail/"+params,
        method: 'get'
    })
}

export default { add, edit, del, getPage,getListFuzzy,getBudgetTree }
