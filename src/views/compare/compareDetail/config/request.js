import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;

export function getPage(params) {
    return request({
        url: requestUrl,
        method: 'get',
        params
    })
}

export function getDetail(params) {
    return request({
        url: requestUrl+"/detail/"+params,
        method: 'get',
    })
}

export function getDetailTree(params) {
    return request({
        url: requestUrl+"/tree/"+params,
        method: 'get',
    })
}

export function updateNodeTree(data) {
    return request({
        url: requestUrl+"/updateNodeTree",
        method: 'put',
        data
    })
}

export function exportCompareDetail(data) {
    return request({
        url: requestUrl+"/exportCompareDetail/"+data,
        method: 'get',
        responseType: 'blob'
    })
}

export default { getPage ,getDetail ,getDetailTree,updateNodeTree,exportCompareDetail }
