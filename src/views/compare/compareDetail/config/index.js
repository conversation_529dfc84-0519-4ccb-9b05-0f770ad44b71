export default {
    //获取某类型字段列表
    getFields(type){
        return  this.parames.filter(item=>{
            return item.whereShow.some((citem)=>citem == type)
    })
    },
    //加工页面formData
    getDefaultForm(data){
        var json = {};
        data.map(item=>{
            json[ item.fieldName ] = item.defaultVal;
    })
        return json;
    },
    //页面请求地址
    requestUrl: '/compare/compareDetail',

    //页面字段参数，可根据实际需求扩展
    parames: [
      {
        label: '项目名称',
        fieldName: 'projectName',
        defaultVal: '',
        size:230,
        fixed:true,
        filedFixed:'left',
        type: 'input',
        whereShow: ['tableList']
      },
      {
        label: '节点名称',
        fieldName: 'nodeName',
        defaultVal: '',
        size:230,
        fixed:true,
        type: 'input',
        whereShow: ['tableList','tableDialog']
      },
      {
        label: '概算合计',
        fieldName: 'projectTotalPay',
        defaultVal: '',
        size:"120",
        type: 'input',
        whereShow: ['tableList']
      },
      {
        label: '一审合计',
        fieldName: 'projectTotalPayOne',
        defaultVal: '',
        size:"120",
        type: 'input',
        whereShow: ['tableList']
      },
      {
        label: '一审审减额',
        fieldName: 'projectLessPriceOne',
        defaultVal: '',
        size:"120",
        type: 'input',
        whereShow: ['tableList']
      },
      {
        label: '二审合计',
        fieldName: 'projectTotalPayTwo',
        defaultVal: '',
        size:"120",
        type: 'input',
        whereShow: ['tableList']
      },
      {
        label: '二审审减额',
        fieldName: 'projectLessPriceTwo',
        defaultVal: '',
        size:"120",
        type: 'input',
        whereShow: ['tableList']
      },
      {
        label: '创建时间',
        fieldName: 'createDate',
        defaultVal: '',
        fixed: true,
        type: 'input',
        whereShow: ['tableList']
      },
      {
        label: '概算',
        fieldName: 'nodeName',
        defaultVal: '',
        type: 'input',
        whereShow: ['tableDialog'],
        children:[
          {
            label: '工程量',
            fieldName: 'projectCount',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableDialog']
          },
          {
            label: '设备费',
            fieldName: 'projectEmSubPay',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableDialog']
          },
          {
            label: '主材费',
            fieldName: 'projectEmPay',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableDialog']
          },
          {
            label: '安装费',
            fieldName: 'projectEmInstallPay',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableDialog']
          },
          {
            label: '建筑工程费',
            fieldName: 'projectBuildPay',
            defaultVal: '',
            size:120,
            type: 'input',
            whereShow: ['tableDialog']
          },
          {
            label: '其他费用',
            fieldName: 'projectOtherPay',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableDialog']
          },
          {
            label: '合计',
            fieldName: 'projectTotalPay',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableDialog']
          },
        ]
      },
      {
        label: '结算',
        fieldName: 'projectCountInit',
        defaultVal: '',
        type: 'input',
        whereShow: ['tableDialog'],
        children: [
          {
            label: '结算名称',
            fieldName: 'resultName',
            defaultVal: '',
            size:230,
            type: 'input',
            whereShow: ['tableDialog']
          },
          {
            label: '一审',
            fieldName: 'projectCountOne',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableDialog'],
            children: [
              {
                label: '工程量',
                fieldName: 'projectCountOne',
                defaultVal: '',
                type: 'input',
                whereShow: ['tableDialog']
              },
              {
                label: '设备费',
                fieldName: 'projectEmSubPayOne',
                defaultVal: '',
                type: 'input',
                whereShow: ['tableDialog']
              },
              {
                label: '安装费',
                fieldName: 'projectEmInstallPayOne',
                defaultVal: '',
                type: 'input',
                whereShow: ['tableDialog']
              },
              {
                label: '建筑工程费',
                fieldName: 'projectBuildPayOne',
                defaultVal: '',
                size:120,
                type: 'input',
                whereShow: ['tableDialog']
              },
              {
                label: '其他费用',
                fieldName: 'projectOtherPayOne',
                defaultVal: '',
                type: 'input',
                whereShow: ['tableDialog']
              },
              {
                label: '合计',
                fieldName: 'projectTotalPayOne',
                defaultVal: '',
                type: 'input',
                whereShow: ['tableDialog']
              },
              {
                label: '审减额',
                fieldName: 'projectLessPriceOne',
                defaultVal: '',
                type: 'input',
                whereShow: ['tableDialog']
              },
            ]
          },
          {
            label: '二审',
            fieldName: 'projectCountTwo',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableDialog'],
            children: [
              {
                label: '工程量',
                fieldName: 'projectCountTwo',
                defaultVal: '',
                type: 'input',
                whereShow: ['tableDialog']
              },
              {
                label: '设备费',
                fieldName: 'projectEmSubPayTwo',
                defaultVal: '',
                type: 'input',
                whereShow: ['tableDialog']
              },
              {
                label: '安装费',
                fieldName: 'projectEmInstallPayTwo',
                defaultVal: '',
                type: 'input',
                whereShow: ['tableDialog']
              },
              {
                label: '建筑工程费',
                fieldName: 'projectBuildPayTwo',
                defaultVal: '',
                size:120,
                type: 'input',
                whereShow: ['tableDialog']
              },
              {
                label: '其他费用',
                fieldName: 'projectOtherPayTwo',
                defaultVal: '',
                type: 'input',
                whereShow: ['tableDialog']
              },
              {
                label: '合计',
                fieldName: 'projectTotalPayTwo',
                defaultVal: '',
                type: 'input',
                whereShow: ['tableDialog']
              },
              {
                label: '审减额',
                fieldName: 'projectLessPriceTwo',
                defaultVal: '',
                type: 'input',
                whereShow: ['tableDialog']
              },
            ]
          }
        ]
      },

    ]
}
