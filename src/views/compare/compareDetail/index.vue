<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.projectName" clearable size="small" placeholder="请输入项目名称" style="width: 200px;"
                  class="filter-item" @keyup.enter.native="crud.toQuery"/>
        <el-date-picker
          @change="crud.toQuery"
          v-model="query.createTime"
          :default-time="['00:00:00','23:59:59']"
          type="daterange"
          range-separator=":"
          size="small"
          class="date-item"
          value-format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
        <rrOperation/>
      </div>
      <crudOperation :permission="permission"/>
    </div>
    <!--表格渲染-->
    <el-table ref="table" v-loading="crud.loading" :tree-props="{children: 'children', hasChildren: 'hasChildren'}" lazy
              :data="crud.data" row-key="id" @select="crud.selectChange" @select-all="crud.selectAllChange"
              @selection-change="crud.selectionChangeHandler" border>
      <el-table-column :selectable="checkboxT" type="selection" width="55"/>
      <template v-for="(item, key) in tableListFields">
        <el-table-column :label="item.label" :prop="item.fieldName" :key="key" :show-overflow-tooltip="item.fixed" :fixed="item.filedFixed"
                         :min-width="item.size">
          <template v-slot="scope">
            {{ scope.row[item.fieldName] == '0.0000' ? '' : scope.row[item.fieldName] }}
          </template>
        </el-table-column>
      </template>
      <el-table-column label="操作" width="200px" align="center" fixed="right">
        <template v-slot="scope">
          <el-button v-if="scope.row.parentId.trim()=='-1'" type="text" @click="compareDetailButton((scope.row))">
            查看详情
          </el-button>
          <el-button v-if="scope.row.parentId.trim()=='-1'" type="text" @click="openTreeButton(scope.row)">手动归集
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 详情弹窗 -->
    <el-dialog v-dialogDrag append-to-body :close-on-click-modal="false" title="对比详情"
               :visible.sync="tableDialog.dialogTableVisible" width="1200px">
      <el-button size="mini" type="primary" style="margin-bottom: 10px" round @click="exportCompareDetail">导出Excel
      </el-button>
      <el-table
        class="compareDetailTableClass"
        :data="compareDetailTable"
        style="width: 100%;"
        height="600"
        row-key="id"
        :expand-row-keys="openKeys"
        :row-class-name="tableRowClassName"
        border
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
        <template slot="empty">
          <p>{{ dataText }}</p>
        </template>
        <template v-for="(item, key) in tableDialogFields">
          <el-table-column :label="item.label" :prop="item.fieldName" align="left" header-align="center" width="200px"
                           :key="key" :show-overflow-tooltip="true" :fixed="item.fixed">
            <template v-for="(item, key) in item.children">
              <el-table-column :label="item.label" :prop="item.fieldName" align="center" :key="key" width="90%"
                               :show-overflow-tooltip="true">
                <template v-for="(item, key) in item.children">
                  <el-table-column :label="item.label" :prop="item.fieldName" align="center" :key="key"
                                   :width="item.size" :show-overflow-tooltip="true">
                    <template v-slot="scope">
                      {{ scope.row[item.fieldName] }}
                    </template>
                  </el-table-column>
                </template>
              </el-table-column>
            </template>
          </el-table-column>
        </template>
      </el-table>
    </el-dialog>

    <CollectionDataView
      :projectId="resultEntity.projectId"
      :resultId="resultEntity.id"
      :collectionDataVisible="collectionDataVisible"
      :closeDialog="closeDialog"
    />
    <!--分页组件-->
    <pagination/>
  </div>
</template>

<script>
import CollectionDataView from '@/components/CollectionDataView'
import crudRequest from './config/request'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import {getToken} from '@/utils/auth'
import {getDetail, exportCompareDetail, getDetailTree} from './config/request'
import {updateNodeTree} from './config/request'
import {downloadFile} from '@/utils/index'
//注入配置文件
import config from './config/index'

const formFields = config.getFields('form');
const tableListFields = config.getFields('tableList');
const tableDialogFields = config.getFields('tableDialog');
const defaultForm = config.getDefaultForm(formFields);
export default {
  name: 'simpleList',
  components: {crudOperation, rrOperation, udOperation, pagination, CollectionDataView},
  cruds() {
    return CRUD({
      title: '概结算对比', url: config.requestUrl,
      crudMethod: {...crudRequest},
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: true
      }
    })
  },

  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 设置数据字典
  dicts: ['project_types'],
  data() {
    return {
      headerStyle: {
        // 'height':'10px',
        // 'width':'300px'
      },
      formFields: formFields,
      tableListFields: tableListFields,
      tableDialogFields: tableDialogFields,
      rules: {
        name: [
          {required: true, message: '请输入名称', trigger: 'blur'}
        ]
      },
      permission: {
        add: ['admin', 'compare:budget:add'],
        edit: ['admin', 'compare:budget:edit'],
        del: ['admin', 'compare:budget:del']
      },
      defaultProps: {
        label: 'nodeName',
        children: 'children'
      },
      enabledTypeOptions: [
        {key: 'true', display_name: '正常'},
        {key: 'false', display_name: '禁用'}
      ],
      compareDetailTable: [],
      tableDialog: {
        dialogTableVisible: false,
        projectEntity: ''
      },
      openKeys: [],
      dataText: '数据加载中',
      resultEntity: {},
      collectionDataVisible: false,
    }
  },
  methods: {
    checkboxT(row, rowIndex) {
      return row.id !== 1
    },
    handleSuccess(response, file, fileList) {
      this.crud.notify('上传成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      this.form.fileName = file.name;
      this.form.uri = response.data.uri;
      this.sheetList = response.data.sheetNames;
    },
    // 监听上传失败
    handleError(e, file, fileList) {
      const msg = JSON.parse(e.message)
      this.$notify({
        title: msg.message,
        type: 'error',
        duration: 2500
      })
      this.loading = false
    },
    beforeUploadFile(file) {
      let isLt2M = true
      isLt2M = file.size / 1024 / 1024 < 100
      if (!isLt2M) {
        this.loading = false
        this.$message.error('上传文件大小不能超过 100MB!')
      }
      this.form.name = file.name
      return isLt2M
    },
    selectChanged(projectName) {
      this.form.projectName = projectName;
    },
    //展开详情
    compareDetailButton(row) {
      this.tableDialog.dialogTableVisible = true;
      this.tableDialog.projectEntity = row;
      getDetail(row.projectId)
        .then(res => {
          if (res.length > 0) {
            this.compareDetailTable = res;
            this.openKeys.push(res[0].id);
          } else {
            this.dataText = '暂无数据';
          }

        })
    },
    //手动归集弹窗
    openTreeButton(data) {
      this.collectionDataVisible = true;
      this.resultEntity = data;
    },
    //导出项目
    exportCompareDetail() {
      exportCompareDetail(this.tableDialog.projectEntity.projectId)
        .then(res => {
          downloadFile(res, this.tableDialog.projectEntity.projectName + '对比详情.xls')
        })
    },
    //设置行颜色
    tableRowClassName({row, rowIndex}) {
      if (row.itemType === 2) {
        if (row.nodeStatus === 1) {
          return 'success-row';
        }
        return 'warning-row';
      }
      return '';
    },
    closeDialog() {
      this.collectionDataVisible = false;
    },
  }
}
</script>

<style lang="scss" scoped>
.compareDetailTableClass >>> {
  .el-table__header tr,
  .el-table__header th {
    padding: 0;
    height: 10px;
  }

  .compareDetailTableClass
  .el-table__body tr,
  .el-table__body td {
    padding: 0;
    height: 15px;
  }
}
</style>
<style>

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  color: green;
}

.custom-tree-node-child {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  color: orangered;
}

.el-table .warning-row {
  background: rgba(255, 220, 211, 0.99);
}

.el-table .success-row {
  background: #e2f8d5;
}
</style>
