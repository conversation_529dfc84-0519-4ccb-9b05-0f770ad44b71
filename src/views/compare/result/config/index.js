export default {
    //获取某类型字段列表
    getFields(type){
        return  this.parames.filter(item=>{
            return item.whereShow.some((citem)=>citem == type)
    })
    },
    //加工页面formData
    getDefaultForm(data){
        var json = {};
        data.map(item=>{
            json[ item.fieldName ] = item.defaultVal;
    })
        return json;
    },
    //页面请求地址
    requestUrl: '/compare/result',

    //页面字段参数，可根据实际需求扩展
    parames: [

        {
            label: '项目名称',
            fieldName: 'projectName',
            defaultVal: '',
            type: 'select',
            whereShow: ['tableList','form', 'add', 'edit']
        },

        {
            label: '归集方式',
            fieldName: 'autoType',
            defaultVal: "1",
            type: 'radio',
            whereShow: ['form', 'add', 'edit']
        },
        {
            label: '初审文件名称',
            fieldName: 'initUri',
            defaultVal: '',
            type: 'upload',
            whereShow: [ 'form', 'add', 'edit']
        },
        {
            label: '文件名称',
            fieldName: 'initFileName',
            defaultVal: '',
            type: 'upload',
            whereShow: [ 'tableList']
        },
        {
            label: '状态',
            fieldName: 'projectStatus',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '归集节点',
            fieldName: 'parentId',
            defaultVal: '',
            type: 'input',
            whereShow: ['form', 'add', 'edit']
        },
        {
            label: '创建人',
            fieldName: 'createUserName',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '创建时间',
            fieldName: 'createDate',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '分线',
            type: 'line',
            whereShow: ['form']
        },
        {
            label: '上传附件信息',
            fieldName: 'accessoryFile',
            defaultVal: '',
            type: 'uploadTable',
            whereShow: ['form','add', 'edit']
        }

    ]
}
