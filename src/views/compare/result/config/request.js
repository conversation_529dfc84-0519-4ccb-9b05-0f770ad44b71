import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;

export function getPage(params) {
    return request({
        url: requestUrl,
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: requestUrl,
        method: 'post',
        data
    })
}

export function del(ids) {
    return request({
        url: requestUrl,
        method: 'delete',
        data: ids
    })
}

export function edit(data) {
    return request({
        url: requestUrl,
        method: 'put',
        data
    })
}

export function getResultDetailTable(data) {
    return request({
        url: requestUrl+"/table/"+data,
        method: 'get',
    })
}

export function resultFileDetail(params) {
  return request({
    url: requestUrl+"/resultFileDetail",
    method: 'get',
    params
  })
}

export function getResultDetailByResultId(params) {
  return request({
    url: requestUrl+"/resultDetailByResultId/"+params,
    method: 'get',
    params
  })
}

export function getSummary(params) {
  return request({
    url: requestUrl+"/summary",
    method: 'get',
    params
  })
}

export default { add, edit, del, getPage,getResultDetailTable,getSummary}
