<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.projectName" clearable size="small" placeholder="请输入项目名称" style="width: 200px;"
                  class="filter-item" @keyup.enter.native="crud.toQuery"/>
        <el-date-picker
          v-model="query.createTime"
          :default-time="['00:00:00','23:59:59']"
          type="daterange"
          range-separator=":"
          size="small"
          class="date-item"
          value-format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
        <rrOperation/>
      </div>
      <crudOperation :permission="permission"/>
    </div>
    <!--表格渲染-->
    <el-table ref="table" v-loading="crud.loading" :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              default-expand-all :data="crud.data" row-key="id" @select="crud.selectChange"
              @select-all="crud.selectAllChange" @selection-change="crud.selectionChangeHandler">
      <el-table-column :selectable="checkboxT" type="selection" width="55"/>
      <template v-for="(item, key) in tableListFields">
        <el-table-column :label="item.label" :prop="item.fieldName" :key="key">
          <template slot-scope="scope">
            {{
              item.fieldName == 'projectStatus' ? showCurrentData(dict.result_project_status, scope.row[item.fieldName]) : scope.row[item.fieldName]
            }}
          </template>
        </el-table-column>
      </template>
      <el-table-column v-permission="['admin','compare:result:edit','compare:result:del']" label="操作" width="200px"
                       fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="resultDetailButton(scope.row.id)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--结算详情弹窗-->
    <settledetail
      :result-id='resultId'
      :show-dialog="showDetail"
      :show-compare="true"
      @close-compare="closeCompare"
    />
    <!--分页组件-->
    <pagination/>
  </div>
</template>

<script>
import crudRequest from './config/request'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
//注入配置文件
import config from './config/index'
import settledetail from "@/views/compare/settle/settledetail.vue";

const formFields = config.getFields('form');
const tableListFields = config.getFields('tableList');
const defaultForm = config.getDefaultForm(formFields);
export default {
  name: 'simpleList',
  components: {settledetail, crudOperation, rrOperation, udOperation, pagination},
  cruds() {
    return CRUD({
      title: '结算', url: config.requestUrl, crudMethod: {...crudRequest},
      optShow: {
        add: false,
        edit: false,
        del: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 设置数据字典
  dicts: ['result_project_status'],
  data() {
    return {
      formFields: formFields,
      rules: {},
      tableListFields: tableListFields,
      permission: {
        add: ['admin', 'compare:budget:add'],
        edit: ['admin', 'compare:budget:edit'],
        del: ['admin', 'compare:budget:del']
      },
      showDetail: false,
      resultId: ''
    }
  },
  methods: {
    checkboxT(row, rowIndex) {
      return row.id !== 1
    },
    //删除之前的勾子
    [CRUD.HOOK.beforeDelete](crud, form) {
      for (let val of form) {
        if (val.projectStatus === 1 || val.projectStatus === 2) {
          this.$message.error('非初始状态无法删除')
          crud.delAllLoading = false;
          crud.cancel();
        }
      }
    },
    resultDetailButton(resultId) {
      this.resultId=resultId;
      this.showDetail = true;
    },

    closeCompare() {
      this.showDetail = false;
    }
  }
}
</script>

