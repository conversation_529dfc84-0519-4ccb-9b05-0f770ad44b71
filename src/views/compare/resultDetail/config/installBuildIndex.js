import da from "element-ui/src/locale/lang/da";

export default {
  //获取某类型字段列表
  getFields(type) {
    let arr=[];
    this.parames.forEach((value) => {
      if (value.whereShow.some((item) => item === type)){
        let data=JSON.parse(JSON.stringify(value));
        if (value.children){
          data.children=[];
          value.children.forEach(item=>{
            if (item.whereShow.some((item) => item === type)){
              data.children.push(item);
            }
          })
        }
        arr.push(data)
      }
    })
    return arr;
  },
  //加工页面formData
  getDefaultForm(data) {
    var json = {};
    data.map(item => {
      json[item.fieldName] = item.defaultVal;
    })
    return json;
  },
  //页面请求地址
  requestUrl: '/compare/resultDetail',

  //页面字段参数，可根据实际需求扩展
  parames: [
    {
      label: '名称',
      fieldName: 'name',
      defaultVal: '',
      size:'200',
      fixed:true,
      type: 'input',
      whereShow: ['tableInstallDialog','tableBuildDialog']
    },

    {
      label: '初审',
      fieldName: 'nodeStatus',
      defaultVal: '',
      isFather:true,
      type: 'input',
      whereShow: ['tableInstallDialog','tableBuildDialog'],
      children:[
        {
          label: '单位',
          fieldName: 'unit',
          defaultVal: '',
          type: 'input',
          whereShow: ['tableInstallDialog','tableBuildDialog']
        },
        {
          label: '工程量',
          fieldName: 'projectCount',
          defaultVal: '',
          type: 'input',
          whereShow: ['tableInstallDialog','tableBuildDialog']
        },
        {
          label: '备注',
          fieldName: 'remark',
          defaultVal: '',
          type: 'input',
          whereShow: ['tableInstallDialog','tableBuildDialog']
        },
        {
          label: '送审金额',
          fieldName: 'applyAmount',
          defaultVal: '',
          type: 'input',
          whereShow: ['tableInstallDialog','tableBuildDialog']
        },
        {
          label: '审定金额',
          fieldName: 'auditAmount',
          defaultVal: '',
          type: 'input',
          whereShow: ['tableInstallDialog','tableBuildDialog']
        },

      ]
    },
    {
      label: '一审',
      fieldName: 'projectCountOne',
      defaultVal: '',
      isFather:true,
      type: 'input',
      whereShow: ['tableInstallDialog','tableBuildDialog'],
      children: [
        {
          label: '审定金额',
          fieldName: 'auditAmountOne',
          defaultVal: '',
          type: 'input',
          whereShow: ['tableInstallDialog','tableBuildDialog']
        },
      ]
    },
    {
      label: '二审',
      fieldName: 'projectCountTwo',
      defaultVal: '',
      isFather:true,
      type: 'input',
      whereShow: ['tableInstallDialog','tableBuildDialog',],
      children: [
        {
          label: '审定金额',
          fieldName: 'auditAmountTwo',
          defaultVal: '',
          type: 'input',
          whereShow: ['tableInstallDialog','tableBuildDialog']
        },
      ]
    },
  ]
}
