import da from "element-ui/src/locale/lang/da";

export default {
  //获取某类型字段列表
  getFields(type) {
    let arr=[];
    this.parames.forEach((value) => {
      if (value.whereShow.some((item) => item === type)){
        let data=JSON.parse(JSON.stringify(value));
        if (value.children){
          data.children=[];
          value.children.forEach(item=>{
            if (item.whereShow.some((item) => item === type)){
              data.children.push(item);
            }
          })
        }
        arr.push(data)
      }
    })
    return arr;
  },
  //加工页面formData
  getDefaultForm(data) {
    var json = {};
    data.map(item => {
      json[item.fieldName] = item.defaultVal;
    })
    return json;
  },
  //页面请求地址
  requestUrl: '/compare/resultDetail',

  //页面字段参数，可根据实际需求扩展
  parames: [
    {
      label: '主键ID',
      fieldName: 'id',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'edit']
    },

    {
      label: '名称',
      fieldName: 'resultNodeName',
      defaultVal: '',
      size:'200',
      fixed:true,
      type: 'input',
      whereShow: ['tableInstallDialog','tableBuildDialog','tabOtherDialog']
    },

    {
      label: '初审',
      fieldName: 'nodeStatus',
      defaultVal: '',
      isFather:true,
      type: 'input',
      whereShow: ['tableInstallDialog','tableBuildDialog','tabOtherDialog'],
      children:[
        {
          label: '工程量',
          fieldName: 'projectCountInit',
          defaultVal: '',
          type: 'input',
          whereShow: ['tableInstallDialog','tableBuildDialog']
        },
        {
          label: '设备费',
          fieldName: 'projectEmSubPayInit',
          defaultVal: '',
          type: 'input',
          whereShow: ['']
        },
        {
          label: '主材费',
          fieldName: 'projectEmPayInit',
          defaultVal: '',
          type: 'input',
          whereShow: ['']
        },
        {
          label: '安装费',
          fieldName: 'projectEmInstallPayInit',
          defaultVal: '',
          type: 'input',
          whereShow: ['tableInstallDialog']
        },
        {
          label: '建筑工程费',
          fieldName: 'projectBuildPayInit',
          defaultVal: '',
          size:120,
          type: 'input',
          whereShow: ['tableBuildDialog']
        },
        {
          label: '其他费用',
          fieldName: 'projectOtherPayInit',
          defaultVal: '',
          type: 'input',
          whereShow: ['tabOtherDialog']
        },
        {
          label: '合计',
          fieldName: 'projectTotalPayInit',
          defaultVal: '',
          type: 'input',
          whereShow: []
        },
        {
          label: '费用计取依据',
          fieldName: 'projectOtherBasis',
          defaultVal: '',
          type: 'input',
          whereShow: ['tabOtherDialog']
        },
        {
          label: '其他费说明',
          fieldName: 'projectOtherInfo',
          defaultVal: '',
          type: 'input',
          whereShow: ['tabOtherDialog']
        },
      ]
    },
    {
      label: '一审',
      fieldName: 'projectCountOne',
      defaultVal: '',
      isFather:true,
      type: 'input',
      whereShow: ['tableInstallDialog','tableBuildDialog','tabOtherDialog'],
      children: [
        {
          label: '工程量',
          fieldName: 'projectCountOne',
          defaultVal: '',
          type: 'input',
          whereShow: ['tableInstallDialog','tableBuildDialog']
        },
        {
          label: '设备费',
          fieldName: 'projectEmSubPayOne',
          defaultVal: '',
          type: 'input',
          whereShow: ['']
        },
        {
          label: '主材费',
          fieldName: 'projectEmPayOne',
          defaultVal: '',
          type: 'input',
          whereShow: ['']
        },
        {
          label: '安装费',
          fieldName: 'projectEmInstallPayOne',
          defaultVal: '',
          type: 'input',
          whereShow: ['tableInstallDialog']
        },
        {
          label: '建筑工程费',
          fieldName: 'projectBuildPayOne',
          defaultVal: '',
          size:120,
          type: 'input',
          whereShow: ['tableBuildDialog']
        },
        {
          label: '其他费用',
          fieldName: 'projectOtherPayOne',
          defaultVal: '',
          type: 'input',
          whereShow: ['tabOtherDialog']
        },
        {
          label: '合计',
          fieldName: 'projectTotalPayOne',
          defaultVal: '',
          type: 'input',
          whereShow: []
        },
        {
          label: '审减额',
          fieldName: 'projectLessPriceOne',
          defaultVal: '',
          type: 'input',
          whereShow: []
        },
      ]
    },
    {
      label: '二审',
      fieldName: 'projectCountTwo',
      defaultVal: '',
      isFather:true,
      type: 'input',
      whereShow: ['tableInstallDialog','tableBuildDialog',,'tabOtherDialog'],
      children: [
        {
          label: '工程量',
          fieldName: 'projectCountTwo',
          defaultVal: '',
          type: 'input',
          whereShow: ['tableInstallDialog','tableBuildDialog']
        },
        {
          label: '设备费',
          fieldName: 'projectEmSubPayTwo',
          defaultVal: '',
          type: 'input',
          whereShow: ['']
        },
        {
          label: '主材费',
          fieldName: 'projectEmPayTwo',
          defaultVal: '',
          type: 'input',
          whereShow: ['']
        },
        {
          label: '安装费',
          fieldName: 'projectEmInstallPayTwo',
          defaultVal: '',
          type: 'input',
          whereShow: ['tableInstallDialog']
        },
        {
          label: '建筑工程费',
          fieldName: 'projectBuildPayTwo',
          defaultVal: '',
          size:120,
          type: 'input',
          whereShow: ['tableBuildDialog']
        },
        {
          label: '其他费用',
          fieldName: 'projectOtherPayTwo',
          defaultVal: '',
          type: 'input',
          whereShow: ['tabOtherDialog']
        },
        {
          label: '合计',
          fieldName: 'projectTotalPayTwo',
          defaultVal: '',
          type: 'input',
          whereShow: []
        },
        {
          label: '审减额',
          fieldName: 'projectLessPriceTwo',
          defaultVal: '',
          type: 'input',
          whereShow: []
        },
      ]
    },

    {
      label: '排序',
      fieldName: 'sort',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '创建用户姓名',
      fieldName: 'createUserName',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '创建时间',
      fieldName: 'createDate',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '更新用户姓名',
      fieldName: 'updateUserName',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '更新时间',
      fieldName: 'updateDate',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '数据版本',
      fieldName: 'dataVersion',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'edit']
    },
  ]
}
