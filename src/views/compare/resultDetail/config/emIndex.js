export default {
  //获取某类型字段列表
  getFields(type) {
    let arr=[];
    this.parames.forEach((value) => {
      if (value.whereShow.some((item) => item === type)){
        let data=JSON.parse(JSON.stringify(value));
        if (value.children){
          data.children=[];
          value.children.forEach(item=>{
            if (item.whereShow.some((item) => item === type)){
              data.children.push(item);
            }
          })
        }
        arr.push(data)
      }
    })
    return arr;
  },
  //加工页面formData
  getDefaultForm(data){
    var json = {};
    data.map(item=>{
      json[ item.fieldName ] = item.defaultVal;
    })
    return json;
  },
  //页面请求地址
  requestUrl: '/compare/emDetail',

  //页面字段参数，可根据实际需求扩展
  parames: [

    {
      label: '设备、材料名称规格',
      fieldName: 'emName',
      defaultVal: '',
      size:200,
      type: 'input',
      whereShow: ['firstList','twoList', 'form', 'add', 'edit']
    },
    {
      label: '初审',
      fieldName: 'nodeStatus',
      defaultVal: '',
      isFather: true,
      type: 'input',
      whereShow: ['firstList', 'twoList'],
      children: [
        {
          label: '单位',
          fieldName: 'unit',
          defaultVal: '',
          type: 'input',
          whereShow: ['firstList', 'twoList', 'form', 'add', 'edit']
        },
        {
          label: '数量',
          fieldName: 'count',
          defaultVal: '',
          type: 'input',
          whereShow: ['firstList', 'twoList', 'form', 'add', 'edit']
        },
        {
          label: '购置单价',
          fieldName: 'emBuyPay',
          defaultVal: '',
          type: 'input',
          whereShow: ['firstList', 'twoList', 'form', 'add', 'edit']
        },

        {
          label: '使用量',
          fieldName: 'emUsedCount',
          defaultVal: '',
          type: 'input',
          whereShow: ['firstList', 'form', 'add', 'edit']
        },
        {
          label: '退款量',
          fieldName: 'emReturnCount',
          defaultVal: '',
          type: 'input',
          whereShow: ['firstList', 'form', 'add', 'edit']
        },
        {
          label: '差额',
          fieldName: 'emDifference',
          defaultVal: '',
          type: 'input',
          whereShow: ['firstList', 'form', 'add', 'edit']
        },
        {
          label: '合价',
          fieldName: 'emTotalPay',
          defaultVal: '',
          type: 'input',
          whereShow: ['firstList', 'twoList', 'form', 'add', 'edit']
        },
      ]
    },
    {
      label: '一审',
      fieldName: 'nodeStatus',
      defaultVal: '',
      isFather:true,
      type: 'input',
      whereShow: ['firstList','twoList'],
      children:[
        {
          label: '合价',
          fieldName: 'emTotalPayOne',
          defaultVal: '',
          type: 'input',
          whereShow: ['firstList', 'twoList','form', 'add', 'edit']
        },
      ]
    },
    {
      label: '二审',
      fieldName: 'nodeStatus',
      defaultVal: '',
      isFather:true,
      type: 'input',
      whereShow: ['firstList','twoList'],
      children:[
        {
          label: '合价',
          fieldName: 'emTotalPayTwo',
          defaultVal: '',
          type: 'input',
          whereShow: ['firstList', 'twoList','form', 'add', 'edit']
        },
      ]
    }
  ]
}
