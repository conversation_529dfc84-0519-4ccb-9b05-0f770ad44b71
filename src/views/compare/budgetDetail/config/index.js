export default {
    //获取某类型字段列表
    getFields(type){
        return  this.parames.filter(item=>{
            return item.whereShow.some((citem)=>citem == type)
    })
    },
    //加工页面formData
    getDefaultForm(data){
        var json = {};
        data.map(item=>{
            json[ item.fieldName ] = item.defaultVal;
    })
        return json;
    },
    //页面请求地址
    requestUrl: '/compare/budgetDetail',

    //页面字段参数，可根据实际需求扩展
    parames: [
        {
            label: '主键ID',
            fieldName: 'id',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'edit']
        },
        {
            label: '概算id',
            fieldName: 'projectBudgetId',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '项目ID',
            fieldName: 'projectId',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '序号',
            fieldName: 'nodeNo',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '排序',
            fieldName: 'sort',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '名称',
            fieldName: 'nodeName',
            defaultVal: '',
            size:"250",
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit','tableDialog']
        },
        {
            label: '创建用户ID',
            fieldName: 'parentId',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '工程量',
            fieldName: 'projectCount',
            defaultVal: '',
            size:"120",
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit','tableDialog']
        },
        {
            label: '设备费',
            fieldName: 'projectEmSubPay',
            defaultVal: '',
            size:"120",
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit','tableDialog']
        },
        {
            label: '主材费',
            fieldName: 'projectEmPay',
            defaultVal: '',
            size:"120",
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit','tableDialog']
        },
        {
            label: '安装费',
            fieldName: 'projectEmInstallPay',
            defaultVal: '',
            size:"120",
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit','tableDialog']
        },
        {
            label: '建筑工程费',
            fieldName: 'projectBuildPay',
            defaultVal: '',
            size:"120",
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit','tableDialog']
        },
        {
            label: '其他费用',
            fieldName: 'projectOtherPay',
            defaultVal: '',
            size:"120",
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit','tableDialog']
        },
        {
            label: '合计',
            fieldName: 'projectTotalPay',
            defaultVal: '',
            size:"120",
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit','tableDialog']
        },
      {
        label: '工程量',
        fieldName: 'projectCountInit',
        defaultVal: '',
        size:"120",
        type: 'input',
        whereShow: ['tableList', 'form', 'add', 'edit']
      },
      {
        label: '设备费',
        fieldName: 'projectEmSubPayInit',
        defaultVal: '',
        size:"120",
        type: 'input',
        whereShow: ['tableList', 'form', 'add', 'edit']
      },
      {
        label: '主材费',
        fieldName: 'projectEmPayInit',
        defaultVal: '',
        size:"120",
        type: 'input',
        whereShow: ['tableList', 'form', 'add', 'edit']
      },
      {
        label: '安装费',
        fieldName: 'projectEmInstallPayInit',
        defaultVal: '',
        size:"120",
        type: 'input',
        whereShow: ['tableList', 'form', 'add', 'edit']
      },
      {
        label: '建筑工程费',
        fieldName: 'projectBuildPayInit',
        defaultVal: '',
        size:"120",
        type: 'input',
        whereShow: ['tableList', 'form', 'add', 'edit']
      },
      {
        label: '其他费用',
        fieldName: 'projectOtherPayInit',
        defaultVal: '',
        size:"120",
        type: 'input',
        whereShow: ['tableList', 'form', 'add', 'edit']
      },
      {
        label: '合计',
        fieldName: 'projectTotalPayInit',
        defaultVal: '',
        size:"120",
        type: 'input',
        whereShow: ['tableList', 'form', 'add', 'edit']
      },
        {
            label: '结算工程量(一审)',
            fieldName: 'projectCountOne',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '结算设备费(一审)',
            fieldName: 'projectEmSubPayOne',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '结算主材费(一审)',
            fieldName: 'projectEmPayOne',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '结算安装费(一审)',
            fieldName: 'projectEmInstallPayOne',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '结算建筑工程费(一审)',
            fieldName: 'projectBuildPayOne',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '结算其他费用(一审)',
            fieldName: 'projectOtherPayOne',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '结算合计(一审)',
            fieldName: 'projectTotalPayOne',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '结算工程量',
            fieldName: 'projectCount',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '结算设备费',
            fieldName: 'projectEmSubPay',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '结算主材费',
            fieldName: 'projectEmPay',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '结算安装费',
            fieldName: 'projectEmInstallPay',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '结算建筑工程费',
            fieldName: 'projectBuildPay',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '结算其他费用',
            fieldName: 'projectOtherPay',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '结算合计',
            fieldName: 'projectTotalPay',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '创建用户姓名',
            fieldName: 'createUserName',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '创建时间',
            fieldName: 'createDate',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '更新用户姓名',
            fieldName: 'updateUserName',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '更新时间',
            fieldName: 'updateDate',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '数据版本',
            fieldName: 'dataVersion',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'edit']
        },
    ]
}
