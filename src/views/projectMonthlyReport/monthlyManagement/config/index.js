export default {
  //获取某类型字段列表
  getFields(type) {
    return this.parames.filter(item => {
      return item.whereShow.some((citem) => citem == type)
    })
  },
  //加工页面formData
  getDefaultForm(data) {
    var json = {};
    data.map(item => {
      json[item.fieldName] = item.defaultVal;
    })
    return json;
  },
  //页面请求地址
  requestUrl: '/monthly/projectMonthlyReport',

  //页面字段参数，可根据实际需求扩展
  parames: [
    {
      label: '序号',
      fieldName: 'code',
      defaultVal: '',
      type: 'input',
      visible: true,
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '名称',
      fieldName: 'projectName',
      defaultVal: '',
      type: 'input',
      visible: true,
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '项目类型',
      fieldName: 'projectType',
      defaultVal: '',
      type: 'input',
      visible: true,
      whereShow: ['tableList', 'form', 'add', 'edit']
    },

    {
      label: '结算审核状态',
      fieldName: 'approvalStatus',
      defaultVal: '',
      type: 'input',
      visible: true,
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '建设单位',
      fieldName: 'buildOrgName',
      defaultVal: '',
      type: 'input',
      visible: true,
      whereShow: ['tableList']
    },
    {
      label: '概算金额',
      fieldName: 'estimateAmount',
      defaultVal: '',
      type: 'input',
      visible: true,
      whereShow: ['tableList']
    },
    {
      label: '累计下达计划金额',
      fieldName: 'plannedInvestmentAmount',
      defaultVal: '',
      type: 'input',
      visible: true,
      whereShow: ['tableList']
    },
    {
      label: '项目进展情况',
      fieldName: 'progress',
      defaultVal: '',
      type: 'input',
      visible: true,
      whereShow: ['tableList']
    },
    {
      label: '完成项目总进度',
      fieldName: 'overallProgress',
      defaultVal: '',
      type: 'input',
      visible:true,
      whereShow: ['tableList']
    },
    {
      label: '项目完工时间',
      fieldName: 'scheduledCompletionTime',
      defaultVal: '',
      type: 'input',
      visible:true,
      whereShow: ['tableList']
    },
    {
      label: '省级公司结算审核',
      fieldName: 'provincialCompanySettlement',
      defaultVal: '',
      type: 'input',
      visible:true,
      whereShow: ['tableList']
    },
    {
      label: '上报结算金额',
      fieldName: 'reportedSettlementAmount',
      defaultVal: '',
      type: 'input',
      visible:true,
      whereShow: ['tableList']
    },
    {
      label: '审定结算金额',
      fieldName: 'approvedSettlementAmount',
      defaultVal: '',
      type: 'input',
      visible:true,
      whereShow: ['tableList']
    },
    {
      label: '预计结算上报时间',
      fieldName: 'estimatedSettlementReportingDate',
      defaultVal: '',
      type: 'input',
      visible:true,
      whereShow: ['tableList']
    },
    {
      label: '目前存在的问题',
      fieldName: 'existingProblemsSolutions',
      defaultVal: '',
      type: 'input',
      visible:true,
      whereShow: ['tableList']
    },
    {
      label: '月报类型',
      fieldName: 'monthlyReportType',
      defaultVal: '',
      type: 'input',
      visible: true,
      whereShow: ['tableList','form', 'add', 'edit']
    },
  ]
}
