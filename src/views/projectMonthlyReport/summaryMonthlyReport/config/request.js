import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;

export function getPage(params) {
    return request({
        url: '/monthly/projectMonthlyReport/getProjectMonthlyReport',
        method: 'get',
        params
    })
}

export function save(data) {
    return request({
        url: '/monthly/projectMonthlyReport/saveProjectMonthlyReport',
        method: 'post',
        data
    })
}

//获取月报详情数据
export function getSubmitMonthlyReportProjectGroup(data) {
    return request({
        url: `/monthly/projectMonthlyReport/getSubmitMonthlyReportProjectGroup`,
        method: 'get',
        params: data
    })
}

export function getMonthlyReportProjectGroup(data) {
    return request({
        url: `/monthly/projectMonthlyReport/getMonthlyReportProjectGroup/${data.monthlyReportId}`,
        method: 'get',
        data
    })
}


//重新加载月报数据
export function getAllProjectGroup(data) {
    return request({
        url: `/monthly/projectMonthlyReport/getAllProjectGroup`,
        method: 'get',
        data
    })
}

export function saveProjectMonthlyReport(data) {
    return request({
        url: `/monthly/projectMonthlyReport/saveProjectMonthlyReport`,
        method: 'post',
        data
    })
}

//获取子级组织列表数据

export function getSonOrgList(params) {
    return request({
        url: `/monthly/projectMonthlyReport/getSonOrgList`,
        method: 'get',
        params
    })
}

//保存附件
export function projectMonthlyReportItemAccessory(data) {
    return request({
        url: `/monthly/projectMonthlyReportItemAccessory`,
        method: 'post',
        data
    })
}







export function add(data) {
    return request({
        url: requestUrl,
        method: 'post',
        data
    })
}

export function del(ids) {
    return request({
        url: requestUrl,
        method: 'delete',
        data: ids
    })
}

export function edit(data) {
    return request({
        url: requestUrl,
        method: 'put',
        data
    })
}
//获取附件
export function getProjectMonthlyReportItemAccessoryList(data) {
    return request({
        url: '/monthly/projectMonthlyReportItemAccessory/list',
        method: 'get',
        data
    })
}

//下载附件
export function download(data) {
    return request({
        url: '/monthly/projectMonthlyReport/downloadSubmitMonthlyReportProject',
        method: 'post',
        responseType: 'blob',
        data
    })
}

//获取月报概览echarts数据
export function getEchartsData(data) {
  return request({
    url: `/monthly/projectMonthlyReport/getEchartsData`,
    method: 'get',
    params: data
  })
}

export default { add, edit, del, getPage,
    getSonOrgList,
    projectMonthlyReportItemAccessory,
    download,
    getProjectMonthlyReportItemAccessoryList,
    getMonthlyReportProjectGroup,
    save, getSubmitMonthlyReportProjectGroup, getAllProjectGroup, saveProjectMonthlyReport,getEchartsData  }
