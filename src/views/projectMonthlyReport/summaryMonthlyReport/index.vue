<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div>
        <el-date-picker
          class="date-item"
          size="mini"
          @change="seeDetails"
          v-model="query.date"
          type="monthrange"
          align="left"
          unlink-panels
          range-separator="-"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          :picker-options="pickerOptions"
          :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>

        <el-select v-model="query.monthlyReportType" class="filter-item" clearable placeholder="请选择月报类型" @change="seeDetails">
          <el-option
            v-for="item in dict.monthlyReportType"
            :key="item.code"
            :label="item.name"
            :value="item.code">
          </el-option>
        </el-select>
      </div>
    </div>
    <!--查看附件-->
    <el-dialog
      append-to-body
      :visible.sync="seeUploadDialogVisible"
      title="查看附件"
      width="600px">
      <el-table
        :data="enclosureListData"
        border
        style="width: 100%">
        <el-table-column
          prop="name"
          label="文件名称"
        >
        </el-table-column>
        <el-table-column
          prop="remark"
          label="描述"
        >
        </el-table-column>
        <el-table-column
          prop="createDate"
          label="创建时间"
          width="180">
        </el-table-column>
        <el-table-column
          label="操作">
          <template v-slot="scope">
            <el-button type="text" @click="downLoadFile(scope.row)">下载文件</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog
      append-to-body
      :visible.sync="uploadDialogVisible"
      title="上传附件"
      width="600px">
      <UploadFile
        ref="uploadFile"
        :fileChage="uploadFileSuccess"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="uploadDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveEnclosure">保存</el-button>
      </div>
    </el-dialog>
    <div style="padding-bottom: 10px">
      <el-button type="primary" :disabled="selections.length<=0" icon="el-icon-document-checked"
                 @click="downloadMonthlyReportFile">下载月报
      </el-button>

      <div class="crud-opts">
          <span class="crud-opts-right">
          <el-popover
            placement="bottom-end"
            width="170"
            trigger="click"
          >
            <el-button

              slot="reference"
              size="mini"
              icon="el-icon-s-grid"
            >
              <i
                class="fa fa-caret-down"
                aria-hidden="true"
              />
            </el-button>
            <el-checkbox
              v-for="item in tableListFields"
              :key="item.label"
              v-model="item.visible"
              @change="handleCheckedTableColumnsChange(item)"
            >
              {{ item.label }}
            </el-checkbox>
          </el-popover>
        </span>
      </div>
    </div>

    <el-tabs v-model="tabsActiveName" @tab-click="tabsHandleClick" type="border-card">
      <el-tab-pane label="月报概况" name="0">
        <el-empty v-show="echartsData == null" description="暂无数据" style="width:100%; height:500px"></el-empty>
        <Category v-show="echartsData != null" class="chart-wrapper"
                  :dataMap="echartsData" style="width:100%; height:500px"/>

      </el-tab-pane>
      <el-tab-pane label="已批复结算项目" name="1">
        <el-empty v-if="detailsList.length <= 0" description="暂无数据"></el-empty>
        <el-collapse v-if="detailsList.length > 0" accordion v-model="collapseActiveNames">
          <el-collapse-item v-for="(item, index) in detailsList" :key="index" :name="index">
            <template slot="title">
              {{ item.groupName }}
            </template>
            <el-table
              height="450px"
              :data="item.groupItems"
              @selection-change="selectionChangeHandler"
              border
              style="width: 100%">
              <el-table-column type="selection" width="55"/>

              <el-table-column
                v-if="tableListFields[0].visible"
                prop="code"
                label="序号"
                width="60">
              </el-table-column>

              <el-table-column
                v-if="tableListFields[1].visible"
                prop="projectName"
                label="项目名称"
                width="180">
                <template v-slot="scope">
                  <el-badge value="new" class="item" v-show="scope.row.novice"></el-badge>
                  {{ scope.row.projectName }}
                </template>
              </el-table-column>

              <el-table-column
                v-if="tableListFields[2].visible"
                prop="projectType"
                label="项目类型">
                <template v-slot="scope">
                  {{ showCurrentData(dict.project_type_new, scope.row.projectTypeCd) }}
                </template>
              </el-table-column>
              <el-table-column
                v-if="tableListFields[15].visible"
                prop="monthlyReportType"
                width="80"
                label="月报类型">
                <template v-slot="scope">
                  {{
                    showCurrentData(dict.monthlyReportType, scope.row.monthlyReportType ? scope.row.monthlyReportType.toString() : '')
                  }}
                </template>
              </el-table-column>
              <el-table-column
                v-if="tableListFields[3].visible"
                prop="approvalStatus"
                width="200"
                label="结算审核状态">
                <template v-slot="scope">
                  {{ showCurrentData(dict.settlement_audit_status_cd, scope.row.settlementAuditStatusCd) }}
                </template>
              </el-table-column>

              <el-table-column
                v-if="tableListFields[4].visible"
                prop="buildOrgName"
                width="200"
                label="项目建设单位">
              </el-table-column>

              <el-table-column
                width="200"
                v-if="tableListFields[5].visible"
                prop="estimateAmount"
                label="概算金额（万元）">
              </el-table-column>

              <el-table-column
                v-if="tableListFields[6].visible"
                prop="plannedInvestmentAmount"
                width="200"
                label="累计下达计划金额（万元） ">
              </el-table-column>

              <el-table-column
                v-if="tableListFields[7].visible"
                prop="progress"
                width="200"
                label="项目进展情况">
                <template v-slot="scope">
                  {{ scope.row.progress }}
                </template>
              </el-table-column>

              <el-table-column
                v-if="tableListFields[8].visible"
                prop="overallProgress"
                width="200"
                label="完成项目总进度的%">
                <template v-slot="scope">
                  {{ scope.row.overallProgress }}
                </template>
              </el-table-column>

              <el-table-column
                v-if="tableListFields[9].visible"
                prop="scheduledCompletionTime"
                width="200"
                label="项目完工（或预计完工）时间">
                <template v-slot="scope">
                  {{ dateFormat(scope.row.scheduledCompletionTime) }}
                </template>
              </el-table-column>

              <el-table-column
                width="200"
                v-if="tableListFields[10].visible"
                prop="provincialCompanySettlement"
                label="省级公司结算审核及上报情况">
                <template v-slot="scope">
                  {{ scope.row.provincialCompanySettlement }}
                </template>
              </el-table-column>

              <el-table-column
                v-if="tableListFields[11].visible"
                prop="reportedSettlementAmount"
                width="200"
                label="上报结算金额（万元） ">
                <template v-slot="scope">
                  {{ scope.row.reportedSettlementAmount }}
                </template>
              </el-table-column>
              <el-table-column
                v-if="tableListFields[12].visible"
                prop="approvedSettlementAmount"
                width="200"
                label="审定结算金额（万元）">
                <template v-slot="scope">
                  {{ scope.row.approvedSettlementAmount }}
                </template>
              </el-table-column>


              <el-table-column
                v-if="tableListFields[13].visible"
                prop="estimatedSettlementReportingDate"
                width="200"
                label="预计结算上报时间 ">
                <template v-slot="scope">
                  {{ dateFormat(scope.row.estimatedSettlementReportingDate) }}
                </template>
              </el-table-column>

              <el-table-column
                prop="reportYear"
                width="160"
                label="上报日期">
                <template v-slot="scope">
                  {{ scope.row.reportYear + '-' + scope.row.reportMonth }}
                </template>
              </el-table-column>

              <el-table-column
                width="400"
                v-if="tableListFields[14].visible"
                prop="existingProblemsSolutions"
                label="目前存在的问题（影响结算上报和审核的主要因素）及解决方法 ">
                <template v-slot="scope">
                  {{ scope.row.existingProblemsSolutions }}
                </template>
              </el-table-column>

            </el-table>
          </el-collapse-item>
        </el-collapse>
      </el-tab-pane>
      <el-tab-pane label="正在审核项目" name="2">
        <el-empty v-if="detailsList.length <= 0" description="暂无数据"></el-empty>
        <el-collapse v-if="detailsList.length > 0" accordion v-model="collapseActiveNames">
          <el-collapse-item v-for="(item, index) in detailsList" :key="index" :name="index">
            <template slot="title">
              {{ item.groupName }}
            </template>
            <el-table
              height="450px"
              :data="item.groupItems"
              @selection-change="selectionChangeHandler"
              border
              style="width: 100%">
              <el-table-column type="selection" width="55"/>
              <el-table-column
                v-if="tableListFields[0].visible"
                prop="code"
                label="序号"
                width="60">
              </el-table-column>
              <el-table-column
                v-if="tableListFields[1].visible"
                prop="projectName"
                label="项目名称"
                width="180">
                <template v-slot="scope">
                  <el-badge value="new" class="item" v-show="scope.row.novice"></el-badge>
                  {{ scope.row.projectName }}
                </template>
              </el-table-column>
              <el-table-column
                v-if="tableListFields[2].visible"
                prop="projectType"
                label="项目类型">
                <template v-slot="scope">
                  {{ showCurrentData(dict.project_type_new, scope.row.projectTypeCd) }}
                </template>
              </el-table-column>
              <el-table-column
                v-if="tableListFields[15].visible"
                prop="monthlyReportType"
                width="80"
                label="月报类型">
                <template v-slot="scope">
                  {{
                    showCurrentData(dict.monthlyReportType, scope.row.monthlyReportType ? scope.row.monthlyReportType.toString() : '')
                  }}
                </template>
              </el-table-column>
              <el-table-column
                v-if="tableListFields[3].visible"
                prop="approvalStatus"
                width="200"
                label="结算审核状态">
                <template v-slot="scope">
                  {{ showCurrentData(dict.settlement_audit_status_cd, scope.row.settlementAuditStatusCd) }}
                </template>
              </el-table-column>
              <el-table-column
                v-if="tableListFields[4].visible"
                prop="buildOrgName"
                width="200"
                label="项目建设单位">
              </el-table-column>
              <el-table-column
                width="200"
                v-if="tableListFields[5].visible"
                prop="estimateAmount"
                label="概算金额（万元）">
              </el-table-column>

              <el-table-column
                v-if="tableListFields[6].visible"
                prop="plannedInvestmentAmount"
                width="200"
                label="累计下达计划金额（万元） ">
              </el-table-column>

              <el-table-column
                v-if="tableListFields[7].visible"
                prop="progress"
                width="200"
                label="项目进展情况">
                <template v-slot="scope">
                  {{ scope.row.progress }}
                </template>
              </el-table-column>

              <el-table-column
                v-if="tableListFields[8].visible"
                prop="overallProgress"
                width="200"
                label="完成项目总进度的%">
                <template v-slot="scope">
                  {{ scope.row.overallProgress }}
                </template>
              </el-table-column>

              <el-table-column
                v-if="tableListFields[9].visible"
                prop="scheduledCompletionTime"
                width="200"
                label="项目完工（或预计完工）时间">
                <template v-slot="scope">
                  {{ dateFormat(scope.row.scheduledCompletionTime) }}
                </template>
              </el-table-column>

              <el-table-column
                width="200"
                v-if="tableListFields[10].visible"
                prop="provincialCompanySettlement"
                label="省级公司结算审核及上报情况">
                <template v-slot="scope">
                  {{ scope.row.provincialCompanySettlement }}
                </template>
              </el-table-column>

              <el-table-column
                v-if="tableListFields[11].visible"
                prop="reportedSettlementAmount"
                width="200"
                label="上报结算金额（万元） ">
                <template v-slot="scope">
                  {{ scope.row.reportedSettlementAmount }}
                </template>
              </el-table-column>
              <el-table-column
                v-if="tableListFields[12].visible"
                prop="approvedSettlementAmount"
                width="200"
                label="审定结算金额（万元）">
                <template v-slot="scope">
                  {{ scope.row.approvedSettlementAmount }}
                </template>
              </el-table-column>


              <el-table-column
                v-if="tableListFields[13].visible"
                prop="estimatedSettlementReportingDate"
                width="200"
                label="预计结算上报时间 ">
                <template v-slot="scope">
                  {{ dateFormat(scope.row.estimatedSettlementReportingDate) }}
                </template>
              </el-table-column>

              <el-table-column
                prop="reportYear"
                width="160"
                label="上报日期">
                <template v-slot="scope">
                  {{ scope.row.reportYear + '-' + scope.row.reportMonth }}
                </template>
              </el-table-column>

              <el-table-column
                width="400"
                v-if="tableListFields[14].visible"
                prop="existingProblemsSolutions"
                label="目前存在的问题（影响结算上报和审核的主要因素）及解决方法 ">
                <template v-slot="scope">
                  {{ scope.row.existingProblemsSolutions }}
                </template>
              </el-table-column>

            </el-table>
          </el-collapse-item>
        </el-collapse>
      </el-tab-pane>
      <el-tab-pane label="未结算项目" name="3">
        <el-empty v-if="detailsList.length <= 0" description="暂无数据"></el-empty>
        <el-collapse v-if="detailsList.length > 0" accordion v-model="collapseActiveNames">
          <el-collapse-item v-for="(item, index) in detailsList" :key="index" :name="index">
            <template slot="title">
              {{ item.groupName }}
            </template>
            <el-table
              height="450px"
              :data="item.groupItems"
              @selection-change="selectionChangeHandler"
              border
              style="width: 100%">
              <el-table-column type="selection" width="55"/>
              <el-table-column
                v-if="tableListFields[0].visible"
                prop="code"
                label="序号"
                width="60">
              </el-table-column>
              <el-table-column
                v-if="tableListFields[1].visible"
                prop="projectName"
                label="项目名称"
                width="180">
                <template v-slot="scope">
                  <el-badge value="new" class="item" v-show="scope.row.novice"></el-badge>
                  {{ scope.row.projectName }}
                </template>
              </el-table-column>
              <el-table-column
                v-if="tableListFields[2].visible"
                prop="projectType"
                label="项目类型">
                <template v-slot="scope">
                  {{ showCurrentData(dict.project_type_new, scope.row.projectTypeCd) }}
                </template>
              </el-table-column>
              <el-table-column
                v-if="tableListFields[15].visible"
                prop="monthlyReportType"
                width="80"
                label="月报类型">
                <template v-slot="scope">
                  {{
                    showCurrentData(dict.monthlyReportType, scope.row.monthlyReportType ? scope.row.monthlyReportType.toString() : '')
                  }}
                </template>
              </el-table-column>
              <el-table-column
                v-if="tableListFields[3].visible"
                prop="approvalStatus"
                width="200"
                label="结算审核状态">
                <template v-slot="scope">
                  {{ showCurrentData(dict.settlement_audit_status_cd, scope.row.settlementAuditStatusCd) }}
                </template>
              </el-table-column>
              <el-table-column
                v-if="tableListFields[4].visible"
                prop="buildOrgName"
                width="200"
                label="项目建设单位">
              </el-table-column>
              <el-table-column
                width="200"
                v-if="tableListFields[5].visible"
                prop="estimateAmount"
                label="概算金额（万元）">
              </el-table-column>

              <el-table-column
                v-if="tableListFields[6].visible"
                prop="plannedInvestmentAmount"
                width="200"
                label="累计下达计划金额（万元） ">
              </el-table-column>

              <el-table-column
                v-if="tableListFields[7].visible"
                prop="progress"
                width="200"
                label="项目进展情况">
                <template v-slot="scope">
                  {{ scope.row.progress }}
                </template>
              </el-table-column>

              <el-table-column
                v-if="tableListFields[8].visible"
                prop="overallProgress"
                width="200"
                label="完成项目总进度的%">
                <template v-slot="scope">
                  {{ scope.row.overallProgress }}
                </template>
              </el-table-column>

              <el-table-column
                v-if="tableListFields[9].visible"
                prop="scheduledCompletionTime"
                width="200"
                label="项目完工（或预计完工）时间">
                <template v-slot="scope">
                  {{ dateFormat(scope.row.scheduledCompletionTime) }}
                </template>
              </el-table-column>

              <el-table-column
                width="200"
                v-if="tableListFields[10].visible"
                prop="provincialCompanySettlement"
                label="省级公司结算审核及上报情况">
                <template v-slot="scope">
                  {{ scope.row.provincialCompanySettlement }}
                </template>
              </el-table-column>

              <el-table-column
                v-if="tableListFields[11].visible"
                prop="reportedSettlementAmount"
                width="200"
                label="上报结算金额（万元） ">
                <template v-slot="scope">
                  {{ scope.row.reportedSettlementAmount }}
                </template>
              </el-table-column>
              <el-table-column
                v-if="tableListFields[12].visible"
                prop="approvedSettlementAmount"
                width="200"
                label="审定结算金额（万元）">
                <template v-slot="scope">
                  {{ scope.row.approvedSettlementAmount }}
                </template>
              </el-table-column>


              <el-table-column
                v-if="tableListFields[13].visible"
                prop="estimatedSettlementReportingDate"
                width="200"
                label="预计结算上报时间 ">
                <template v-slot="scope">
                  {{ dateFormat(scope.row.estimatedSettlementReportingDate) }}
                </template>
              </el-table-column>

              <el-table-column
                prop="reportYear"
                width="160"
                label="上报日期">
                <template v-slot="scope">
                  {{ scope.row.reportYear + '-' + scope.row.reportMonth }}
                </template>
              </el-table-column>

              <el-table-column
                width="400"
                v-if="tableListFields[14].visible"
                prop="existingProblemsSolutions"
                label="目前存在的问题（影响结算上报和审核的主要因素）及解决方法 ">
                <template v-slot="scope">
                  {{ scope.row.existingProblemsSolutions }}
                </template>
              </el-table-column>

            </el-table>
          </el-collapse-item>
        </el-collapse>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import crudRequest from './config/request'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import Category from '@/components/Echarts/Category'
import request from '@/utils/request'
import format from '@/utils/date.format'

import UploadFile from '@/components/UploadFile'
//注入配置文件
import config from './config/index'

const formFields = config.getFields('form');
const tableListFields = config.getFields('tableList');
const defaultForm = config.getDefaultForm(formFields);
import {downloadFile} from '@/utils/index'
import {mapGetters} from "vuex";
import moment from "moment";

function zeroFilling(n) {
  if (n < 10) {
    return '0' + n;
  }
  return n;
}

export default {
  name: 'simpleList',
  components: {UploadFile, Category},
  // 设置数据字典
  dicts: ['settlement_audit_status_cd', 'project_types', 'project_type_new', 'monthlyReportType'],
  computed: {
    ...mapGetters([
      'user',
    ])
  },
  data() {
    return {
      query: {
        date: [moment().startOf('month'), moment().endOf('month')]
      },
      echartsData: {barDataMap: [], pieDataMap: []},
      tabsActiveName: '0',
      settlementAuditStatusCd: 1,
      //查看附件列表弹窗
      seeUploadDialogVisible: false,
      //附件列表
      enclosureListData: [],
      //当前操作附件的数据
      currentEnclosure: {},
      //附件列表数据
      enclosureList: [],
      //上传附件弹窗
      uploadDialogVisible: false,
      //组织列表
      sonOrgList: [],
      //选中数据
      selections: [],
      //当前选中数据
      currentRecord: {},
      collapseActiveNames: [],
      //详情弹窗
      detailsDialogVisible: false,
      //详情列表
      detailsList: [],
      deptName: '',
      formFields: formFields,
      tableListFields: tableListFields,
      defaultProps: {children: 'children', label: 'name'},
      rules: {
        name: [
          {required: true, message: '请输入名称', trigger: 'blur'}
        ]
      },
      permission: {
        add: ['admin', 'project:projectMonthlyReport:add'],
        edit: ['admin', 'project:projectMonthlyReport:edit'],
        del: ['admin', 'project:projectMonthlyReport:del']
      },
      enabledTypeOptions: [
        {key: 'true', display_name: '正常'},
        {key: 'false', display_name: '禁用'}
      ],
      pickerOptions: {
        shortcuts: [{
          text: '本年',
          onClick(picker) {
            const start = new Date(new Date().getFullYear(), 0);
            const end = new Date(new Date().getFullYear(), 11);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()]);
          }
        }, {
          text: '第一季度',
          onClick(picker) {
            const start = new Date(new Date().getFullYear(), 0);
            const end = new Date(new Date().getFullYear(), 2);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '第二季度',
          onClick(picker) {
            const start = new Date(new Date().getFullYear(), 3);
            const end = new Date(new Date().getFullYear(), 5);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '第三季度',
          onClick(picker) {
            const start = new Date(new Date().getFullYear(), 6);
            const end = new Date(new Date().getFullYear(), 8);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '第四季度',
          onClick(picker) {
            const start = new Date(new Date().getFullYear(), 9);
            const end = new Date(new Date().getFullYear(), 11);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },
  watch: {
    uploadDialogVisible(newVal) {
      if (!newVal) {
        this.$refs.uploadFile.clearFiles();
        this.enclosureList = [];
      }
    }
  },
  methods: {
    dateFormat(value) {
      if (value !== null) {
        var myDate = new Date(value);
        return myDate.format("Y-m-d");
      }
    },
    syncColumn() {
      //同步更新
      request({
        url: '/system/userTableColumn/' + this.user.id + '/' + this.$route.name,
        method: 'get'
      }).then(res => {
        this.entity = res;
        if (res) {
          this.tableListFields = JSON.parse(res.context);
          this.updateTable(this.tableListFields);
        }
      })
    },
    saveColumnConfig() {
      //保存
      if (this.entity) {
        this.entity.context = JSON.stringify(this.tableListFields)
      } else {
        this.entity = {
          userId: this.user.id,
          routerName: this.$route.name,
          context: JSON.stringify(this.tableListFields)
        }
      }
      request({
        url: '/system/userTableColumn/',
        method: 'put',
        data: this.entity
      })
    },
    handleCheckedTableColumnsChange(item) {
      this.saveColumnConfig();
    },
    getEchartsData() {
      crudRequest.getEchartsData({
        queryStartDate: moment(this.query.date[0]).format('YYYY-MM-DD HH:mm:ss'),
        queryEndDate: moment(this.query.date[1]).format('YYYY-MM-DD HH:mm:ss'),
        monthlyReportType: this.query.monthlyReportType
      }).then(res => {
        this.echartsData = res;
      })
    },
    tabsHandleClick(tab) {
      this.settlementAuditStatusCd = tab.name;
      this.seeDetails();
    },
    downloadMonthlyReport() {
      crudRequest.getSubmitMonthlyReportProjectGroup({})
        .then(res => {
          this.detailsDialogVisible = true;
          this.currentRecord = {
            isDownLoad: 1
          };

          this.setListData(res);
          this.detailsList = res;
        })
    },
    downloadMonthlyReportFile() {
      //downloadMonthlyReportFile
      if (this.selections.length <= 0) {
        return;
      }
      crudRequest.download(
        this.selections.map(item => item.id)
      )
        .then(res => {

          downloadFile(res, '月报.xlsx', '');
        })
    },
    //下载附件
    downLoadFile(record) {

      crudRequest.download({
        uri: record.uri
      })
        .then(res => {
          downloadFile(res, record.name);
        })

    },
    //查看附件
    seeEnclosure(record) {
      crudRequest.getProjectMonthlyReportItemAccessoryList({
        monthlyReportItemIdEq: record.id
      })
        .then(res => {
          this.seeUploadDialogVisible = true;
          this.enclosureListData = res;
        })

    },
    //保存附件
    saveEnclosure() {

      //如果去掉默认数据数组为空，则不需要上次
      if (this.enclosureList.length <= 0) {
        this.uploadDialogVisible = false;
        this.crud.notify('操作成功！', 'success');
        return;
      }

      crudRequest.projectMonthlyReportItemAccessory({
        accessories: this.enclosureList.map(item => ({
          name: item.name,
          uri: item.src,
          remark: item.remark,
          monthlyReportItemId: this.currentEnclosure.id
        })),
        monthlyReportItemId: this.currentEnclosure.id
      })
        .then(res => {
          this.uploadDialogVisible = false;
          this.crud.notify('操作成功！', 'success');
        })
    },
    closeBeforeEnclosureDialog() {

    },
    //文件上传成功处理
    uploadFileSuccess(current, list) {
      this.enclosureList = list;
    },
    //上传附件
    uploadEnclosure(record) {
      crudRequest.getProjectMonthlyReportItemAccessoryList({
        monthlyReportItemIdEq: record.id
      })
        .then(res => {
          this.uploadDialogVisible = true;
          this.currentEnclosure = record;
          setTimeout(() => {
            this.$refs.uploadFile.setDefaultFiles(res);
          }, 0)

        })
    },

    selectionChangeHandler(records) {
      this.selections = records;
    },
    //保存月报
    saveMonthlyReport() {
      crudRequest.saveProjectMonthlyReport({
        ifSubmit: 0,
        items: this.detailsList.map(item => item.groupItems).flat(1)
      })
        .then(res => {
          this.crud.notify('操作成功！', 'success')
        })
    },
    //提交月报
    submitMonthlyReport() {
      let data = [...this.detailsList.map(item => item.groupItems).flat(1)];
      data.map(item => {
        if (this.selections.filter(citem => item.id == citem.id).length > 0) {
          item.ifSubmit = 1;
        }
      })
      crudRequest.saveProjectMonthlyReport({
        ifSubmit: 1,
        items: data
      })
        .then(res => {
          this.crud.notify('操作成功！', 'success')
        })

    },
    //重新加载月报
    reloadMonthlyReport() {
      crudRequest.getAllProjectGroup({})
        .then(res => {
          this.setListData(res);
          this.detailsList = res;
        })
    },
    //新增月报
    addMonthlyReport() {
      crudRequest.getPage({})
        .then(res => {

          this.setListData(res);
          this.detailsList = res;
        })
    },
    setListData(res) {
      let code = 0;
      res.map(item => {
        item.groupItems.map(citem => {
          code++;
          citem.code = code;
          if (citem.settlementAuditStatusCd) {
            citem.settlementAuditStatusCd = citem.settlementAuditStatusCd.toString();
          }
        })
      })
    },
    checkboxT(row, rowIndex) {
      return row.id !== 1
    },
    seeDetails(record) {
      record = record || {};
      // record.ifRead = 1;
      crudRequest.getSubmitMonthlyReportProjectGroup({
        //monthlyReportId: record.id
        orgCode: record.orgCode,
        queryStartDate: moment(this.query.date[0]).format('YYYY-MM-DD HH:mm:ss'),
        queryEndDate: moment(this.query.date[1]).format('YYYY-MM-DD HH:mm:ss'),
        settlementAuditStatusCd: this.settlementAuditStatusCd,
        monthlyReportType: this.query.monthlyReportType
      })
        .then(res => {
          res = res || [];
          this.detailsDialogVisible = true;
          // record.ifLocked = record.ifLocked == 0 ? 1 : 0;
          this.currentRecord = record;
          this.setListData(res);
          this.detailsList = res;
        })
      this.getEchartsData();
    },
    expandChange(row, expandedRows) {
      if (row.children) return
      crudRequest.getMonthlyReportProjectGroup({
        monthlyReportId: row.id
      })
        .then(res => {
          row.children = res;
        })
    },
    getSonOrgList() {
      const sort = 'id,desc'
      const params = {sort: sort}
      if (this.deptName) {
        params['name'] = this.deptName
      }
      crudRequest.getSonOrgList(params)
        .then(res => {
          this.sonOrgList = res;
        })
    },
    // 切换部门
    handleNodeClick(data) {
      if (this.query.orgCode == data.code) {
        data.code = '';
      }
      data.ifEnabledCd = 1;
      this.query.orgCode = data.code;
      this.seeDetails({
        orgCode: data.code
      });
    },

    initPageData() {
      this.getSonOrgList();
      this.seeDetails();
    }
  },
  mounted() {
    this.initPageData();
    this.syncColumn();
  }
}
</script>

<style scoped>
.item {
  margin-top: 8px;

}

.crud-opts {
  float: right;
  padding: 4px 0;
  display: -webkit-flex;
  display: flex;
  align-items: center;
}

.crud-opts .crud-opts-right {
  margin-left: auto;
}
</style>
