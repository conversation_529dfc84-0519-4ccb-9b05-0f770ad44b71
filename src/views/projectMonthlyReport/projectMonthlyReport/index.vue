<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div>
        <!-- 搜索 -->
        <el-date-picker
            size="small"
            class="date-item"
            @change="crud.toQuery"
            @remove="crud.toQuery"
            v-model="query.date"
            type="monthrange"
            align="left"
            unlink-panels
            range-separator="-"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            :picker-options="pickerOptions"
            :default-time="['00:00:00', '23:59:59']"
            :clearable="false">
        </el-date-picker>
        <el-select class="filter-item" v-model="query.orgCode" clearable placeholder="请选择组织" @change="crud.toQuery">
          <el-option v-for="item in sonOrgList" :key="item.code" :label="item.name" :value="item.code"/>
        </el-select>
<!--        <rrOperation/>-->
      </div>
      <!-- <crudOperation :permission="permission" />  -->
      <el-button type="primary" icon="el-icon-document-checked" @click="addMonthlyReport">新增月报</el-button>
      <span class="annotation" style="margin-top: 12px;right: 40px;color: red">* 提示:月报报送时间为每月15号前</span>
    </div>
    <!--查看附件-->
    <el-dialog
      append-to-body
      :visible.sync="seeUploadDialogVisible"
      title="查看附件"
      width="600px">
      <el-table
        :data="enclosureListData"
        border
        style="width: 100%">
        <el-table-column
          prop="name"
          label="文件名称"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="remark"
          label="描述"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="createDate"
          label="创建时间"
          width="180"
          align="center">
        </el-table-column>
        <el-table-column
          label="操作"
          align="center">
          <template v-slot="scope">
            <el-button type="text" @click="downLoadFile(scope.row)">下载文件</el-button>
            <el-button type="text" size='mini' @click="previewAttachment(scope.row)">预览</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog
      append-to-body
      :visible.sync="uploadDialogVisible"
      title="上传附件"
      width="600px">
      <UploadFile
        ref="uploadFile"
        :fileChage="uploadFileSuccess"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="uploadDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveEnclosure">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog v-if="detailsDialogVisible" append-to-body :visible.sync="detailsDialogVisible" title="项目月报" width="80%" :before-close="handleClose" fullscreen	>

      <div style="padding-bottom: 10px" v-if="!currentRecord.ifLocked">
        <el-button type="primary" icon="el-icon-document-checked" @click="saveMonthlyReport">保存数据</el-button>
        <el-button type="primary" icon="el-icon-tickets" @click="submitMonthlyReport">
          提交月报
        </el-button>
        <span class="annotation" style="margin-top: 12px;right: 40px;">
           <svg-icon icon-class="month_submit" class="svg-submit"></svg-icon><span>:本月已提交&nbsp;&nbsp;&nbsp;</span>
           <svg-icon icon-class="baocun" class="svg-baocun"></svg-icon><span>:本月已保存</span>
        </span>
      </div>
      <el-collapse v-model="collapseActiveNames" accordion>
        <el-collapse-item v-for="(item, index) in detailsList" :key="index" :name="index"  @click.native="collapseClick(index)">
          <template slot="title">
            {{ item.groupName }}
          </template>
          <ux-grid
            :ref="'table'+index"
            :height="650"
            @selection-change="selectionChangeHandler"
            :checkboxConfig="{ highlight: true}"
            show-header-overflow="title"
            show-overflow="title"
            :row-style="{height: '80px'}"
            size="small">

<!--          <el-table-->
<!--            height="450px"-->
<!--            :data="item.groupItems"-->
<!--            @selection-change="selectionChangeHandler"-->
<!--            @select="selectChangeHandler"-->
<!--            border-->
<!--            :ref="'table'+index"-->
<!--            style="width: 100%">-->

            <ux-table-column :resizable="true" type="checkbox" width="45"  fixed="left" v-if="!currentRecord.ifLocked" :index=0></ux-table-column>
            <ux-table-column
              :index=1
              header-align="center"
              :resizable="true"
              field="code"
              title="序号"
              fixed="left"
              width="50">
            </ux-table-column>
            <ux-table-column
              :index=2
              header-align="center"
              :resizable="true"
              field="projectName"
              fixed="left"
              title="项目名称"
              width="240">
              <template v-slot="scope">
                  <svg-icon v-if="scope.row.currentMonthIfSubmit === 1" icon-class="month_submit" class="svg-submit"></svg-icon>
                  <svg-icon v-else-if="scope.row.currentMonthIfReported === 1" icon-class="baocun" class="svg-baocun"></svg-icon>
                {{scope.row.projectName}}
              </template>
              </ux-table-column>


            <ux-table-column
              :index=3
              header-align="center"
              :resizable="true"
              field="projectTypeCd"
              title="项目类型"
              width="120">
              <template v-slot="scope">
                {{ showCurrentData(dict.project_type_new, scope.row.projectTypeCd) }}
              </template>
            </ux-table-column>
            <ux-table-column
              :index=15
              header-align="center"
              :resizable="true"
              field="monthlyReportType"
              title="月报类型"
              width="120">
              <template v-slot="scope"  v-if="currentRecord.ifLocked">
                {{ showCurrentData(dict.monthlyReportType, scope.row.monthlyReportType) }}
              </template>
              <template v-slot="scope" v-if="!currentRecord.ifLocked">
                <el-select v-model="scope.row.monthlyReportType" placeholder="月报类型">
                  <el-option
                    v-for="item in dict.monthlyReportType"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                  </el-option>
                </el-select>
              </template>
            </ux-table-column>

            <ux-table-column
              :index=4
              header-align="center"
              :resizable="true"
              width="150"
              title="结算审核状态">
              <template v-slot="scope" v-if="currentRecord.ifLocked">
                {{ showCurrentData(dict.settlement_audit_status_cd, scope.row.settlementAuditStatusCd) }}
              </template>
              <template v-slot="scope" v-if="!currentRecord.ifLocked">
                <el-select v-model="scope.row.settlementAuditStatusCd" placeholder="请选择">
                  <el-option
                    v-for="item in dict.settlement_audit_status_cd"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                  </el-option>
                </el-select>
              </template>
            </ux-table-column>

            <ux-table-column
              :index=5
              header-align="center"
              :resizable="true"
              field="buildOrgName"
              width="130"
              title="项目建设单位">
            </ux-table-column>

            <ux-table-column
              :index=6
              header-align="center"
              :resizable="true"
              width="130"
              field="estimateAmount"
              title="概算金额(万元)">
            </ux-table-column>

            <ux-table-column
              :index=7
              header-align="center"
              :resizable="true"
              field="plannedInvestmentAmount"
              width="150"
              title="累计下达计划金额(万元)">
            </ux-table-column>

            <ux-table-column
              :index=8
              header-align="center"
              :resizable="true"
              width="200"
              title="项目进展情况">
              <template v-slot="scope" v-if="currentRecord.ifLocked">
                {{ scope.row.progress }}
              </template>
              <template v-slot="scope" v-if="!currentRecord.ifLocked">
                <el-input :autosize="{ minRows: 3, maxRows: 3}" type="textarea" v-model="scope.row.progress" placeholder="请输入"/>
              </template>
            </ux-table-column>

            <ux-table-column
              :index=9
              header-align="center"
              :resizable="true"
              width="150"
              title="完成项目总进度的%">
              <template v-slot="scope" v-if="currentRecord.ifLocked">
                {{ scope.row.overallProgress }}
              </template>
              <template v-slot="scope" v-if="!currentRecord.ifLocked">
                <el-input type="text" v-model="scope.row.overallProgress" placeholder="请输入"/>
              </template>
            </ux-table-column>

            <ux-table-column
              :index=10
              header-align="center"
              :resizable="true"
              width="190"
              title="项目完工（或预计完工）时间">
              <template v-slot="scope" v-if="currentRecord.ifLocked">
                {{ scope.row.scheduledCompletionTime }}
              </template>
              <template v-slot="scope" v-if="!currentRecord.ifLocked">
                <el-date-picker
                  v-model="scope.row.scheduledCompletionTime"
                  value-format="yyyy-MM-dd"
                  value ="yyyy-MM-dd"
                  type="date"
                  style="width: 168px"
                  placeholder="选择日期时间">
                </el-date-picker>
              </template>
            </ux-table-column>

            <ux-table-column
              :index=11
              header-align="center"
              width="200"
              :resizable="true"
              title="省级公司结算审核及上报情况">
              <template v-slot="scope" v-if="currentRecord.ifLocked">
                {{ scope.row.provincialCompanySettlement }}
              </template>
              <template v-slot="scope" v-if="!currentRecord.ifLocked">
                <el-input :autosize="{ minRows: 3, maxRows: 3}" type="textarea" v-model="scope.row.provincialCompanySettlement" placeholder="请输入"/>
              </template>
            </ux-table-column>

            <ux-table-column
              :index=12
              header-align="center"
              :resizable="true"
              width="150"
              title="上报结算金额(万元)">
              <template v-slot="scope" v-if="currentRecord.ifLocked">
                {{ scope.row.reportedSettlementAmount }}
              </template>
              <template v-slot="scope" v-if="!currentRecord.ifLocked">
                <el-input type="text" v-model="scope.row.reportedSettlementAmount" placeholder="请输入"/>
              </template>
            </ux-table-column>

            <ux-table-column
              :index=13
              header-align="center"
              :resizable="true"
              width="150"
              title="审定结算金额(万元)">
              <template v-slot="scope" v-if="currentRecord.ifLocked">
                {{ scope.row.approvedSettlementAmount }}
              </template>
              <template v-slot="scope" v-if="!currentRecord.ifLocked">
                <el-input type="text" v-model="scope.row.approvedSettlementAmount" placeholder="请输入"/>
              </template>
            </ux-table-column>


            <ux-table-column
              :index=14
              header-align="center"
              :resizable="true"
              width="180"
              title="预计结算上报时间">
              <template v-slot="scope" v-if="currentRecord.ifLocked">
                {{ scope.row.estimatedSettlementReportingDate }}
              </template>
              <template v-slot="scope" v-if="!currentRecord.ifLocked">
                <el-date-picker
                  v-model="scope.row.estimatedSettlementReportingDate"
                  value-format="yyyy-MM-dd"
                  value ="yyyy-MM-dd"
                  type="date"
                  style="width: 158px"
                  placeholder="选择日期时间">
                </el-date-picker>
              </template>
            </ux-table-column>

            <ux-table-column
              :index=15
              header-align="center"
              :resizable="true"
              width="400"
              field="existingProblemsSolutions"
              title="目前存在的问题（影响结算上报和审核的主要因素）及解决方法 ">
              <template v-slot="scope" v-if="currentRecord.ifLocked">
                {{ scope.row.existingProblemsSolutions }}
              </template>

              <template v-slot="scope" v-if="!currentRecord.ifLocked">
                <el-input :autosize="{ minRows: 3, maxRows: 3}" type="textarea" v-model="scope.row.existingProblemsSolutions" placeholder="请输入"/>
              </template>
            </ux-table-column>

            <ux-table-column
              :index=16
              header-align="center"
              :resizable="true"
              v-permission="['admin','project:projectMonthlyReport:edit','project:projectMonthlyReport:del']" label="操作"
              width="160px" align="center" fixed="right">
              <template v-slot="scope">
                <el-button type="text" @click="uploadEnclosure(scope.row)">上传附件</el-button>
                <el-button type="text" @click="seeEnclosure(scope.row)">查看附件</el-button>
              </template>
            </ux-table-column>
<!--          </el-table>-->
          </ux-grid>
        </el-collapse-item>
      </el-collapse>

    </el-dialog>
    <!--表单组件-->
    <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU"
               :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
      <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
        <template v-for="(item, key) in formFields">
          <template v-if="item.type == 'radio'">
            <el-form-item label="状态" :prop="item.fieldName" :key="key">
              <el-radio v-for="item in dict.dept_status" :key="item.id" v-model="form.enabled" :label="item.value">{{
                item.label }}
              </el-radio>
            </el-form-item>
          </template>
          <template v-if="item.type == 'input'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-input v-model="form[item.fieldName]" :style="formInputStyle"/>
            </el-form-item>
          </template>
          <template v-if="item.type == 'date'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-date-picker
                v-model="form[item.fieldName]"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                :style="formInputStyle"
                placeholder="选择日期时间">
              </el-date-picker>
            </el-form-item>
          </template>
          <template v-if="item.type == 'select'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]" :style="formInputStyle" placeholder="请选择">
                <el-option
                  v-for="(item) in dict.project_types"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-if="item.type == 'textarea'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-input :autosize="{ minRows: 1, maxRows: 1}" :type="item.type" v-model="form[item.fieldName]" style="width: 370px;"/>
            </el-form-item>
          </template>
        </template>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>
    <!--表格渲染-->

    <el-table
      v-loading="crud.loading"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      :data='crud.data'
      row-key="id"
      @expand-change="expandChange"
      @select="crud.selectChange"
      @select-all="crud.selectAllChange">

      <template v-for="(item, key) in tableListFields">
        <el-table-column :label="item.label" :prop="item.fieldName" :key="key">
          <template v-slot="scope">
            {{ item.fieldName == 'ifLocked' ? scope.row[ item.fieldName ] == 1 ? '是' : '否' : scope.row[ item.fieldName ]
            }}
          </template>
        </el-table-column>
      </template>

      <el-table-column v-permission="['admin','projectMonthlyReport:projectMonthlyReport:details']" label="操作"
                       width="130px" align="center" fixed="right">
        <template v-slot="scope">
          <el-button type="text" @click="seeDetails(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination/>
    <!--    预览-->
    <reviewFile
      :reviewFile="reviewFile"
      :open-review="showReview"
      :closeReview="closeReview"
    >
    </reviewFile>
  </div>
</template>

<script>
  import crudRequest from './config/request'
  import '@riophae/vue-treeselect/dist/vue-treeselect.css'
  import CRUD, {presenter, header, form, crud} from '@crud/crud'
  import rrOperation from '@crud/RR.operation'
  import crudOperation from '@crud/CRUD.operation'
  import udOperation from '@crud/UD.operation'
  import pagination from '@crud/Pagination'
  import UploadFile from '@/components/UploadFile'
  import {UxGrid, UxTableColumn } from 'umy-ui';
  //注入配置文件
  import config from './config/index'

  const formFields = config.getFields('form');
  const tableListFields = config.getFields('tableList');
  const defaultForm = config.getDefaultForm(formFields);

  import {downloadFile} from '@/utils/index'
  import moment from "moment";
  import reviewFile from "@/components/Review";

  export default {
    name: 'simpleList',
    components: {crudOperation, rrOperation, udOperation, pagination, UploadFile,UxGrid,UxTableColumn,reviewFile},
    cruds() {
      return CRUD({title: '模板', url: config.requestUrl, crudMethod: {...crudRequest}})
    },
    mixins: [presenter(), header(), form(defaultForm), crud()],
    // 设置数据字典
    dicts: ['settlement_audit_status_cd','project_types','project_type_new','monthlyReportType'],
    data() {
      return {
        query: {
          date: [moment().startOf('year'), moment().endOf('year')]
        },
        //月报详情id
        monthlyReportId:'',
        //项目月报内容附件
        accessories:{},
        //查看附件列表弹窗
        seeUploadDialogVisible: false,
        //附件列表
        enclosureListData: [],
        //当前操作附件的数据
        currentEnclosure: {},
        //附件列表数据
        enclosureList: [],
        //上传附件弹窗
        uploadDialogVisible: false,
        //组织列表
        sonOrgList: [],
        //选中数据
        selections: [],
        //当前选中数据
        currentRecord: {},
        collapseActiveNames: [],
        //详情弹窗
        detailsDialogVisible: false,
        //详情列表
        detailsList: [],
        formFields: formFields,
        tableListFields: tableListFields,
        rules: {
          name: [
            {required: true, message: '请输入名称', trigger: 'blur'}
          ]
        },
        permission: {
          add: ['admin', 'project:projectMonthlyReport:add'],
          edit: ['admin', 'project:projectMonthlyReport:edit'],
          del: ['admin', 'project:projectMonthlyReport:del']
        },
        enabledTypeOptions: [
          {key: 'true', display_name: '正常'},
          {key: 'false', display_name: '禁用'}
        ],
        queryMonthlyReportId: "",
        pickerOptions: {
          shortcuts: [{
            text: '本年',
            onClick(picker) {
              const start = new Date(new Date().getFullYear(), 0);
              const end = new Date(new Date().getFullYear(), 11);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '本月',
            onClick(picker) {
              picker.$emit('pick', [new Date(), new Date()]);
            }
          }, {
            text: '第一季度',
            onClick(picker) {
              const start = new Date(new Date().getFullYear(), 0);
              const end = new Date(new Date().getFullYear(), 2);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '第二季度',
            onClick(picker) {
              const start = new Date(new Date().getFullYear(), 3);
              const end = new Date(new Date().getFullYear(), 5);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '第三季度',
            onClick(picker) {
              const start = new Date(new Date().getFullYear(), 6);
              const end = new Date(new Date().getFullYear(), 8);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '第四季度',
            onClick(picker) {
              const start = new Date(new Date().getFullYear(), 9);
              const end = new Date(new Date().getFullYear(), 11);
              picker.$emit('pick', [start, end]);
            }
          }]
        },
        reviewFile: {},
        showReview: false,
      }
    },
    watch: {
      uploadDialogVisible(newVal) {
        if (!newVal) {
          this.$refs.uploadFile.clearFiles();
          this.enclosureList = [];
        }
      },
      detailsList(newVal) {
        setTimeout(() => {
          this.collapseActiveNames = [];
          this.detailsList.map((item, index) => {
            item.groupItems.map((citem, cindex) => {
              //this.$refs['table' + index][0].toggleRowSelection([{row:citem,selected: true}]);
            })
            // this.collapseActiveNames.push(index);
          })
          this.$nextTick(() => {
            this.collapseActiveNames = [...this.collapseActiveNames]
          })
        }, 0)
      }
    },
    methods: {
      collapseClick(index){
        this.$refs['table' + index][0].doLayout();
      },
      handleClose(done){
        this.monthlyReportId = '';
        done();
      },
      //下载附件
      downLoadFile(record) {
        //downloadFile
        crudRequest.download({
          uri: record.uri
        })
          .then(res => {
            downloadFile(res, record.name);
          })

      },
      //查看附件
      seeEnclosure(record) {
        crudRequest.getProjectMonthlyReportItemAccessoryList({
          monthlyReportItemIdEq: record.id
        })
          .then(res => {
            this.seeUploadDialogVisible = true;
            this.enclosureListData = res;
          })

      },
      //保存附件
      saveEnclosure() {
        //如果去掉默认数据数组为空，则不需要上次
        if (this.enclosureList.length <= 0) {
          this.uploadDialogVisible = false;
          this.crud.notify('操作成功！', 'success');
          return;
        }

        crudRequest.projectMonthlyReportItemAccessory({
          accessories: this.enclosureList.map(item => ({
            name: item.name,
            uri: item.src,
            remark: item.remark,
            monthlyReportItemId: this.currentEnclosure.id
          })),
          monthlyReportItemId: this.currentEnclosure.id
        })
          .then(res => {
            this.uploadDialogVisible = false;
            this.crud.notify('操作成功！', 'success');
          })
      },
      closeBeforeEnclosureDialog() {

      },
      //文件上传成功处理
      uploadFileSuccess(current, list) {
        this.enclosureList = list;
      },
      //上传附件
      uploadEnclosure(record) {
        if (record.currentMonthIfReported === 1 || record.currentMonthIfSubmit === 1) {
          crudRequest.getProjectMonthlyReportItemAccessoryList({
            monthlyReportItemIdEq: record.id
          }).then(res => {
            this.uploadDialogVisible = true;
            this.currentEnclosure = record;
            setTimeout(() => {
              this.$refs.uploadFile.setDefaultFiles(res);
            }, 0)
          })
        }else {
          this.$message.warning('请先保存数据！');
        }
      },
      [CRUD.HOOK.beforeRefresh](crud) {
        crud.query.queryStartDate = moment(this.query.date[0]).format('YYYY-MM-DD HH:mm:ss');
        crud.query.queryEndDate = moment(this.query.date[1]).format('YYYY-MM-DD HH:mm:ss');
      },
      selectionChangeHandler(refsTable) {
        //赋值触发
        if (Object.prototype.toString.call(refsTable) === "[object Object]") {
          this.selections.push(...refsTable.getCheckboxRecords().flat(1));
        }else {//勾选触发 因为 有多个table所以直接循环插入所有表单
          let arrTable = []
          this.detailsList.forEach((item,index)=>{
            arrTable.push(this.$refs['table' + index][0].getCheckboxRecords())
          })
          this.selections = arrTable.flat(1);
        }
      },
      selectChangeHandler(selection, row){

      },
      //保存月报
      saveMonthlyReport() {
        let data = [...this.selections];
        if (data.length === 0) {
          this.crud.notify('请勾选需要提交的项目', 'warning')
          return;
        }
        crudRequest.saveProjectMonthlyReport({
          ifSubmit: 0,
          items: this.selections
        }).then(res => {
          this.crud.notify('操作成功！', 'success')
          this.crud.refresh();
          this.refreshMonthlyReport();
        })
      },
      refreshMonthlyReport(){
        crudRequest.getPage({})
          .then(res => {
            this.currentRecord = {ifLocked: 0};
            // this.setListData(res);
            // this.detailsList = res;
            let concat = [].concat(...res.map(o => o.groupItems));
            this.detailsList.forEach((item, index) => {
              item.groupItems.forEach(row => {
                if (this.$refs['table' + index][0].isCheckedByCheckboxRow(row)) {
                  let filterRow = concat.filter(e=>e.projectId === row.projectId );
                  row.currentMonthIfReported = 1;
                  row.id = filterRow[0].id;
                }
              })
            })
            //动态赋值
            this.setFormDate(false);
          })
      },
      //提交月报
      submitMonthlyReport() {
        let data = [...this.selections];
        if (data.length === 0) {
          this.crud.notify('请勾选需要提交的项目', 'warning')
          return;
        }
        data.map(item => {
          if (this.selections.filter(citem => item.id == citem.id).length > 0) {
            item.ifReported = 1;
          }
        })
        crudRequest.saveProjectMonthlyReport({
          monthlyReportId: this.monthlyReportId,
          ifSubmit: 1,
          items: data
        })
          .then(res => {
            this.crud.notify('操作成功！', 'success')
            this.detailsDialogVisible = false;
            this.crud.refresh();
          })
      },
      //重新加载月报
      reloadMonthlyReport() {
        this.seeDetails(this.currentRecord);
        // crudRequest.getAllProjectGroup({})
        //   .then(res => {
        //     this.setListData(res);
        //     this.detailsList = res;
        //   })
      },
      //新增月报
      addMonthlyReport() {
        crudRequest.getPage({})
          .then(res => {
            this.detailsDialogVisible = true;
            this.currentRecord = {ifLocked: 0};
            this.setListData(res);
            this.detailsList = res;
            //动态赋值
            this.setFormDate(false);
          })
      },
      //动态赋值表单数据
      setFormDate(isTick){
        this.$nextTick(() => {
          this.detailsList.forEach((item,index)=>{
            item.groupItems.forEach(row=>{
              if (this.$refs['table' + index][0].isCheckedByCheckboxRow(row)) {
                row.currentMonthIfReported = 1;
              }
            })
          })
          //清空勾选
          this.selections = [];
          this.detailsList.forEach((item,index)=>{
            let refsTable = this.$refs['table' + index][0];
            refsTable.reloadData(item.groupItems).then(() => {
              //赋值初始化勾选的项目
              this.selectionChangeHandler(refsTable);
            })
            //勾上已提交的项目
            if (isTick) {
              this.tickRow(item.groupItems,refsTable);
            }
          })
        })
      },
      tickRow(groupItems,refsTable){
        groupItems.forEach(row=>{
          if (row.ifSubmit === 1) {
            refsTable.toggleRowSelection([
              {
                row: row
              }
            ])
          }
        })
      },
      setListData(res) {
        let code = 0;
        res.map(item => {
          item.groupItems.map(citem => {
            code++;
            citem.code = code;
            if (citem.settlementAuditStatusCd) {
              citem.settlementAuditStatusCd = citem.settlementAuditStatusCd.toString();
            }
            citem.monthlyReportType=citem.monthlyReportType?citem.monthlyReportType.toString():''
          })
        })
      },
      checkboxT(row, rowIndex) {

        return row.ifSubmit == 1
      },
      //详情
      seeDetails(record) {
        this.monthlyReportId = record.id
        crudRequest.getMonthlyReportProjectGroup({
          monthlyReportId: record.id
        })
          .then(res => {
            this.detailsDialogVisible = true;
            this.currentRecord = record;
            this.setListData(res);
            this.detailsList = res;
            //动态赋值
            this.setFormDate(true);
          })
      },
      expandChange(row, expandedRows) {
        if (row.children) return
        crudRequest.getMonthlyReportProjectGroup({
          monthlyReportId: row.id
        })
          .then(res => {
            row.children = res;
          })
      },
      initPageData() {
        crudRequest.getSonOrgList({})
          .then(res => {
            this.sonOrgList = res;
          })
      },
      // 预览文件
      previewAttachment(row) {
        this.reviewFile = row
        const arrFile = row.name.split(".");
        const fileType = arrFile[arrFile.length - 1].toLowerCase();
        const allowFileType = ['pdf', 'docx', 'xlsx', 'xls']
        if (allowFileType.indexOf(fileType) < 0) {
          this.$message.error('暂不支持该文件类型预览')
          return false
        }
        this.showReview = true
      },
      closeReview() {
        this.showReview = false
      },
    },
    mounted() {
      this.initPageData();
    }
  }
</script>
<style scoped>
  .svg-submit{
    width: 2em;
    height: 2em;
    vertical-align: -0.578em;
    margin-left: -4.4px;
  }
  .svg-baocun{
    width: 1.25em;
    height: 1.25em;
    vertical-align: -0.26em;
    margin-right: 2px;
  }
  .annotation {
    position: absolute;
    z-index: 8;
    font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
    font-size: 14px;
  }
  .el-dialog {
    position: relative;
    margin: 0 auto 0px;
    background: #FFFFFF;
    border-radius: 2px;
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 50%;
    height: 60%;
  }
  .el-dialog__body {
    border-top: 1px solid #dcdfe6;
    border-bottom: 1px solid #dcdfe6;
    max-height:85%  !important;
    min-height: 70%;
    overflow-y: auto;
  }

  >>> .elx-table.size--small .elx-body--column.col--ellipsis>.elx-cell{
    max-height: 100px;
  }
</style>
