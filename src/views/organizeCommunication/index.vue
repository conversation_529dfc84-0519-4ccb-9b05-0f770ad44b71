<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.name" clearable size="small" placeholder="输入标题搜索" style="width: 200px;"
                  class="filter-item" @keyup.enter.native="crud.toQuery"/>
        <rrOperation/>
      </div>
      <crudOperation :permission="permission"/>
    </div>
    <!--表单组件-->
    <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU"
               :visible.sync="crud.status.cu > 0" v-if="crud.status.cu > 0" :title="crud.status.title" width="870px">

      <el-form ref="form" :disabled="crud.status.details > 0" :model="form" :rules="rules" size="small"
               label-width="80px">
        <template v-for="(item, key) in formFields">
          <template v-if="item.type == 'radio'">
            <el-form-item label="状态" :prop="item.fieldName" :key="key">
              <el-radio v-for="item in dict.dept_status" :key="item.id" v-model="form.enabled" :label="item.value">{{
                  item.label
                }}
              </el-radio>
            </el-form-item>
          </template>
          <template v-if="item.type == 'input'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-input v-model="form[item.fieldName]"/>
            </el-form-item>
          </template>
          <template v-if="item.type == 'editor'">
            <el-form-item :label="item.label" :prop="item.fieldName">
              <el-radio-group v-model="contentType">
                <el-radio label="text">文本</el-radio>
                <el-radio label="file">文件</el-radio>
              </el-radio-group>
              <template v-if="contentType == 'text'">
                <TinymceEditor ref="editor" :value="form[item.fieldName]" v-model="form[item.fieldName]">
                </TinymceEditor>
              </template>
              <template v-else>
                <el-upload
                    action=""
                    :on-remove="contentHandleRemove"
                    :show-file-list="true"
                    :http-request="contentHttpRequest"
                    :file-list="contentFileList">
                  <el-button size="small" plain>选择文件</el-button>
                </el-upload>
              </template>
            </el-form-item>
          </template>

          <template v-if="item.type == 'select' && item.fieldName == 'statusCd'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]" placeholder="请选择" style="width:100%">
                <el-option
                  v-for="(item) in status"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </template>

          <template v-if="item.type == 'file'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key" style="text-align: left">
              <el-upload
                class="upload-demo"
                action=""
                multiple
                :on-remove="handleRemove"
                :limit="10"
                :show-file-list="true"
                :http-request="httpRequest"
                :file-list="fileList">
                <el-button size="small" plain>选择文件</el-button>
              </el-upload>
            </el-form-item>
          </template>
          <template v-if="item.type == 'date'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-date-picker
                v-model="form[item.fieldName]"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                :style="formInputStyle"
                placeholder="选择日期时间">
              </el-date-picker>
            </el-form-item>
          </template>
          <template v-if="item.type == 'select' && item.fieldName == 'typeCd'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]" placeholder="请选择" style="width:100%">
                <el-option
                  v-for="(item) in dict.announcement_type"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-if="item.type == 'textarea'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-input :type="item.type" v-model="form[item.fieldName]"/>
            </el-form-item>
          </template>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>
    <!--表格渲染-->
    <el-table ref="table" v-loading="crud.loading" :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              default-expand-all :data="crud.data" row-key="id" @select="crud.selectChange"
              @select-all="crud.selectAllChange" @selection-change="crud.selectionChangeHandler" border>
      <el-table-column :selectable="checkboxT" type="selection" width="55"/>
      <template
        v-for="(item, key) in tableListFields">
        <el-table-column :label="item.label" :prop="item.fieldName" :key="key">
          <template v-slot="scope">
            <template v-if="item.fieldName == 'title'">
              <el-button type="text" size="small" @click="toPage(scope.row)">{{ scope.row[item.fieldName] }}</el-button>
            </template>
            <template v-else-if="item.fieldName == 'typeCd'">
              {{ showCurrentData(dict.announcement_type, scope.row[item.fieldName]) }}
            </template>
            <template v-else-if="item.fieldName == 'statusCd'">
              {{ showCurrentData(status, scope.row[item.fieldName]) }}
            </template>
            <template v-else>
              {{ scope.row[item.fieldName] }}
            </template>
          </template>
        </el-table-column>
      </template>

      <el-table-column v-permission="['admin','notice:organize:edit','notice:organize:del','notice:organize:top']"
                       label="操作"
                       width="220px" align="center" fixed="right">
        <template v-slot="scope">
          <div style="display:inline-block;">
            <udOperation
              :data="scope.row"
              :permission="permission"
              :disabledDle="scope.row.id === 1"
              msg="确定删除吗,如果存在下级节点则一并删除，此操作不能撤销！"
            />
          </div>
          <div style="display:inline-block;" v-permission="['admin','notice:organize:top']">
            <el-button size='mini' type="primary" @click="top(scope.row)" plain>
              <span v-show="scope.row.topValue > 0">
                取消置顶
              </span>
              <span v-show="scope.row.topValue === 0">
                置顶
              </span>
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination/>
  </div>
</template>

<script>
import crudRequest from './config/request'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import TinymceEditor from '@/components/tinymce/'  //富文本编辑器
import {upload} from '@/utils/upload'
//注入配置文件
import config from './config/index'

const formFields = config.getFields('form');
const tableListFields = config.getFields('tableList');
const defaultForm = config.getDefaultForm(formFields);

function checkTitle() {
  let path = location.href.split('/')
  path = path[path.length - 1];
  switch (path) {
    case 'organizeCommunication':
      return '企业通知'
    case 'pricingBasis':
      return '计价依据'
    case 'standardSpecification':
      return '标准规范'
  }
}

function checkType() {
  let path = location.href.split('/')
  path = path[path.length - 1];
  switch (path) {
    case 'organizeCommunication':
      return 1
    case 'pricingBasis':
      return 2
    case 'standardSpecification':
      return 3
  }
}


export default {
  name: 'organizeCommunication',
  components: {crudOperation, rrOperation, udOperation, pagination, TinymceEditor},
  cruds() {
    return CRUD({
      title: checkTitle(),
      params: {ifGetReadAnnounce: 1},
      url: config.requestUrl,
      crudMethod: {...crudRequest},
      sort: ['top_value,desc', 'create_date,desc'],
      columnDropIndex: 1,
      query: {type: checkType()}
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 设置数据字典
  dicts: [],
  data() {
    return {
      delFileIdList: [],
      status: [{name: '未发布', code: 1}, {name: '已发布', code: 2}],
      fileList: [],
      loading: false,
      formFields: formFields,
      tableListFields: tableListFields,
      rules: {
        name: [
          {required: true, message: '请输入名称', trigger: 'blur'}
        ]
      },
      permission: {
        add: ['admin', 'notice:announce:add'],
        edit: ['admin', 'notice:announce:edit'],
        del: ['admin', 'notice:announce:del'],
        details: ['admin', 'notice:announce:details']
      },
      type: checkType(),
      path: this.getPath(),
      contentType: 'text'
    }
  },
  methods: {
    top(row) {
      crudRequest.top({id: row.id}).then(res => {
        this.crud.refresh();
      })
    },
    [CRUD.HOOK.beforeToAdd](crud, record) {
      this.contentType = "text"
      this.fileList = [];
      this.contentFileList = [];
    },
    [CRUD.HOOK.beforeToEdit](crud, record) {
      this.fileList = []
      if (record.content.includes('fileName')) {
        this.contentType = 'file'
        const file = JSON.parse(record.content)
        this.contentFileList = [{ name: file.fileName, url: file.url }]
      } else {
        this.contentType = 'text'
      }
      this.getInitData(record)
    },
    [CRUD.HOOK.beforeSubmit](crud, record) {
      this.form.type = this.type
      this.form.fileList = this.fileList;
      if (this.delFileIdList.length > 0) {
        crudRequest.delNotifyFile(this.delFileIdList)
      }
    },
    [CRUD.HOOK.afterSubmit](crud, record) {
      this.delFileIdList = [];
    },
    handleRemove(file, fileList) {
      if (file.id){
        this.delFileIdList.push(file.id)
      }
      this.fileList = fileList;
    },
    contentHandleRemove() {
      this.form.content = ''
    },
    contentHttpRequest(file) {
      const loading = this.$loading({
        lock: true,
        text: '上传文件中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      upload('general/file/upload', file.file)
          .then(res => {
            this.form.content = JSON.stringify({ type: 'file', url: res.data.data, fileName: file.file.name });
            this.contentFileList = [{ type: 'file', url: res.data.data, name: file.file.name }];
            loading.close();
          })
          .catch(e => {
            loading.close();
            this.crud.notify('上传失败！', 'error')
          })
    },
    httpRequest(file) {
      this.loading = true;
      upload('general/file/upload', file.file)
        .then(res => {
          this.fileList.push({
            name: file.file.name,
            url: res.data.data,
            size: this.formatBytes(file.file.size),
            fileModule: 3
          })
          this.loading = false;
        })
        .catch(e => {
          this.loading = false;
          this.crud.notify('上传失败！', 'error')
        })
    },
    checkboxT(row, rowIndex) {
      return row.id !== 1
    },
    getInitData(data) {
      this.fileList = data.fileList
    },
    getPath() {
      let path = location.href.split('/')
      path = path[path.length - 1];
      switch (path) {
        case 'organizeCommunication':
          return '/organizeDetails'
        case 'pricingBasis':
          return '/pricingBasisDetails'
        case 'standardSpecification':
          return '/standardSpecificationDetails'
      }
    },
    toPage(row) {
      let path = this.getPath() + '?id=' + row.id
      this.$router.push(path)
    }
  },

}
</script>
<style scope>
>>> .el-dialog__body {
  padding: 0
}

>>> .w-e-text-container {

  height: 360px !important;
}

.editor {
  text-align: left;
  width: 750px;
}
</style>

