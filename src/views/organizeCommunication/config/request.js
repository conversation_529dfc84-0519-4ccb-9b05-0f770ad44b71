import request from '@/utils/request'
import config from './index'
import qs from "qs";
const requestUrl = config.requestUrl;

export function getPage(params) {
    return request({
        url: requestUrl+'/page'+ '?' + qs.stringify(params, { indices: false }),
        method: 'get'
    })
}

export function add(data) {
    return request({
        url: requestUrl,
        method: 'post',
        data
    })
}


export function getDetails(data) {
    return request({
        url: requestUrl+ '/'+ data.id,
        method: 'get',
        data
    })
}

export function del(ids) {
    return request({
        url: requestUrl,
        method: 'delete',
        data: ids
    })
}

export function edit(data) {
    return request({
        url: requestUrl,
        method: 'put',
        data
    })
}

export function pageByType(data) {
  return request({
    url: requestUrl+'/pageByType',
    method: 'post',
    data
  })
}

export function top(params) {
    return request({
        url: requestUrl+'/top',
        method: 'get',
        params
    })
}

export function delNotifyFile(ids) {
    return request({
        url: '/system/NotifyFile',
        method: 'delete',
        data: ids
    })
}

export function statistical(params) {
    return request({
        url: requestUrl + '/statistical',
        method: 'get',
        params
    })
}

export function getStatistical(params) {
    return request({
        url: requestUrl + '/getStatistical'+ '?' + qs.stringify(params, { indices: false }),
        method: 'get'
    })
}
export default { add, edit, del, getPage, getDetails, pageByType, top, delNotifyFile, statistical, getStatistical}
