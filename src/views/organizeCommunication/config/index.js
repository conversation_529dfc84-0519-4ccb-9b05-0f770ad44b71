export default {
    //获取某类型字段列表
    getFields(type){
        return  this.parames.filter(item=>{
            return item.whereShow.some((citem)=>citem == type)
    })
    },
    //加工页面formData
    getDefaultForm(data){
        var json = {};
        data.map(item=>{
            json[ item.fieldName ] = item.defaultVal;
    })
        return json;
    },
    //页面请求地址
    requestUrl: '/system/organize',

    //页面字段参数，可根据实际需求扩展
    parames: [
        {
            label: '主键ID',
            fieldName: 'id',
            defaultVal: '',
            type: 'input',
            whereShow: []
        },
        {
            label: '标题',
            fieldName: 'title',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit','tableListList']
        },
        {
            label: '内容',
            fieldName: 'content',
            defaultVal: '<p style="box-sizing: inherit; font-family: \'Helvetica Neue\', Helvetica, \'PingFang SC\', \'Hiragino Sans GB\', \'Microsoft YaHei\', Arial, sans-serif; background-color: #ffffff;">各单位：</p>\n' +
                '<p style="box-sizing: inherit; font-family: \'Helvetica Neue\', Helvetica, \'PingFang SC\', \'Hiragino Sans GB\', \'Microsoft YaHei\', Arial, sans-serif; background-color: #ffffff;">&nbsp;</p>\n' +
                '<p style="box-sizing: inherit; font-family: \'Helvetica Neue\', Helvetica, \'PingFang SC\', \'Hiragino Sans GB\', \'Microsoft YaHei\', Arial, sans-serif; background-color: #ffffff;">&nbsp; &nbsp; &nbsp; &nbsp;此处是通知模板，发布通知时可以直接编辑文字即可。如果需要修改，可自行调整字体大小或者颜</p>\n' +
                '<p style="box-sizing: inherit; font-family: \'Helvetica Neue\', Helvetica, \'PingFang SC\', \'Hiragino Sans GB\', \'Microsoft YaHei\', Arial, sans-serif; background-color: #ffffff;">色、段落样式等。此处是通知模板，发布通知时可以直接编辑文字即可。如果需要修改，可自行调整字</p>\n' +
                '<p style="box-sizing: inherit; font-family: \'Helvetica Neue\', Helvetica, \'PingFang SC\', \'Hiragino Sans GB\', \'Microsoft YaHei\', Arial, sans-serif; background-color: #ffffff;">体大小或者颜色、段落样式等。</p>\n' +
                '<p style="box-sizing: inherit; font-family: \'Helvetica Neue\', Helvetica, \'PingFang SC\', \'Hiragino Sans GB\', \'Microsoft YaHei\', Arial, sans-serif; background-color: #ffffff;">&nbsp; &nbsp; &nbsp; 此处是通知模板，发布通知时可以直接编辑文字即可。如果需要修改，可自行调整字体大小或者颜</p>\n' +
                '<p style="box-sizing: inherit; font-family: \'Helvetica Neue\', Helvetica, \'PingFang SC\', \'Hiragino Sans GB\', \'Microsoft YaHei\', Arial, sans-serif; background-color: #ffffff;">色、段落样式等。此处是通知模板，发布通知时可以直接编辑文字即可。如果需要修改，可自行调整字</p>\n' +
                '<p style="box-sizing: inherit; font-family: \'Helvetica Neue\', Helvetica, \'PingFang SC\', \'Hiragino Sans GB\', \'Microsoft YaHei\', Arial, sans-serif; background-color: #ffffff;">体大小或者颜色、段落样式等。</p>\n' +
                '<p style="box-sizing: inherit; font-family: \'Helvetica Neue\', Helvetica, \'PingFang SC\', \'Hiragino Sans GB\', \'Microsoft YaHei\', Arial, sans-serif; background-color: #ffffff;">&nbsp;</p>\n' +
                '<p style="box-sizing: inherit; font-family: \'Helvetica Neue\', Helvetica, \'PingFang SC\', \'Hiragino Sans GB\', \'Microsoft YaHei\', Arial, sans-serif; background-color: #ffffff;">&nbsp;</p>\n' +
                '<p style="box-sizing: inherit; font-family: \'Helvetica Neue\', Helvetica, \'PingFang SC\', \'Hiragino Sans GB\', \'Microsoft YaHei\', Arial, sans-serif; background-color: #ffffff;">&nbsp;</p>\n' +
                '<p style="box-sizing: inherit; font-family: \'Helvetica Neue\', Helvetica, \'PingFang SC\', \'Hiragino Sans GB\', \'Microsoft YaHei\', Arial, sans-serif; background-color: #ffffff;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;20**年**月**日</p>',
            type: 'editor',
            whereShow: ['form', 'add', 'edit']
        },
        {
            label: '附件上传',
            fieldName: 'fileName',
            defaultVal: '',
            type: 'file',
            whereShow: ['form', 'add', 'edit']
        },
        {
            label: '文件地址',
            fieldName: 'fileUri',
            defaultVal: '',
            type: 'fileUri',
            whereShow: ['form', 'add', 'edit']
        },
        {
            label: '发布人',
            fieldName: 'createUserName',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList','tableListList']
        },
        {
            label: '创建时间',
            fieldName: 'createDate',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList','tableListList']
        },

    ]
}
