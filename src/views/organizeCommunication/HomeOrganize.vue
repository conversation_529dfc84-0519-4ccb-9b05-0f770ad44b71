<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <!-- 搜索 -->
      <el-input v-model="query.name" clearable size="small" placeholder="输入标题名称搜索" style="width: 200px;"
                class="filter-item" @keyup.enter.native="crud.toQuery"/>

      <el-button class="filter-item" size="mini" type="success" icon="el-icon-search" @click="toQuery">搜索</el-button>
      <el-button class="filter-item" size="mini" type="warning" icon="el-icon-refresh-left" @click="resetQuery()">重置
      </el-button>
    </div>

    <el-table ref="table" :tree-props="{children: 'children', hasChildren: 'hasChildren'}" default-expand-all
              :data="data.records" row-key="id" border>
      <el-table-column prop="title" label="标题">
        <template v-slot="scope">
          <el-link :underline="false" :href='`/organizeDetails?id=${scope.row.id}`'>{{scope.row.title}}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="updateDate" label="修改时间"></el-table-column>
    </el-table>

    <el-pagination
      :page-size.sync="data.size"
      :total="data.total"
      :current-page.sync="data.current"
      style="margin-top: 8px;"
      layout="total, prev, pager, next, sizes"
      @size-change="sizeChangeHandler"
      @current-change="pageChangeHandler"
    />
  </div>
</template>

<script>
  import crudRequest from "./config/request";

  export default {
    name: "HomeOrganize",
    components: {},
    data() {
      return {
        data: [],
        query: {ifGetReadAnnounce: 1},
      }
    },
    mounted() {
      this.crudGetPage();
    },
    methods: {
      crudGetPage() {
        crudRequest.getPage(this.query).then(res => {
          this.data = res;
        })
      },
      pageChangeHandler(val) {
        this.query.current = val;
        this.crudGetPage();
      },
      sizeChangeHandler(val) {
        this.query.current = 1;
        this.query.size = val;
        this.crudGetPage();
      },
      toQuery() {
        this.crudGetPage();
      },
      resetQuery() {
        this.query = {}
        this.crudGetPage();
      }
    }
  }
</script>

<style scoped>

</style>
