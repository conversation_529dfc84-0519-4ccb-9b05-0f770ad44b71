<template>
  <div class="content">
    <div class="header-title">
      <p class="title"> {{ details.title }} </p>
      <div class="bar-content">
        {{ formatCreateDate(details.createDate) }}&nbsp;·&nbsp;阅读&nbsp;{{
          readingCount
        }}&nbsp;·&nbsp;下载&nbsp;{{ downloadCount }}
      </div>
      <el-divider style="margin: 16px 0"></el-divider>
    </div>
    <template v-if="details.content && details.content.includes('fileName')">
      <div>
        <iframe v-if="fileType==='pdf'" :src="pdfUri+'#toolbar=0'" :style="{height: '1200px', width:'100%'}" ></iframe>
        <div v-else :style="{width:'100%',overflowY:'auto'}">
          <div v-if="fileType==='word'" ref="docFile"/>
          <div v-if="fileType==='excel'" >
            <el-tabs v-model="activeName" type="border-card">
              <el-tab-pane v-for="(item,index) in excelSheet" :key="index" :label="item.name" :name="item.index">
                <div class="table" v-html="item.innerHTML"></div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <div style="margin: auto; width: 100%; overflow: auto">
        <div style="width: 100%" v-html="details.content"></div>
      </div>
    </template>
    <div style="margin: auto;  width: 100%;">
      <br/>
      <div v-if="fileList.length > 0">
        <strong style="font-size: 13px"> 附件: </strong>
      </div>
      <div>
      </div>
      <template v-for="(item, key) in fileList">
        <div style="margin-left: 50px">
          <el-button type="text" @click="downLoad(item)">{{ item.name }}</el-button>
          <el-button type="text" size="mini" @click="previewAttachment(item)" plain style="float: right">预览</el-button>
        </div>
      </template>
    </div>
    <reviewFile
      :reviewFile="reviewFile"
      :open-review="showReview"
      :closeReview="closeReview"
    >
    </reviewFile>
  </div>
</template>

<script>
import {getDetails, statistical, getStatistical} from './config/request'
import request from '@/utils/request'
import {downloadFile} from '@/utils/index'
import moment from "moment";
import reviewFile from "@/components/Review";

const docx = require('docx-preview');
window.JSZip = require('jszip')
const XLSX = require('xlsx');
export default {
  data() {
    return {
      downloadCount: 0,
      readingCount: 0,
      details: {},
      fileList: [],
      reviewFile: {},
      showReview: false,
    }
  },
  components: {
    reviewFile
  },
  watch: {
    fileList(val) {
      //获取阅读下载数量
      this.getStatisticalAll(this.$route.query.id, val);
    }
  },
  methods: {
    previewAttachment(row) {
      this.reviewFile = row
      this.reviewFile.uri = row.url
      const arrFile = row.name.split(".");
      const fileType = arrFile[arrFile.length - 1].toLowerCase();
      const allowFileType = ['pdf', 'docx', 'xlsx', 'xls']
      if (allowFileType.indexOf(fileType) < 0) {
        this.$message.error('暂不支持该文件类型预览')
        return false
      }
      this.showReview = true
    },
    closeReview() {
      this.showReview = false
    },
    getStatisticalAll(noticeId, noticeFileList) {
      let fileIdList = noticeFileList.map(item => item.id);
      getStatistical({noticeId: noticeId, noticeFileIdList: fileIdList}).then(res => {
        this.readingCount = res[noticeId];
        let downCount = 0;
        this.fileList.forEach(item => {
          downCount += res[item.id]?res[item.id]:0;
        })
        this.downloadCount = downCount;
      });
    },
    statisticalAll(noticeId,noticeFileId){
      statistical({noticeId: noticeId, noticeFileId: noticeFileId}).then(item=>{
        //获取阅读下载数量
        this.getStatisticalAll(this.$route.query.id, this.fileList);
      })
    },
    formatCreateDate(date) {
      let year = moment(date).format('YYYY');
      let month = moment(date).format('MM');
      let day = moment(date).format('DD');
      let time = moment(date).format("HH:mm");
      return year + '年' + month + '月' + day + '日 ' + time;
    },
    initView(fileInfo) {
      const arrFile = fileInfo.fileName.split(".");
      const fileType = arrFile[arrFile.length - 1].toLowerCase();
      const allowFileType = ['pdf', 'docx', 'xlsx', 'xls']
      if (allowFileType.indexOf(fileType) < 0) {
        this.$message.error("此类型文件无法预览!")
        this.closeReview()
        return false
      }
      if (fileType === 'pdf') {
        this.fileType = fileType;
        this.pdfUri = process.env.VUE_APP_BASE_FILE + fileInfo.url
      }
      if (fileType === 'docx') {
        this.fileType = 'word';
        request({
          url: '/general/file/download?uri=' +  fileInfo.url,
          type: 'get',
          responseType: 'blob'
        }).then(res => {
          docx.renderAsync(res, this.$refs.docFile)
        })
      }
      if (fileType === 'xlsx' || fileType === 'xls') {
        this.excelSheet = []
        this.fileType = 'excel';
        request({
          url: '/general/file/download?uri=' + fileInfo.url,
          type: 'get',
          responseType: 'arraybuffer'
        }).then(res => {
          let workbook = XLSX.read(new Uint8Array(res), {type: "array"});
          this.activeName = '0'
          workbook.SheetNames.forEach(sheet => {
            const worksheet = workbook.Sheets[sheet];
            if (worksheet) {
              let innerHTML = ''
              try {
                innerHTML = XLSX.utils.sheet_to_html(worksheet);
              } catch (e) {
              }
              this.excelSheet.push({
                name: sheet,
                innerHTML: innerHTML
              });
            }
          })
        })
      }
    },

    initData() {
      getDetails({
        id: this.$route.query.id
      })
          .then(res => {
            this.fileList = res.fileList
            this.details = res;
            this.$nextTick(() => {
              if (this.details.content.includes('fileName')) {
                this.initView(JSON.parse(this.details.content));
              }
            })
          })
    },
    downLoad(item) {
      request({
        url: '/general/file/download?uri=' + item.url,
        type: 'get',
        responseType: 'blob'
      })
          .then(res => {
            downloadFile(res, item.name)
          })
    }
  },
  created() {
    this.initData();
  },
  mounted() {
    this.statisticalAll(this.$route.query.id);
  }
}
</script>
<style scoped>
.header-title {
  line-height: 0.63;
}

.bar-content {
  letter-spacing: 0.3px;
  font-size: 0.80rem;
  color: #8a919f;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  flex-direction: column-reverse;
}

.content {
  width: 95%;
  margin: 0 auto;
  padding-bottom: 50px;
}

.title {
  width: 100%;
  text-align: center;
  padding-top: 20px;
  font-size: 20px;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
}
</style>
