<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <el-form :inline="true" label-position="top">
          <el-form-item label="项目名称">
            <el-input v-model="query.projectName" clearable size="small" placeholder="项目名称"
                      class="filter-item" @change="crud.toQuery"/>
          </el-form-item>
          <el-form-item label="工程类型">
            <el-select v-model="query.projectTypeCd" clearable size="small" placeholder="工程类型"
                       class="filter-item" @change="crud.toQuery">
              <el-option
                v-for="(item) in dict.project_type_new"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="审核状态">
            <el-select v-model="query.auditStatus" clearable size="small" placeholder="工程类型"
                       class="filter-item" @change="crud.toQuery">
              <el-option
                v-for="(item) in dict.audit_status"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="审核性质">
            <el-select v-model="query.auditNatureCd" clearable size="small" placeholder="工程类型"
                       class="filter-item" @change="crud.toQuery">
              <el-option
                v-for="(item) in dict.audit_nature"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="建设单位">
            <el-cascader
              class="filter-item"
              v-model="query.buildOrgCode"
              @change="crud.toQuery"
              :options="options"
              :props="{  multiple: false,
                    value: 'code',
                    label: 'name',
                    children: 'children',
                    disabled: 'ifOrg',
                    checkStrictly: true,
                    emitPath: false}"
              clearable>
            </el-cascader>
          </el-form-item>
          <el-form-item label="报审金额(万元)">
            <el-form-item prop="min">
              <el-input v-model="query.min" class="filter-item" style="width: 120px;" clearable @change="crud.toQuery"/>
            </el-form-item>
            <el-form-item>
              ~
            </el-form-item>
            <el-form-item prop="max">
              <el-input v-model="query.max" class="filter-item" style="width: 120px;" clearable @change="crud.toQuery"/>
            </el-form-item>
          </el-form-item>
          <el-form-item>
            <label slot="label">&nbsp;&nbsp;&nbsp;&nbsp;</label>
            <rrOperation/>
          </el-form-item>
        </el-form>
      </div>
      <crudOperation :permission="permission"/>
    </div>
    <el-dialog :visible.sync="imgPreview" title="实时流程图" width="80%">
      <DrawingPreview :svgData="currentImgData" :imgPreview="imgPreview"/>
    </el-dialog>

    <el-dialog :visible.sync="projectDetails" title="项目详情" width="80%">
      <projectDetails
        :unitDict="unitDict"
        :id="projectAuditId"
        :projectId="projectId"
        :visible="projectDetails"
        :record="auditRecord"
      />
    </el-dialog>

    <!--表单组件-->
    <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU"
               :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="1200px">
      <div v-if="vShowdownTemplate" class="downTemplateDiv">
        <el-button @click="downTemplate" type="primary">下载模板<i class="el-icon-download el-icon--right"></i>
        </el-button>
      </div>
      <span v-if="vShowFileHint" class="downTemplateDiv" style="font-size: 12px;color: red">必传：工程结算报审表，工程结算审批申请报告，结算上报资料目录清单，工程结算书，其它电子版资料</span>
      <el-tabs ref="tabsRef" v-model="proActiveName" type="card">
        <el-tab-pane label="项目信息" name="0">
          <el-form ref="form" :inline="true" :model="form" :rules="rules" size="small" label-width="160px">
            <template v-for="(item, key) in formFields">
              <template v-if="item.type == 'radio'">
                <el-form-item label="状态" :prop="item.fieldName" :key="key">
                  <el-radio v-for="item in dict.dept_status" :key="item.id" v-model="form.enabled" :label="item.value">
                    {{ item.label }}
                  </el-radio>
                </el-form-item>
              </template>
              <template v-if="item.type == 'input' ">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key" :style="item.style">
                  <el-input v-model="form[item.fieldName]" :disabled="item.disabled" :style="formInputStyle"/>
                </el-form-item>
              </template>

              <template v-if="item.type === 'input-number'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-input-number v-model="form[item.fieldName]" :style="formInputStyle" :disabled="item.disabled"
                                   :max=99999999 :controls="false" @input="changeAmount"/>
                </el-form-item>
              </template>

              <template v-if="item.type === 'selectLong' ">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-select v-model="form[item.fieldName]" :style="item.inputStyle"
                             placeholder="请选择" clearable filterable>
                    <el-option
                      v-for="(item) in unitDict"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </template>

              <template v-if="item.type == 'auto' ">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key" :style="item.style">
                  <el-input v-model="form[item.fieldName]" :disabled="item.disabled" :style="item.inputStyle"/>
                  <el-button type="text" @click="tab.dialogVisible=true">查看</el-button>
                </el-form-item>
              </template>
              <template v-if="item.type == 'column'">
                <el-form-item :prop="item.fieldName" :key="key" :style="item.style">
                  <el-alert
                    style="width: 100%;"
                    :title="item.label"
                    type="info"
                    :closable="false">
                  </el-alert>
                </el-form-item>
              </template>
              <template v-if="item.type == 'date'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-date-picker
                    v-model="form[item.fieldName]"
                    value-format="yyyy年MM月dd日 HH:mm:ss"
                    type="date"
                    :disabled="item.disabled"
                    :style="formInputStyle"
                    placeholder="选择日期时间">
                  </el-date-picker>
                </el-form-item>
              </template>
              <template v-if="item.fieldName == 'ifResultBefore'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-select v-model="form[item.fieldName]" :style="formInputStyle"
                             placeholder="请选择">
                    <el-option
                      v-for="(item) in dict.if_result_before"
                      :key="item.code"
                      :disabled="item.disabled"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </template>
              <template v-if="item.fieldName == 'projectName'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-select v-model="form[item.fieldName]" @change="projectListChange" :style="formInputStyle"
                             placeholder="请选择">
                    <el-option
                      :disabled="item.disabled"
                      v-for="(item) in projectList"
                      :key="item.id"
                      :label="item.projectName"
                      :value="item"
                    />
                  </el-select>
                </el-form-item>
                <el-input :style="formInputStyle" placeholder="标段或者单项工程名称"
                          v-model="form.bidOrSingleProjectName"></el-input>
              </template>

              <template v-if="item.type == 'select' && item.fieldName == 'projectTypeCd'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-select v-model="form[item.fieldName]" :style="formInputStyle" placeholder="请选择">
                    <el-option
                      v-for="(item) in dict.engineering_types"
                      :key="item.code"
                      :disabled="item.disabled"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </template>
              <template v-if="item.type == 'select' && item.fieldName == 'projectNatureCd'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-select v-model="form[item.fieldName]" :style="formInputStyle" placeholder="请选择">
                    <el-option
                      v-for="(item) in dict.project_nature"
                      :key="item.code"
                      :disabled="item.disabled"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </template>
              <template v-if="item.type == 'select' && item.fieldName == 'auditNatureCd'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-select v-model="form[item.fieldName]" :style="formInputStyle" placeholder="请选择"
                             @change="changeAuditNatureCd">
                    <el-option
                      v-for="(item) in dict.audit_nature"
                      :key="item.code"
                      :disabled="item.disabled"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </template>
              <template v-if="item.type == 'textarea'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-input :type="item.type" v-model="form[item.fieldName]" :style="item.inputStyle"
                            :disabled="item.disabled"/>
                </el-form-item>
              </template>
            </template>
            <el-form-item label="累计下达投资计划额">
              <el-input v-model="tab.cumulativeInvestmentAmount" :disabled="true" style="width: 171px"></el-input>
            </el-form-item>
          </el-form>
          <el-table
            :data="tab.tableData"
            style="width: 80%;margin: auto" border>
            <el-table-column
              prop="planApprovalDate"
              label="投资计划下达年份"
            >
            </el-table-column>
            <el-table-column
              prop="fundPlanCode"
              label="计划投资编号"
            >
            </el-table-column>
            <el-table-column
              prop="planApprovalAmount"
              label="计划投资额(万元)">
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="附件" name="1">
          <AttachmentsUploadFile
            :fileChage="uploadList"
            :uploadFileData="accessories"
            ref="upload"
          />
        </el-tab-pane>

        <el-tab-pane label="结算数据" name="2">
          <resultUpload
            :fileChage="resultUploadList"
            ref="resultUpload"
            :projectAuditId="projectAuditId"
            :editData="resultFileData"
            :orgTypeCd="3"
            :disabled="false"
            :projectId='projectId'
          />
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>
    <!--表格渲染-->
    <el-tabs type="border-card">
      <el-tab-pane label="审核概览" lazy v-if="user.orgTypeCd!==3">
        <Chart class="chart-wrapper" :dataMap="chartData"/>
      </el-tab-pane>
      <el-tab-pane label="审核列表">
        <el-table ref="table" v-loading="crud.loading" :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                  default-expand-all :data="crud.data" row-key="id" @select="crud.selectChange"
                  @select-all="crud.selectAllChange" @selection-change="crud.selectionChangeHandler" border>
          <el-table-column :selectable="checkboxT" type="selection" width="55"/>
          <template v-for="(item, key) in tableListFields">
            <el-table-column :label="item.label" :prop="item.fieldName" :key="key" :data-value="key" :width="item.width"
                             align="center" show-overflow-tooltip :fixed="item.fixed">
              <template slot-scope="scope">
                <template v-if="item.fieldName === 'auditCode'">
                  <el-button type="text" size="small" @click="seeDetails(scope.row)">
                    {{ scope.row.auditCode }}
                  </el-button>
                </template>
                <template v-else-if="item.fieldName === 'projectTypeCd'">
                  {{ showCurrentData(dict.engineering_types, scope.row[item.fieldName]) }}
                </template>
                <template v-else-if="item.fieldName === 'auditStatus'">
                  <el-tag :type="getTagClass(scope.row.auditStatus)" size="medium">{{ showCurrentData(dict.audit_status, scope.row[item.fieldName]) }}</el-tag>
                </template>
                <template v-else-if="item.fieldName === 'projectNatureCd'">
                  {{ showCurrentData(dict.project_nature, scope.row[item.fieldName]) }}
                </template>
                <template v-else-if="item.fieldName === 'auditNatureCd'">
                  {{ showCurrentData(dict.audit_nature, scope.row[item.fieldName]) }}
                </template>
                <template v-else-if="item.fieldName === 'ifResultBefore'">
                  {{ showCurrentData(dict.if_result_before, scope.row[item.fieldName]) }}
                </template>
                <template v-else>
                  {{ scope.row[item.fieldName] }}
                </template>
              </template>

            </el-table-column>
          </template>

          <el-table-column v-permission="[
                'admin',
                'projectManagement:projectAudit:start',
                'projectManagement:projectAudit:record',
                'projectManagement:projectAudit:edit',
                'projectManagement:projectAudit:details' ,
                'projectManagement:projectAudit:flowChart']" label="操作" width="180px" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button v-permission="['admin', 'projectManagement:projectAudit:start']"
                         v-if="scope.row.processInstanceId === ''" type="text" size="small"
                         @click="startAudit(scope.row.id)">
                启动审核
              </el-button>
              <el-button v-permission="['admin', 'projectManagement:projectAudit:edit']"
                         v-if="scope.row.auditStatus===0||(scope.row.auditStatus===3&&scope.row.createUserId===user.id)"
                         type="text" size="small" @click="crud.toEdit(scope.row)">编辑
              </el-button>
              <el-button v-permission="['admin', 'projectManagement:projectAudit:details']" type="text" size="small"
                         @click="seeDetails(scope.row)">详情
              </el-button>
              <el-button v-if="scope.row.auditStatus===2&&scope.row.projectNatureCd!==1" type="text" size="small"
                         @click="downLoadWord(scope.row)">下载
              </el-button>
              <el-button v-if="scope.row.auditStatus===2&&scope.row.projectNatureCd!==1" type="text" size="small"
                         @click="previewPrint(scope.row)">预览打印
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination/>
      </el-tab-pane>
    </el-tabs>
    <!--结算已完成列表-->
    <el-dialog title="结算列表" :visible.sync="tab.dialogVisible" width="1200px">
      <el-table ref="table" v-loading="crud.loading" default-expand-all :data="tab.resultList" row-key="id" border>
        <template v-for="(item, key) in auditedFiled">
          <el-table-column :label="item.label" :prop="item.fieldName" :key="key" :show-overflow-tooltip="true" fixed>
            <template slot-scope="scope">
              {{ scope.row[item.fieldName] }}
            </template>
          </el-table-column>
        </template>
      </el-table>

    </el-dialog>
    <!--pdf预览与打印-->
    <el-dialog
      title="预览与打印"
      :visible.sync="pdfDialogVisible"
      :fullscreen="true"
    >
      <div style="width: 100%;height: 100%;position:fixed;">
        <iframe :src="pdfUrl" style="width: 100%;height: 100%"></iframe>
      </div>
    </el-dialog>
    <!--批复函函号-->
    <el-dialog
      title="请输入批复函号"
      :visible.sync="editInputVisible"
      width="20%"
    >
      <el-input v-model="letterNub" placeholder="请输入内容"></el-input>
      <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="editLetterNub">确 定</el-button>
          </span>
    </el-dialog>
    <!--选择下载类型-->
    <el-dialog
      title="请输入批复函号"
      :visible.sync="editInputVisible"
      width="20%"
    >
      <el-input v-model="letterNub" placeholder="请输入内容"></el-input>
      <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="editLetterNub">确 定</el-button>
          </span>
    </el-dialog>
    <!--选择pdf类型-->
    <el-dialog
      title="请输入批复函号"
      :visible.sync="checkFileVisible"
      width="20%"
    >
      <el-radio-group v-model="radioFileType">
        <el-radio :label=1>审批表</el-radio>
        <el-radio :label=2>批复函</el-radio>
      </el-radio-group>
      <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="checkFileType">确 定</el-button>
          </span>
    </el-dialog>
  </div>
</template>

<script>
import crudRequest, {getAuditList, getFlowImgByProcessInstanceId} from './config/request'
import {getHistoryTaskPage} from '@/views/workflow/workflow/config/request'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import projectDetails from '@/views/tools/projectDetails'
import PreviewForm from '@/components/PreviewForm'
import AttachmentsUploadFile from '@/components/AttachmentsUploadFile/index'
import resultUpload from '@/components/AttachmentsUploadFile/resultUpload'
import Chart from '@/components/Echarts/ProjectAuditChart'
import unitListRequest from '@/views/qualificationManagement/enterpriseQualification/config/request'
import request from '@/utils/request'
import {getOneByType} from '@/views/fileTemplate/config/request'
import {Message} from 'element-ui'
//注入配置文件
import config from './config/index'
import {getPage, getDetails} from '../projectInfo/config/request'
import {mapGetters} from "vuex";
import DrawingPreview from '@/components/DrawingPreview/index'
import {downloadFile} from '@/utils/index'

const formFields = config.getFields('form');
const tableListFields = config.getFields('tableList');
const auditedFiled = config.getFields('auditedFiled');
const defaultForm = config.getDefaultForm(formFields);
export default {
  name: 'simpleList',
  components: {
    AttachmentsUploadFile,
    crudOperation,
    rrOperation,
    udOperation,
    pagination,
    projectDetails,
    PreviewForm,
    resultUpload,
    DrawingPreview,
    Chart
  },
  cruds() {
    return CRUD({title: '审核', url: config.requestUrl, crudMethod: {...crudRequest}})
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 设置数据字典
  dicts: ['engineering_types', 'project_nature', 'audit_nature', 'if_result_before', 'audit_status', 'project_schedule', 'project_type_new', 'catalogue'],
  data() {
    return {
      //是否显示附件提示
      vShowFileHint: false,
      //是否显示下载模板按钮
      vShowdownTemplate: false,
      //结算附件数据
      resultFileData: {},
      //审核附件
      accessories: [],
      //企业资质
      unitDict: [],
      formInputStyle: 'width:171px',
      auditPageSize: 10,
      auditPage: 1,
      auditRecordsVisible: false,
      auditRecords: {},
      auditRecord: {},
      auditLoading: false,
      proActiveName: '0',
      //流程图
      imgPreview: false,
      projectDetails: false,
      projectAuditId: '',
      projectId: '',
      currentImgData: '',
      projectData: {},
      options: [],
      //项目列表
      projectList: [],
      formFields: formFields,
      tableListFields: tableListFields,
      auditedFiled: auditedFiled,
      //预览打印
      pdfDialogVisible: false,
      rules: {
        constructionAmount: [
          {required: true, message: '请输入工程费', trigger: 'blur'}
        ],
        auditEmFirstAmount: [
          {required: true, message: '请输入甲供设备材料费', trigger: 'blur'}
        ],
        otherAmount: [
          {required: true, message: '请输入其他费', trigger: 'blur'}
        ],
        projectName: [
          {required: true, message: '请选择项目名称', trigger: 'blur'}
        ],
        projectTypeCd: [
          {required: true, message: '请选择工程类型', trigger: 'blur'}
        ],
        projectNatureCd: [
          {required: true, message: '请选择项目性质', trigger: 'blur'}
        ],
        auditNatureCd: [
          {required: true, message: '请选择审核性质', trigger: 'blur'}
        ],
        buildTotalInvestmentAmount: [
          {required: true, message: '请输入报审金额', trigger: 'blur'}
        ],
      },
      permission: {
        add: ['admin', 'projectManagement:projectAudit:add'],
        edit: ['admin', 'projectManagement:projectAudit:edit'],
        del: ['admin', 'projectManagement:projectAudit:del']
      },
      enabledTypeOptions: [
        {key: 'true', display_name: '正常'},
        {key: 'false', display_name: '禁用'}
      ],
      tab: {
        dialogVisible: false,
        cumulativeInvestmentAmount: '',
        tableData: [],
        resultList: [],
      },
      pdfUrl: '',
      chartData: {},
      editInputVisible: false,
      letterNub: '',
      doType: 1,
      radioFileType: 1,
      doRecord: {},
      checkFileVisible: false,
      //组织机构
      org: {}


    }
  },
  computed: {
    ...mapGetters([
      'user',
    ])
  },
  mounted() {
    getPage({
      current: 1,
      size: 100
    })
      .then(res => {
        this.projectList = res.records;
      })
    //获取单位资质信息
    unitListRequest.getUnitDict().then(res => {
      this.unitDict = res;
    })
    crudRequest.getBuildTwoAndThreeLevel().then(res => {
      this.options = this.getTreeData(res)
    })
    //获取图表数据
    crudRequest.getAuditChartData().then(res => {
      this.chartData = res
    })
    //获取组织信息
    crudRequest.getCurrentOrg(this.user.orgCode).then(res => {
      this.org = res
    })
  },
  watch: {
    proActiveName(val) {
      val === "2" ? this.vShowdownTemplate = true : this.vShowdownTemplate = false;
      val === "1" ? this.vShowFileHint = true : this.vShowFileHint = false;
    }
  },
  methods: {
    //下载模板
    downTemplate() {
      //模板类型（1：结算，2：概算）
      getOneByType("1").then(item => {
        request({
          url: '/general/file/download?uri=' + item.templateUrl,
          type: 'get',
          responseType: 'blob'
        })
          .then(res => {
            downloadFile(res, item.templateFileName)
          })
      })
    },
    getAuditPage() {
      this.auditLoading = true;
      getHistoryTaskPage({
        current: this.auditPage,
        size: this.auditPageSize,
        processInstanceId: this.auditRecord.processInstanceId
      })
        .then(res => {
          res.records.map(item => {
            item.formContent = item.formContent && JSON.parse(item.formContent);
            item.formData = item.formData && JSON.parse(item.formData);
          })
          this.auditRecords = res;
          this.auditLoading = false;
        })
        .catch(err => {
          this.auditLoading = false;
        })
    },
    //上传附件
    uploadList(current, list) {
      this.form.accessories = list;
    },
    getSettlementFileData(projectAuditId) {
      crudRequest.getSettlementFileData(projectAuditId).then(res => {
        if (res != null && res.file != null) {
          this.resultFileData = res;
        }
      })
    },
    [CRUD.HOOK.beforeToAdd]() {
      this.$nextTick(() => {
        this.form.projectNatureCd = this.dict['project_nature'][0].code.toString()
      })
    },

    [CRUD.HOOK.beforeToCU]() {
      this.tab.cumulativeInvestmentAmount = '';
      this.tab.tableData = [];
      this.tab.resultList = [];
      this.tab.dialogVisible = false;
      this.proActiveName = '0'
      setTimeout(() => {
        this.$refs.upload.clearFiles();
        this.$refs.resultUpload.clearFiles();
      }, 0)

    },
    [CRUD.HOOK.afterSubmit](crud) {
      setTimeout(() => {
        this.$confirm('是否启动' + crud.form.addResData.auditCode + '审核流程', '提示', {
          confirmButtonText: '启动',
          cancelButtonText: '取消',
          type: 'info'
        }).then(() => {
          this.startAudit(crud.form.addResData.id);
        })
      }, 600)

    },
    [CRUD.HOOK.beforeRefresh]() {
      if (this.query.min && this.query.max) {
        if (this.query.min > this.query.max) {
          this.$message.error("请正确设置金额区间");
        }
      }
    },
    [CRUD.HOOK.beforeValidateCU](crud) {
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          this.proActiveName = "0"
        }
      })
    },
    [CRUD.HOOK.afterValidateCU](data) {
      // let catalogue = this.dict.catalogue.filter(item => ['2', '3', '4', '6', '7'].includes(item.code));
      // let accessories = this.form.accessories;
      // //附件是否上传
      // if (accessories.length <= 0) {
      //   this.proActiveName = '1'
      //   Message({
      //     message: '请上传附件',
      //     type: 'warning'
      //   })
      //   return false;
      // }
      // //是否上传全部类型附件
      // let filter = [...catalogue].filter(x => [...accessories].every(y => x.code !== y.directoriesCd));
      // if (filter.length > 0) {
      //   let unrealized = filter.map((item) => {
      //     return item.name
      //   })
      //   Message({
      //     message: '请上传：' + unrealized,
      //     type: 'warning'
      //   })
      //   return false;
      // }
    },
    [CRUD.HOOK.beforeToEdit](data) {
      if (data.form.auditStatus === 1 || data.form.auditStatus === 1) {
        Message({
          message: '当前状态不可编辑！',
          type: 'warning'
        })
        return false;
      }
      if (data.form.createUserId !== this.user.id) {
        Message({
          message: '非创建人不可编辑！',
          type: 'warning'
        })
        return false;
      }
      this.projectAuditId = data.form.id
      this.numberToString(data.form);
      getDetails({id: data.form.projectId}).then(res => {
        this.projectListChange(res)
      })
      this.getProjectAuditAccessory(data.form);
      //返回结算附件
      this.getSettlementFileData(data.form.id);
    },
    getProjectAuditAccessory(data) {
      //获取审核附件
      crudRequest.getProjectAuditAccessory(data).then(res => {
        this.accessories = res
      })
    },
    numberToString(data) {
      data.projectTypeCd = data.projectTypeCd ? data.projectTypeCd.toString() : data.projectTypeCd;
      data.projectNatureCd = data.projectNatureCd ? data.projectNatureCd.toString() : data.projectNatureCd;
      data.auditNatureCd = data.auditNatureCd ? data.auditNatureCd.toString() : data.auditNatureCd;
      data.ifResultBefore = data.ifResultBefore ? data.ifResultBefore.toString() : data.ifResultBefore;
    },
    //项目选择发生变化
    projectListChange(item) {
      this.projectData = item;
      this.getAuditList(item.id);
      this.form.createUserName = this.user.nickname;
      this.form.createDate = new Date();
      this.form.projectId = item.id;
      this.form.projectName = item.projectName;
      this.form.buildOrgName = item.buildThreeOrgName;
      this.form.submitAuditTime = new Date();
      this.form.plannedInvestmentFinishAnnual = item.plannedInvestmentFinishAnnual;
      this.form.companyInvestmentPlannedDocumentNumber = item.companyInvestmentPlannedDocumentNumber;
      this.form.secondaryInvestmentPlannedDocumentNumber = item.secondaryInvestmentPlannedDocumentNumber;
      this.form.affiliatedCompanyInvestmentPlannedDocumentNumber = item.affiliatedCompanyInvestmentPlannedDocumentNumber;
      this.form.auditInitContext = item.auditInitContext;
      this.form.auditInitCode = item.auditInitCode;
      //概算总额
      this.form.budgetTotalAmount = item.estimateApprovedAmount;
      //批复概算工程费
      this.form.budgetProjectTotalAmount = item.budgetProjectTotalAmount;
      //批复概算他费用
      this.form.budgetOtherTotalAmount = item.budgetOtherTotalAmount;
      //批复概算总抵扣增值税额
      this.form.budgetAddValueTotalAmount = item.budgetAddValueTotalAmount;
      //累计结算
      this.form.totalAmounted = item.settlementAmount;
      //历史投资
      this.tab.tableData = item.projectPlannedInvestments;
      //历史累计投资
      this.tab.cumulativeInvestmentAmount = item.cumulativeInvestmentAmount;
      //施工单位
      this.form.constructionOrgName = item.constructionOrg
      //更新子组件数据
      this.$refs.resultUpload.getBudgetData(item.id);
    },
    // 查看实时流程图
    showFlowImg(row) {
      crudRequest.getFlowImgByProcessInstanceId(row)
        .then(res => {
          this.currentImgData = res;
          this.imgPreview = true;
        })
    },
    //获取结算列表
    getAuditList(projectId) {
      crudRequest.getAuditList(projectId).then(res => {
        if (res) {
          res.forEach(item => {
            item.buildOrgName = this.projectData.buildThreeOrgName;
          })
          this.tab.resultList = res;
        }
      })
    },
    //启动审核
    startAudit(id) {
      crudRequest.startAudit({
        id: id
      })
        .then(res => {
          this.crud.notify('操作成功', 'success');
          this.crud.toQuery();
        }).catch(err => {
      })
    },
    //查看详情
    seeDetails(record) {
      this.projectDetails = true;
      this.projectAuditId = record.id;
      this.projectId = record.projectId;
      this.auditRecord = record;
    },
    checkboxT(row, rowIndex) {
      return row.id !== 1
    },
    resultUploadList(list, sheetList) {
      let data = {
        file: list[0],
        sheetList: sheetList,
        dataType: 0,
        projectId: this.projectId,
      }
      this.form.dto = data;
    },
    // 递归判断列表，把最后的children设为undefined
    getTreeData(data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          // children若为空数组，则将children设为undefined
          data[i].children = undefined;
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          this.getTreeData(data[i].children);
        }
      }
      return data;
    },
    //打印选择类型
    previewPrint(row) {
      this.doRecord = row;
      this.checkFileVisible = true;
      this.doType = 2;
    },
    //下载选择类型
    downLoadWord(row) {
      this.doRecord = row;
      this.checkFileVisible = true;
      this.doType = 1;
    },
    checkFileType() {
      this.checkFileVisible = false;
      if (this.doType === 1) {//下载
        if (this.radioFileType === 1) {//审批表
          crudRequest.downAuditWord(this.doRecord.id).then(res => {
            downloadFile(res, this.doRecord.projectName + '工程结算审批表.docx')
          })
        } else {//批复函
          this.editInputVisible = true;
        }
      } else {//打印
        if (this.radioFileType === 1) {//审批表
          this.pdfDialogVisible = true;
          crudRequest.getAuditPdf(this.doRecord.id).then(res => {
              this.pdfUrl = process.env.VUE_APP_BASE_FILE + res;
            }
          )
        } else {//批复函
          this.editInputVisible = true;
        }
      }
    },
    //获取审核编号
    changeAuditNatureCd(code) {
      crudRequest.getAuditNatureCd(code).then(res => {
        this.form.auditCode = res
      })
    },
    //输入函号
    editLetterNub() {
      if (!this.letterNub || this.letterNub === '') {
        this.$message.error("请输入函号")
        return;
      }
      this.editInputVisible = false;
      if (this.doType === 1) {
        crudRequest.downAuditReplyWord(this.doRecord.id, this.letterNub).then(res => {
          downloadFile(res, this.doRecord.projectName + '工程结算批复函.docx')
        })
      } else {
        this.pdfDialogVisible = true;
        crudRequest.getAuditReplyPdfUri(this.doRecord.id, this.letterNub).then(res => {
          this.pdfUrl = process.env.VUE_APP_BASE_FILE + res;
        })
      }
    },
    changeAmount() {
      if (this.org && this.form.buildTotalInvestmentAmount && this.org.limitAmount) {
        //比较数值大小  如果大于单位资质限额
        if (parseFloat(this.form.buildTotalInvestmentAmount) >= parseFloat(this.org.limitAmount)) {
          this.form.projectNatureCd = '3'
        } else {
          this.form.projectNatureCd = '2'
        }
      }
    },
    getTagClass(status){
      switch (status){
        case 0:
          return "info"
        case 1:
          return "danger"
        case 2:
          return "success"
        case 3:
          return "warning"
      }
    }
  },

}
</script>
<style scoped>
.downTemplateDiv {
  z-index: 999;
  position: relative;
  float: right;
}

/deep/ .el-collapse-item__content {
  padding-bottom: 0;
  font-size: 13px;
  color: #303133;
  line-height: 1.769230769230769;
}

/deep/ .el-form-item--mini.el-form-item {
  margin-bottom: 0;
}
</style>
