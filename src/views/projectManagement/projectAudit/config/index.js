export default {
  //获取某类型字段列表
  getFields(type) {
    return this.parames.filter(item => {
      return item.whereShow.some((citem) => citem == type)
    })
  },
  //加工页面formData
  getDefaultForm(data) {
    var json = {};
    data.map(item => {
      json[item.fieldName] = item.defaultVal;
    })
    return json;
  },
  //页面请求地址
  requestUrl: '/project/projectAudit',

  //页面字段参数，可根据实际需求扩展
  parames: [
    {
      label: '主键ID',
      fieldName: 'id',
      defaultVal: '',
      type: 'input',
      whereShow: ['edit']
    },
    {
      label: '报审编号',
      fieldName: 'auditCode',
      defaultVal: '',
      disabled: true,
      fixed: 'left',
      width: '130px',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '项目名称',
      fieldName: 'projectName',
      defaultVal: '',
      width: '200px',
      type: 'select',
      whereShow: ['tableList','form','add', 'edit','auditedFiled']
    },
    {
      label: '建设单位',
      fieldName: 'buildOrgName',
      defaultVal: '',
      disabled: true,
      width: '200px',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit','auditedFiled']
    },
    {
      label: '状态',
      fieldName: 'auditStatus',
      defaultVal: '',
      type: 'select',
      width: '100px',
      whereShow: ['tableList']
    },
    {
      label: '执行人',
      fieldName: 'executeUser',
      defaultVal: '',
      type: 'select',
      width: '80px',
      whereShow: ['tableList']
    },
    {
      label: '任务名称',
      fieldName: 'executeTaskName',
      defaultVal: '',
      type: 'select',
      whereShow: ['']
    },
    {
      label: '报审时间',
      fieldName: 'createDate',
      defaultVal: '',
      disabled: true,
      type: 'date',
      width: '200px',
      whereShow: ['tableList']
    },
    {
      label: '计划投资',
      fieldName: 'plannedInvestmentAmount',
      defaultVal: '',
      disabled: true,
      type: 'input',
      width: '100px',
      whereShow: ['tableList']
    },
    {
      label: '概算批复金额',
      fieldName: 'estimateApprovedAmount',
      defaultVal: '',
      disabled: true,
      type: 'input',
      width: '100px',
      whereShow: ['tableList']
    },

    {
      label: '报审金额(不含税)',
      fieldName: 'buildTotalInvestmentAmount',
      defaultVal: '',
      inputStyle: 'width:880px;',
      width: '200px',
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '工程类型',
      fieldName: 'projectTypeCd',
      defaultVal: '',
      type: 'select',
      whereShow: ['form', 'add', 'edit']
    },
    {
      label: '项目性质',
      fieldName: 'projectNatureCd',
      defaultVal: '',
      type: 'select',
      whereShow: ['form','add', 'edit']
    },
    {
      label: '工程类型',
      fieldName: 'projectTypeCd',
      defaultVal: '',
      type: 'select',
      width: '100px',
      whereShow: ['tableList']
    },

    {
      label: '审核性质',
      fieldName: 'auditNatureCd',
      defaultVal: '',
      type: 'select',
      whereShow: ['form','add', 'edit']
    },

    {
      label: '二级单位',
      fieldName: 'buildTwoOrgName',
      defaultVal: '',
      disabled: true,
      width: '200px',
      type: 'input',
      whereShow: ['tableList']
    },

    {
      label: ' ',
      fieldName: 'buildOrgCode',
      defaultVal: '',
      type: 'column',
      style: 'display:block;width:100%;background: #f4f4f5',
      whereShow: ['form', 'add', 'edit']
    },
    {
      label: '是否办理过单体结算',
      fieldName: 'ifResultBefore',
      defaultVal: '',
      type: 'select',
      whereShow: [ 'form', 'add', 'edit']
    },
    {
      label: '累计结算金额',
      fieldName: 'totalAmounted',
      defaultVal: '',
      inputStyle: 'width:142px;',
      disabled: true,
      type: 'auto',
      whereShow: [ 'form', 'add', 'edit']
    },

    {
      label: '单体工程名称',
      fieldName: 'projectAuditName',
      defaultVal: '',
      inputStyle: 'width:880px;',
      type: 'inputLong',
      whereShow: [ 'form', 'add', 'edit']
    },

    {
      label: '报审工程内容',
      fieldName: 'projectAuditContext',
      defaultVal: '',
      inputStyle: 'width:880px;',
      type: 'textarea',
      whereShow: [ 'form', 'add', 'edit']
    },

    {
      label: '施工单位名称',
      fieldName: 'constructionOrgName',
      defaultVal: '',
      disabled: true,
      inputStyle: 'width:880px;',
      type: 'inputLong',
      whereShow: [ 'form', 'add', 'edit']
    },
    {
      label: '报审金额(不含税)',
      fieldName: 'buildTotalInvestmentAmount',
      defaultVal: '',
      inputStyle: 'width:880px;',
      type: 'input-number',
      whereShow: [ 'form', 'add', 'edit','auditedFiled']
    },
    {
      label: '工程费(万元)',
      fieldName: 'constructionAmount',
      defaultVal: '',
      type: 'input-number',
      whereShow: [ 'form', 'add', 'edit','auditedFiled']
    },
    {
      label: '甲供设备材料费(万元)',
      fieldName: 'auditEmFirstAmount',
      defaultVal: '',
      type: 'input-number',
      whereShow: [ 'form', 'add', 'edit','auditedFiled']
    },
    {
      label: '其他费(万元)',
      fieldName: 'otherAmount',
      defaultVal: '',
      type: 'input-number',
      whereShow: [ 'form', 'add', 'edit','auditedFiled']
    },
    {
      label: '抵扣增值税额(万元)',
      fieldName: 'auditAddValueAmount',
      defaultVal: '',
      type: 'input-number',
      whereShow: [ 'form', 'add', 'edit','auditedFiled']
    },
    {
      label: '初审外委单位名称',
      fieldName: 'auditInitOrgName',
      defaultVal: '',
      inputStyle: 'width:880px;',
      type: 'selectLong',
      whereShow: [ 'form', 'add', 'edit']
    },
    {
      label: '报审人',
      fieldName: 'createUserName',
      defaultVal: '',
      disabled: true,
      size: 200,
      type: 'input',
      whereShow: ['form', 'add', 'edit']
    },
    {
      label: '报审时间',
      fieldName: 'createDate',
      defaultVal: '',
      disabled: true,
      type: 'date',
      whereShow: ['form', 'add', 'edit']
    },

    {
      label: ' ',
      fieldName: 'buildOrgCode',
      defaultVal: '',
      type: 'column',
      style: 'display:block;width:100%;background: #f4f4f5',
      whereShow: ['form', 'add', 'edit']
    },

    /////////

    {
      label: '初设批复文号',
      fieldName: 'auditInitCode',
      defaultVal: '',
      disabled: true,
      inputStyle: 'width:880px;',
      type: 'input',
      whereShow: [ 'form', 'add', 'edit']
    },

    {
      label: '初设批复内容',
      fieldName: 'auditInitContext',
      defaultVal: '',
      disabled: true,
      inputStyle: 'width:880px;',
      type: 'textarea',
      whereShow: [ 'form', 'add', 'edit']
    },
    {
      label: '概算批复金额',
      fieldName: 'budgetTotalAmount',
      defaultVal: '',
      inputStyle: 'width:880px;',
      disabled: true,
      type: 'input',
      whereShow: [ 'form', 'add', 'edit']
    },
    {
      label: '工程费',
      fieldName: 'budgetProjectTotalAmount',
      defaultVal: '',
      disabled: true,
      type: 'input',
      whereShow: [ 'form', 'add', 'edit']
    },
    {
      label: '其他费',
      fieldName: 'budgetOtherTotalAmount',
      defaultVal: '',
      disabled: true,
      type: 'input',
      whereShow: [ 'form', 'add', 'edit']
    },
    {
      label: '总抵扣增值税额',
      fieldName: 'budgetAddValueTotalAmount',
      defaultVal: '',
      disabled: true,
      type: 'input',
      whereShow: [ 'form', 'add', 'edit']
    },
    {
      label: '一审金额',
      fieldName: 'resultAmountOne',
      defaultVal: '',
      disabled: true,
      type: 'input',
      whereShow: ['auditedFiled','tableList']
    },
    {
      label: '二审金额',
      fieldName: 'resultAmountTwo',
      defaultVal: '',
      disabled: true,
      type: 'input',
      whereShow: ['auditedFiled','tableList']
    },
    {
      label: '审定金额',
      fieldName: 'resultAmount',
      defaultVal: '',
      disabled: true,
      type: 'input',
      whereShow: ['auditedFiled','add', 'edit']
    },
    {
      label: '审定工程费',
      fieldName: 'resultProjectAmount',
      defaultVal: '',
      disabled: true,
      type: 'input',
      whereShow: ['add', 'edit']
    },
    {
      label: '审定甲供设备材料费',
      fieldName: 'resultEmFirstAmount',
      defaultVal: '',
      disabled: true,
      type: 'input',
      whereShow: ['add', 'edit']
    },
    {
      label: '审定其他费',
      fieldName: 'resultOtherAmount',
      defaultVal: '',
      disabled: true,
      type: 'input',
      whereShow: ['add', 'edit']
    },
    {
      label: '审定抵扣增值税额',
      fieldName: 'resultAddValueAmount',
      defaultVal: '',
      disabled: true,
      type: 'input',
      whereShow: [ 'add', 'edit']
    },


  ]
}
