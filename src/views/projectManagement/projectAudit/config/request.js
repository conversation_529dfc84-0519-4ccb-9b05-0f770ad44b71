import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;
import qs from 'qs'
export function getPage(params) {
    return request({
        url: requestUrl+'/page',
        method: 'get',
        params,
        paramsSerializer: params => {
          return qs.stringify(params, { indices: false })
        }
    })
}

export function getQueryPage(params) {
  return request({
    url: requestUrl+'/query-page',
    method: 'get',
    params,
    paramsSerializer: params => {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function add(data) {
    return request({
        url: requestUrl,
        method: 'post',
        data
    })
}

export function del(ids) {
    return request({
        url: requestUrl,
        method: 'delete',
        data: ids
    })
}

export function edit(data) {
    return request({
        url: requestUrl,
        method: 'put',
        data
    })
}

export function startAudit(data) {
    return request({
        url: '/project/projectAudit/start/'+data.id,
        method: 'put'
    })
}

export function getFlowImgByProcessInstanceId(data) {
    return request({
        url: `/workflow/workflowTask/getFlowImgByProcessInstanceId/${data.processInstanceId}`,
        method: 'get',
    })
}

export function getProjectAuditAccessory(data) {
    return request({
        url: `/project/projectAudit/getProjectAuditAccessory/`+data.id,
        method: 'get',
    })
}


export function getEnterpriseQualificationAccessory(data) {
    return request({
        url: '/qualification/enterpriseQualification/getEnterpriseQualificationAccessory/'+data.id,
        method: 'get',
        data
    })
}

export function getAuditList(projectId) {
    return request({
        url: '/project/projectAudit/getAuditList/'+projectId,
        method: 'get',
    })
}

export function getBuildTwoAndThreeLevel(data) {
  return request({
    url: '/project/project/getBuildTwoAndThreeLevel',
    method: 'get',
    data
  })
}

export function getAuditPdf(params) {
  return request({
    url: `/project/projectAudit/getAuditPdfUri/`+params,
    method: 'get',
  })
}

export function getAuditReplyPdfUri(id,nub) {
  return request({
    url: `/project/projectAudit/getAuditReplyPdfUri/`+id+'/'+nub,
    method: 'get',
  })
}


export function downAuditWord(params) {
  return request({
    url: `/project/projectAudit/downAuditWord/`+params,
    method: 'get',
    responseType: 'blob'
  })
}
export function downAuditReplyWord(id,nub) {
  return request({
    url: `/project/projectAudit/downAuditReplyWord/`+id+'/'+nub,
    method: 'get',
    responseType: 'blob'
  })
}

export function getAuditResult(params) {
  return request({
    url: `/project/projectAudit/getAuditResult`,
    method: 'get',
    params,
    paramsSerializer: params => {
      return qs.stringify(params, { indices: false })
    }
  })
}

export function getAuditNatureCd(params) {
  return request({
    url: `/project/projectAudit/getAuditNatureCd/` + params,
    method: 'get',
  })
}

export function getAuditChartData() {
  return request({
    url: '/project/projectAudit/getAuditChartData',
    method: 'get',
  })
}

export function getSettlementFileData(param) {
  return request({
    url: '/project/projectAudit/getSettlementFileData/'+param,
    method: 'get',
  })
}

export function getCurrentOrg(code) {
  return request({
    url: '/user/org/getCurrentOrg/' + code,
    method: 'get',
  })
}





export default {getSettlementFileData,getCurrentOrg, getProjectAuditAccessory, add, edit, del, getPage, startAudit, getFlowImgByProcessInstanceId,getAuditList,getBuildTwoAndThreeLevel,getAuditPdf,getAuditReplyPdfUri,downAuditWord,downAuditReplyWord,getAuditNatureCd,getAuditChartData,getQueryPage}
