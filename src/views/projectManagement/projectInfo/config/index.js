export default {
  //获取某类型字段列表
  getFields(type){
    return  this.parames.filter(item=>{
      return item.whereShow.some((citem)=>citem == type)
    })
  },
  //加工页面formData
  getDefaultForm(data){
    var json = {};
    data.map(item=>{
      json[ item.fieldName ] = item.defaultVal;
    })
    return json;
  },
  //页面请求地址
  requestUrl: '/project/project',

  //页面字段参数，可根据实际需求扩展
  parames: [
    {
      label: '主键ID',
      fieldName: 'id',
      defaultVal: '',
      type: 'input',
      whereShow: ['edit']
    },
    {
      label: '项目名称',
      fieldName: 'projectName',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '项目编号',
      fieldName: 'projectCode',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '建设单位（二级）',
      fieldName: 'buildTwoOrgName',
      defaultVal: '',
      type: 'input',
      whereShow: []
    },
    {
      label: '建设单位（三级）',
      fieldName: 'buildThreeOrgCode',
      defaultVal: '',
      type: 'input',
      whereShow: []
    },
    {
      label: '项目类型',
      fieldName: 'projectTypeCd',
      defaultVal: '',
      type: 'select',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '',
      fieldName: 'buildTwoOrgCode',
      defaultVal: '',
      type: 'input',
      whereShow: []
    },
    {
      label: '建设单位',
      fieldName: 'buildThreeOrgName',
      defaultVal: '',
      inputStyle: 'width:166px;',
      type: 'select',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },

    {
      label: '',
      fieldName: 'buildTwoOrgName',
      defaultVal: '',
      inputStyle: 'width:166px;',
      disabled: true,
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '项目类别',
      fieldName: 'projectCategory1Cd',
      defaultVal: '',
      inputStyle: 'width:166px;',
      type: 'select',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '',
      fieldName: 'projectCategory2Cd',
      defaultVal: '',
      inputStyle: 'width:166px;',
      type: 'select',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: ' 项目投资情况',
      fieldName: 'buildOrgCode',
      defaultVal: '',
      type: 'column',
      style: 'display:block;width:100%;background: #f4f4f5',
      whereShow: ['form', 'add', 'edit']
    },

    {
      label: '项目建设总投资(万元)',
      fieldName: 'buildTotalInvestmentAmount',
      defaultVal: '',
      type: 'input-number',

      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '累计下达投资(万元)',
      fieldName: 'cumulativeInvestmentAmount',
      defaultVal: '',
      type: 'input-number',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },

    {
      label: '1',
      fieldName: 'projectPlannedInvestments',
      defaultVal: [
        {
          "fundPlanCode": "",
          "planApprovalAmount": "",
          "planApprovalDate": ""
        }
      ],
      type: '',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '工程费(万元)',
      fieldName: 'budgetProjectTotalAmount',
      defaultVal: '',
      type: 'input-number',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '甲供设备材料费(万元)',
      fieldName: 'budgetEmFirstAmount',
      defaultVal: '',
      type: 'input-number',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '其他费(万元)',
      fieldName: 'budgetOtherTotalAmount',
      defaultVal: '',
      type: 'input-number',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '总抵扣增值税额(万元)',
      fieldName: 'budgetAddValueTotalAmount',
      defaultVal: '',
      type: 'input-number',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '建设期贷款利息(万元)',
      fieldName: 'constructionLoanInterest',
      defaultVal: '',
      type: 'input-number',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '概算批复时间',
      fieldName: 'estimateApprovedTime',
      defaultVal: '',
      type: 'date',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '初设批复文号',
      fieldName: 'auditInitCode',
      defaultVal: '',
      inputStyle: 'width:880px;',
      type: 'input',
      whereShow: [ 'form', 'add', 'edit']
    },

    {
      label: '初设批复内容',
      fieldName: 'auditInitContext',
      defaultVal: '',
      inputStyle: 'width:880px;',
      type: 'textarea',
      whereShow: [ 'form', 'add', 'edit']
    },
    // {
    //   label: '合同清单价',
    //   fieldName: 'contractListAmount',
    //   defaultVal: '',
    //   type: 'input',
    //   whereShow: ['tableList', 'form', 'add', 'edit']
    // },
    {
      label: '结算信息',
      fieldName: 'resultInformation',
      defaultVal: '',
      type: 'column',
      style: 'display:block;width:100%;background: #f4f4f5',
      whereShow: ['form', 'add', 'edit']
    },
    {
      label: '累计已结算金额(万元)',
      fieldName: 'cumulativeSettledAmount',
      defaultVal: '',
      type: 'input-number',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '结算单编号',
      fieldName: 'statementNumber',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    // {
    //   label: '计划完工日期',
    //   fieldName: 'scheduledCompletionTime',
    //   defaultVal: '',
    //   type: 'date',
    //   whereShow: ['tableList', 'form', 'add', 'edit']
    // },
    // {
    //   label: '合同清单价完成时间',
    //   fieldName: 'contractListAmountCompletionTime',
    //   defaultVal: '',
    //   type: 'date',
    //   whereShow: ['tableList', 'form', 'add', 'edit']
    // },
    {
      label: '结算价完成时间',
      fieldName: 'settlementAmountCompletionTime',
      defaultVal: '',
      disabled:true,
      type: 'date',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '结算价',
      fieldName: 'settlementAmount',
      defaultVal: '',
      disabled:true,
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '累计已结算工程内容',
      fieldName: 'projectAudit',
      defaultVal: '',
      inputStyle: 'width:880px;',
      type: 'textarea',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    // {
    //   label: '计划上报结算日期',
    //   fieldName: 'planReportSettlementTime',
    //   defaultVal: '',
    //   type: 'date',
    //   whereShow: ['tableList', 'form', 'add', 'edit']
    // },

    {
      label: '工程建设情况',
      fieldName: 'buildOrgCode',
      defaultVal: '',
      type: 'column',
      style: 'display:block;width:100%;background: #f4f4f5',
      whereShow: ['form', 'add', 'edit']
    },


    {
      label: '计划开工日期',
      fieldName: 'planStartTime',
      defaultVal: '',
      type: 'date',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },

    {
      label: '计划建成日期',
      fieldName: 'planCompletedTime',
      defaultVal: '',
      type: 'date',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '计划投产日期',
      fieldName: 'planProductionTime',
      defaultVal: '',
      type: 'date',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '实际开工日期',
      fieldName: 'realStartTime',
      defaultVal: '',
      type: 'date',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },

    {
      label: '实际建成日期',
      fieldName: 'realCompletedTime',
      defaultVal: '',
      type: 'date',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },

    {
      label: '实际投产日期',
      fieldName: 'realProductionTime',
      defaultVal: '',
      type: 'date',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '本项目进展情况',
      fieldName: 'progressCd',
      defaultVal: '',
      type: 'select',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '本项目完成总进度',
      fieldName: 'overallProgress',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    // {
    //   label: '工程概况',
    //   fieldName: 'projectProfile',
    //   defaultVal: '',
    //   inputStyle: 'width:880px;',
    //   type: 'textarea',
    //   whereShow: ['tableList', 'form', 'add', 'edit']
    // },
    {
      label: '承包商情况',
      fieldName: 'buildOrgCode',
      defaultVal: '',
      type: 'column',
      style: 'display:block;width:100%;background: #f4f4f5;',
      whereShow: ['form', 'add', 'edit']
    },

    {
      label: '',
      fieldName: 'contractor',
      defaultVal: [{
        "designOrg":"",
        "supervisorOrg":"",
        "constructionOrg":""
      }],
      type: '',
      whereShow: ['form', 'add', 'edit']
    },
    // {
    //   label: '设计单位',
    //   fieldName: 'designOrg',
    //   defaultVal: '',
    //   type: 'input',
    //   whereShow: ['tableList', 'form', 'add', 'edit']
    // },
    // {
    //   label: '监理单位',
    //   fieldName: 'supervisorOrg',
    //   defaultVal: '',
    //   type: 'input',
    //   whereShow: ['tableList', 'form', 'add', 'edit']
    // },
    // {
    //   label: '施工单位',
    //   fieldName: 'constructionOrg',
    //   defaultVal: '',
    //   type: 'input',
    //   whereShow: ['tableList', 'form', 'add', 'edit']
    // },
    {
      label: '数据版本',
      fieldName: 'dataVersion',
      defaultVal: '',
      type: 'input',
      whereShow: []
    },
    {
      label: '建设单位编码（二级单位）',
      fieldName: 'planApprovalDate',
      defaultVal: '',
      other: true,
      type: 'input',
      whereShow: []
    },
    {
      label: '建设单位编码（二级单位）',
      fieldName: 'fundPlanCode',
      defaultVal: '',
      other: true,
      type: 'input',
      whereShow: []
    },{
      label: '建设单位编码（二级单位）',
      fieldName: 'planApprovalAmount',
      defaultVal: '',
      type: 'input',
      other: true,
      whereShow: []
    },{
      label: '创建日期',
      fieldName: 'createDate',
      defaultVal: '',
      type: 'date',
      whereShow: []
    }

  ]
}
