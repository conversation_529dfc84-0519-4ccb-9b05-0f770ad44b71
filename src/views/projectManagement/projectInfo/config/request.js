import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;

export function getPage(params) {
    return request({
        url: requestUrl+'/page',
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: requestUrl,
        method: 'post',
        data
    })
}

export function del(ids) {
    return request({
        url: requestUrl,
        method: 'delete',
        data: ids
    })
}

export function edit(data) {
    return request({
        url: requestUrl,
        method: 'put',
        data
    })
}


export function getDetails(data) {
    return request({
        url: '/project/project/'+data.id,
        method: 'get'
    })
}

export function getEchartsData(params) {
  return request({
    url: '/project/project/getEchartsData',
    method: 'get',
    params
  })
}


export function getProjectPlannedInvestment(data) {
    return request({
        url: `/project/project/getProjectPlannedInvestment/${data.id}`,
        method: 'get',
    })
}

export function getBuildThreeLevelOrg(data) {
    return request({
        url: requestUrl+ '/getBuildThreeLevelOrg',
        method: 'get',
        data
    })
}

export function getBuildThreeOrg(data) {
  return request({
    url: requestUrl+ '/getBuildThreeOrg',
    method: 'get',
    data
  })
}

export function getThreeLevelParentOrgCode(code) {
    return request({
        url: '/project/project/getTwoOrg/'+code,
        method: 'get',
    })
}

export default {getDetails, add, edit, del, getPage, getProjectPlannedInvestment, getBuildThreeLevelOrg, getThreeLevelParentOrgCode,getEchartsData,getBuildThreeOrg}
