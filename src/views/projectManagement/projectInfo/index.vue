<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-if="!searchProjectName" v-model="query.projectNameLike" clearable size="small"
                  placeholder="请输入项目名称搜索"
                  style="width: 240px"
                  class="filter-item" @change="crud.toQuery" @keyup.enter.native="crud.toQuery"/>
        <el-select v-model="query.orgCodeRlike" clearable size="small" placeholder="选择公司搜索"
                   class="filter-item"
                   style="width: 300px"
                   @change="crud.toQuery" filterable>
          <el-option v-for="item in orgCodeDict" :key="item.name" :label="item.label" :value="item.value"/>
        </el-select>

        <el-date-picker
          @change="crud.toQuery"
          class="date-item"
          v-model="queryDate"
          type="monthrange"
          align="left"
          unlink-panels
          range-separator="-"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          :picker-options="pickerOptions"
          :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>

        <span>
          <el-button class="filter-item" size="mini" type="success" icon="el-icon-search"
                     @click="crud.toQuery">搜索</el-button>
          <el-button v-if="crud.optShow.reset" class="filter-item" size="mini" type="warning"
                     icon="el-icon-refresh-left" @click="crud.resetQuery()">重置</el-button>
        </span>
      </div>
    </div>
    <!--表单组件-->
    <el-dialog v-dialogDrag append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU"
               :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="1200px">

      <el-tabs  v-model="activeName" type="card">

        <el-tab-pane label="项目" name="first">
          <el-form ref="form" :disabled="crud.status.details > 0" :model="form" :inline="true" :rules="rules"
                   size="small"
                   label-width="170px">
            <template v-for="(item, key) in formFirstFields">
              <template v-if="item.type == 'radio'">
                <el-form-item label="状态" :prop="item.fieldName" :key="key" :style="formInputStyle">
                  <el-radio v-for="item in dict.dept_status" :key="item.id" v-model="form.enabled" :label="item.value">
                    {{ item.label }}
                  </el-radio>
                </el-form-item>
              </template>
              <template v-if="item.type == 'column'">
                <el-form-item :prop="item.fieldName" :key="key" :style="item.style">
                  <el-alert
                    style="width: 100%;"
                    :title="item.label"
                    type="info"
                    :closable="false">
                  </el-alert>
                </el-form-item>
              </template>
              <template v-if="item.fieldName == 'projectPlannedInvestments'">
                <div v-for="(citem, index) in form[item.fieldName]" :key="index+100"
                     style="background: rgba(229,234,234,0.2);">
                  <template>
                    <el-form-item label="投资计划下达年份"
                                  :prop=" 'projectPlannedInvestments.' + index + '.planApprovalDate'"
                                  :rules="rules.planApprovalDate" :key="index+3" style="padding-top: 18px">
                      <el-date-picker
                        style="width: 171px"
                        v-model="citem['planApprovalDate']"
                        value-format="yyyy"
                        type="year"
                        placeholder="选择年份">
                      </el-date-picker>
                    </el-form-item>
                    <el-form-item label="计划投资编号" :prop=" 'projectPlannedInvestments.' + index + '.fundPlanCode'"
                                  :rules="rules.fundPlanCode" :key="index+1" style="padding-top: 18px">
                      <el-input v-model="citem['fundPlanCode']" style="width:171px"/>
                    </el-form-item>
                    <el-form-item label="计划投资额(万元)"
                                  :prop=" 'projectPlannedInvestments.' + index + '.planApprovalAmount'"
                                  :rules="rules.planApprovalAmount" :key="index+2" style="padding-top: 18px">

                      <el-input v-model="citem['planApprovalAmount']" :style="formInputStyle" :disabled="item.disabled"
                                :maxlength="8"
                                oninput="if(this.value.indexOf('.') > 0){this.value=this.value.slice(0,value.indexOf('.') + 5)}else {this.value=this.value.slice(0,8)}"/>
                    </el-form-item>

                  </template>
                  <el-button v-if="index == 0" type="text" @click="addPlan(form[item.fieldName])">添加</el-button>
                  <el-button v-if="index != 0" type="text" style="color: red"
                             @click="delPlan(form[item.fieldName], citem)">
                    删除
                  </el-button>
                </div>
              </template>
              <template v-if="item.type == 'input' && !item.other">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-input v-model="form[item.fieldName]" :disabled="item.disabled" :style="formInputStyle"/>
                </el-form-item>
              </template>

              <template v-if="item.type === 'input-number'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-input v-model="form[item.fieldName]" :style="formInputStyle" :disabled="item.disabled"
                            :maxlength="13"
                            oninput="if(this.value.indexOf('.') > 0){this.value=this.value.slice(0,value.indexOf('.') + 5)}else {this.value=this.value.slice(0,8)}"/>

                </el-form-item>
              </template>

              <template v-if="item.type == 'textarea'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-input type="textarea" v-model="form[item.fieldName]" :style="item.inputStyle"/>
                </el-form-item>
              </template>

              <template v-if="item.type == 'date'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-date-picker
                    v-model="form[item.fieldName]"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    :style="formInputStyle"
                    placeholder="选择日期时间">
                  </el-date-picker>
                </el-form-item>
              </template>

              <template v-if="item.type == 'select' && item.fieldName == 'projectCategory1Cd'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-select v-model="form[item.fieldName]" placeholder="请选择" :style="item.inputStyle"
                             @change="projectCategory1CdChanged">
                    <el-option
                      v-for="(item) in dict.project_category_one"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </template>

              <template v-if="item.type == 'select' && item.fieldName == 'projectCategory2Cd'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-select v-model="form[item.fieldName]" placeholder="请选择" :style="item.inputStyle"
                             @change="projectCategory2CdChanged">
                    <el-option
                      v-for="(item) in dict.project_types"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </template>

              <template v-if="item.type == 'select' && item.fieldName == 'buildThreeOrgName'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-select
                    v-model="form[item.fieldName]"
                    :style="item.inputStyle"
                    placeholder="请选择"
                    @change="buildThreeLevelOrgChange">
                    <el-option
                      v-for="(item) in buildThreeLevelOrg"
                      :key="item.code"
                      :label="item.name"
                      :value="item"
                    />
                  </el-select>
                </el-form-item>
              </template>
              <template v-if="item.type == 'select' && item.fieldName == 'progressCd'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-select v-model="form[item.fieldName]" :style="formInputStyle" placeholder="请选择">
                    <el-option
                      v-for="(item) in dict.project_schedule"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </template>
              <template v-if="item.type == 'select' && item.fieldName == 'projectTypeCd'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-select v-model="form[item.fieldName]" :style="formInputStyle" placeholder="请选择">
                    <el-option
                      v-for="(item) in dict.project_type_new"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </template>
              <template v-if="item.fieldName == 'contractor'">
                <div style="margin-bottom: 15px">
                  <div v-for="(citem, index) in form[item.fieldName]" :key="index+100"
                       style="background: rgba(229,234,234,0.2)">
                    <template>
                      <el-form-item label="设计单位" :prop=" 'contractor.' + index + '.designOrg'"
                                    :rules="rules.designOrg" :key="index+1" style="padding-top: 12px">
                        <el-input v-model="citem['designOrg']" style="width: 171px"/>
                      </el-form-item>
                      <el-form-item label="监理单位" :prop=" 'contractor.' + index + '.supervisorOrg'"
                                    :rules="rules.supervisorOrg" :key="index+2" style="padding-top: 12px">
                        <el-input v-model="citem['supervisorOrg']" style="width: 171px"/>
                      </el-form-item>

                      <el-form-item label="施工单位" :prop=" 'contractor.' + index + '.constructionOrg'"
                                    :rules="rules.constructionOrg" :key="index+3" style="padding-top: 12px">
                        <el-input v-model="citem['constructionOrg']" style="width: 171px"/>
                      </el-form-item>

                      <el-button v-if="index == 0" type="text" @click="addPlan1(form[item.fieldName])">添加</el-button>
                      <el-button v-if="index != 0" type="text" style="color: red"
                                 @click="delPlan1(form[item.fieldName], citem)">删除
                      </el-button>
                    </template>
                  </div>
                </div>
              </template>
            </template>
          </el-form>
        </el-tab-pane>

        <el-tab-pane :disabled="!dialogBudVisible" label="概算" name="second">
          <el-form ref="form1" :model="form" :rules="rules" size="small" label-width="80px">
            <template v-for="(item, key) in formSecondFields">
              <template v-if="item.type == 'upload'">
                <el-form-item label="概算上传">
                  <el-upload
                    ref="upload"
                    action=""
                    :limit=1
                    :http-request="httpRequest"
                    accept=".xls,.xlsx"
                    :before-upload="beforeUploadFile"
                    :on-error="handleError"
                    :before-remove="uploadBeforeRemove"
                    :file-list="elFileList"
                  >
                    <el-button size="small" plain>选择文件</el-button>
                    <span type="text" class="el-upload__tip" @click.stop="downTemplate">模板<i
                      class="el-icon-download el-icon--right"></i></span>
                  </el-upload>
                </el-form-item>
              </template>
              <template v-if="item.fieldName === 'budgetSheetName' ||item.fieldName === 'otherSheetName'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-select v-model="form[item.fieldName]" :style="formInputStyle" placeholder="请选择">
                    <el-option
                      v-for="item in sheetList"
                      :key="item"
                      :label="item"
                      :value="item"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </template>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="概算详情" name="third">
          <el-table
            :data="budgetDetailTree"
            style="width: 100%;"
            height="600"
            highlight-current-row
            row-key="id"
            border
            :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
            <template v-for="(item, key) in tableDialogFields">
              <el-table-column :label="item.label" :prop="item.fieldName" v-if="item.label==='名称'"
                               header-align="center"
                               align="left" :key="key" :width="item.size" :show-overflow-tooltip="true" fixed>
                <template v-slot="scope">
                  {{
                    item.fieldName === 'createTime' ? parseTime(scope.row[item.fieldName]) : scope.row[
                      item.fieldName]
                  }}
                </template>
              </el-table-column>
              <el-table-column v-if="item.label!=='名称'" :label="item.label" align="center" :prop="item.fieldName"
                               :key="key" :width="item.size">
                <template v-slot="scope">
                  {{ scope.row[item.fieldName] === '0.0000' ? '' : scope.row[item.fieldName] }}
                </template>
              </el-table-column>
            </template>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>
    <!--表格渲染-->
    <el-tabs v-model="tableTabs" type="border-card" @tab-click="tabClick">
      <el-tab-pane label="项目概览" lazy v-if="user.orgTypeCd!==3" name="paneOne">
        <Category class="chart-wrapper"
                  :dataMap="echartsData" v-on:tabsValue="tabsValue"/>
      </el-tab-pane>

      <el-tab-pane label="一、二、三类项目" name="paneTwo">
        <crudOperation v-if="tableTabs==='paneTwo'" :permission="{
                    add: ['admin', 'projectManagement:projectInfo:add'],
                    edit: ['********************'],
                    del: ['********************']
                }"/>
        <el-table ref="table" v-if="tableTabs==='paneTwo'" v-loading="crud.loading"
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                  default-expand-all :data="crud.data" row-key="id" @select="crud.selectChange"
                  @select-all="crud.selectAllChange" @selection-change="crud.selectionChangeHandler" border>
          <el-table-column label="项目名称" prop="projectName" fixed="left">
            <template v-slot="scope">
              <el-button type="text" size="small" @click="crud.toEdit(scope.row)">
                {{ scope.row.projectName }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="创建日期" prop="createDate">
            <template v-slot="scope">
              {{ scope.row.createDate.split(' ')[0] }}
            </template>
          </el-table-column>

          <el-table-column label="项目类别" prop="projectCategory1Cd">
            <template v-slot="scope">
              {{ showCurrentData(dict.project_category_one, scope.row.projectCategory1Cd) }}
            </template>
          </el-table-column>
          <el-table-column label="项目类型" prop="projectTypeCd">
            <template v-slot="scope">
              {{ showCurrentData(dict.project_types, scope.row.projectCategory2Cd) }}
            </template>
          </el-table-column>
          <el-table-column label="建设单位(二级)" prop="buildTwoOrgName"></el-table-column>
          <el-table-column label="项目建设总投资" prop="buildTotalInvestmentAmount"></el-table-column>
          <el-table-column label="累计下达投资" prop="cumulativeInvestmentAmount"></el-table-column>
          <el-table-column label="结算价" prop="settlementAmount"></el-table-column>
          <el-table-column label="建设期贷款利息" prop="constructionLoanInterest"></el-table-column>
          <el-table-column
            v-permission="['admin','projectManagement:projectInfo:edit','projectManagement:projectInfo:del','projectManagement:projectInfo:details']"
            label="操作" width="220px" align="center" fixed="right">
            <template v-slot="scope">
              <div style="display:inline-block;">
                <udOperation
                  :data="scope.row"
                  :permission="scope.row.allowedOperation ? permission : permissionNoOperation"
                  :disabledDle="scope.row.id === 1"
                  msg="确定删除吗,如果存在下级节点则一并删除，此操作不能撤销！"
                />
              </div>
              <div style="display:inline-block;">
                <el-tooltip class="item" effect="dark" content="详情" placement="top-start">
                  <el-button size="mini" type="primary" icon="el-icon-info" @click="showAudit(scope.row)"></el-button>
                </el-tooltip>
              </div>
            </template>

          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination/>
      </el-tab-pane>

      <el-tab-pane label="四类项目" name="paneThree">
        <crudOperation v-if="tableTabs==='paneThree'" :permission="{
                    add: ['admin', 'projectManagement:projectInfo:add'],
                    edit: ['********************'],
                    del: ['********************']
                }"/>
        <el-table v-if="tableTabs==='paneThree'" ref="table" v-loading="crud.loading"
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                  default-expand-all :data="fourData.records" row-key="id" @select="crud.selectChange"
                  @select-all="crud.selectAllChange" @selection-change="crud.selectionChangeHandler" border>
          <el-table-column label="项目名称" prop="projectName" fixed="left">
            <template v-slot="scope">
              <el-button type="text" size="small" @click="crud.toEdit(scope.row)">
                {{ scope.row.projectName }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="创建日期" prop="createDate">
            <template v-slot="scope">
              {{ scope.row.createDate.split(' ')[0] }}
            </template>
          </el-table-column>
          <el-table-column label="项目类别" prop="projectCategory1Cd">
            <template v-slot="scope">
              {{ showCurrentData(dict.project_category_one, scope.row.projectCategory1Cd) }}
            </template>
          </el-table-column>
          <el-table-column label="项目类型" prop="projectTypeCd">
            <template v-slot="scope">
              {{ showCurrentData(dict.project_types, scope.row.projectCategory2Cd) }}
            </template>
          </el-table-column>
          <el-table-column label="建设单位(二级)" prop="buildTwoOrgName"></el-table-column>
          <el-table-column label="项目建设总投资" prop="buildTotalInvestmentAmount"></el-table-column>
          <el-table-column label="累计下达投资" prop="cumulativeInvestmentAmount"></el-table-column>
          <el-table-column label="结算价" prop="settlementAmount"></el-table-column>
          <el-table-column label="建设期贷款利息" prop="constructionLoanInterest"></el-table-column>
          <el-table-column
            v-permission="['admin','projectManagement:projectInfo:edit','projectManagement:projectInfo:del',  'projectManagement:projectInfo:details']"
            label="操作" width="220px" align="center" fixed="right">
            <template v-slot="scope">
              <div style="display:inline-block;">
                <udOperation
                  :data="scope.row"
                  :permission="scope.row.allowedOperation ? permission : permissionNoOperation"
                  :disabledDle="scope.row.id === 1"
                  msg="确定删除吗,如果存在下级节点则一并删除，此操作不能撤销！"
                />
              </div>
              <div style="display:inline-block;">
                <el-tooltip class="item" effect="dark" content="详情" placement="top-start">
                  <el-button size="mini" type="primary" icon="el-icon-info" @click="showAudit(scope.row)"></el-button>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <el-pagination
          :page-size.sync="fourData.size"
          :total="fourData.total"
          :current-page.sync="fourCurrent"
          style="margin-top: 8px;"
          layout="total, prev, pager, next, sizes"
          @size-change="sizeChangeHandler($event)"
          @current-change="pageChangeHandler"
        />
      </el-tab-pane>

      <el-tab-pane label="其他类项目" name="paneFour">
        <crudOperation v-if="tableTabs==='paneFour'" :permission="{
                    add: ['admin', 'projectManagement:projectInfo:add'],
                    edit: ['********************'],
                    del: ['********************']
                }"/>
        <el-table ref="table" v-if="tableTabs==='paneFour'" v-loading="crud.loading"
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                  default-expand-all :data="otherData.records" row-key="id" @select="crud.selectChange"
                  @select-all="crud.selectAllChange" @selection-change="crud.selectionChangeHandler" border>
          <el-table-column label="项目名称" prop="projectName" fixed="left">
            <template v-slot="scope">
              <el-button type="text" size="small" @click="crud.toEdit(scope.row)">
                {{ scope.row.projectName }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="创建日期" prop="createDate">
            <template v-slot="scope">
              {{ scope.row.createDate.split(' ')[0] }}
            </template>
          </el-table-column>
          <el-table-column label="项目类别" prop="projectCategory1Cd">
            <template v-slot="scope">
              {{ showCurrentData(dict.project_category_one, scope.row.projectCategory1Cd) }}
            </template>
          </el-table-column>
          <el-table-column label="项目类型" prop="projectTypeCd">
            <template v-slot="scope">
              {{ showCurrentData(dict.project_types, scope.row.projectCategory2Cd) }}
            </template>
          </el-table-column>
          <el-table-column label="建设单位(二级)" prop="buildTwoOrgName"></el-table-column>
          <el-table-column label="项目建设总投资" prop="buildTotalInvestmentAmount"></el-table-column>
          <el-table-column label="累计下达投资" prop="cumulativeInvestmentAmount"></el-table-column>
          <el-table-column label="结算价" prop="settlementAmount"></el-table-column>
          <el-table-column label="建设期贷款利息" prop="constructionLoanInterest"></el-table-column>
          <el-table-column
            v-permission="['admin','projectManagement:projectInfo:edit','projectManagement:projectInfo:details']"
            label="操作" width="220px" align="center" fixed="right">
            <template v-slot="scope">
              <div style="display:inline-block;">
                <udOperation
                  :data="scope.row"
                  :permission="scope.row.allowedOperation ? permission : permissionNoOperation"
                  :disabledDle="scope.row.id === 1"
                  msg="确定删除吗,如果存在下级节点则一并删除，此操作不能撤销！"
                />
              </div>
              <div style="display:inline-block;">
                <el-tooltip class="item" effect="dark" content="详情" placement="top-start">
                  <el-button size="mini" type="primary" icon="el-icon-info" @click="showAudit(scope.row)"></el-button>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <el-pagination
          :page-size.sync="otherData.size"
          :total="otherData.total"
          :current-page.sync="otherCurrent"
          style="margin-top: 8px;"
          layout="total, prev, pager, next, sizes"
          @size-change="otherSizeChangeHandler($event)"
          @current-change="otherPageChangeHandler"
        />
      </el-tab-pane>
    </el-tabs>
    <!--审核列表-->
    <el-dialog
      title="详情汇总"
      :visible.sync="auditDialogVisible"
      width="80%"
    >
      <el-tabs v-model="budTabs" type="card">
        <el-tab-pane label="结算汇总" name="paneOne">
          <el-table
            :data="resultTabData.records"
            style="width: 100%;"
            highlight-current-row
            row-key="id"
            border
          >
            <el-table-column header-align="center" align="center" label="序号" type="index"></el-table-column>
            <el-table-column header-align="center" align="center" label="报审编号" prop="auditCode"></el-table-column>
            <el-table-column header-align="center" align="center" label="结算性质" prop="auditNatureCd">
              <template v-slot="scope">
                {{ showCurrentData(dict['audit_nature'], scope.row['auditNatureCd']) }}
              </template>
            </el-table-column>
            <el-table-column header-align="center" align="center" label="结算金额"
                             prop="projectTotalPayTwo"></el-table-column>
            <el-table-column header-align="center" align="center" label="工程建设内容"
                             prop="projectAuditContext"></el-table-column>
            <el-table-column header-align="center" align="center" label="审批时间" prop="createDate"></el-table-column>
            <el-table-column
              align="center"
              header-align="center"
              label="操作"
              width="100">
              <template v-slot="scope">
                <el-button @click="" type="text" size="small" @click="resultDetailButton(scope.row.id)">结算详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!--结算详情弹窗-->
          <el-dialog title="结算详情" :visible.sync="tab.resultDialogTableVisible" width="1020px"
                     :append-to-body="true">
            <el-tabs v-model="tab.defaultTab" type="card" @tab-click="dialogHandleClick">
              <el-tab-pane label="安装费" name="0">
                <el-table
                  :data="tab.resultDetailInstallTable"
                  style="width: 100%;"
                  height="600"
                  highlight-current-row
                  row-key="id"
                  border
                  lazy
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
                  <template v-for="(item, key) in tableDialogInstallFields">
                    <el-table-column :label="item.label" :prop="item.fieldName" header-align="center" align="left"
                                     :key="key" :width="item.size" :show-overflow-tooltip="true" :fixed="item.fixed">
                      <template v-for="(item, key) in item.children">
                        <el-table-column :label="item.label" :prop="item.fieldName" align="center" :key="key"
                                         :width="item.size" :show-overflow-tooltip="true">
                          <template v-slot="scope">
                            {{ scope.row[item.fieldName] }}
                          </template>
                        </el-table-column>
                      </template>
                    </el-table-column>
                  </template>
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="建筑费" name="1">
                <el-table
                  :data="tab.resultDetailBuildTable"
                  style="width: 100%;"
                  height="600"
                  highlight-current-row
                  row-key="id"
                  border
                  lazy
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
                  <template v-for="(item, key) in tableDialogBuildFields">
                    <el-table-column :label="item.label" :prop="item.fieldName" header-align="center" align="left"
                                     :key="key" :width="item.size" :show-overflow-tooltip="true" :fixed="item.fixed">
                      <template v-for="(item, key) in item.children">
                        <el-table-column :label="item.label" :prop="item.fieldName" align="center" :key="key"
                                         :width="item.size" :show-overflow-tooltip="true">
                          <template v-slot="scope">
                            {{ scope.row[item.fieldName] }}
                          </template>
                        </el-table-column>
                      </template>
                    </el-table-column>
                  </template>
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="其他费信息" name="2">
                <el-table
                  ref="other"
                  :data="tab.resultOtherDetailData"
                  style="width: 100%;"
                  height="600"
                  highlight-current-row
                  row-key="id"
                  border
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
                  <template v-for="(item, key) in tableOtherDialogFields">
                    <el-table-column :label="item.label" :prop="item.fieldName" :key="key" header-align="center"
                                     align="left" :width="item.size" :show-overflow-tooltip="true" :fixed="item.fixed">
                      <template v-for="(item, key) in item.children">
                        <el-table-column :label="item.label" :prop="item.fieldName" align="center" :key="key"
                                         :width="item.size" :show-overflow-tooltip="true">
                          <template v-slot="scope">
                            {{ scope.row[item.fieldName] }}
                          </template>
                        </el-table-column>
                      </template>
                    </el-table-column>
                  </template>
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="设备(甲供)信息" name="3">
                <el-table
                  :data="tab.resultFirstDetailData"
                  style="width: 100%;"
                  height="600"
                  highlight-current-row
                  row-key="id"
                  border
                  lazy
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
                  <template v-for="(item, key) in firstFields">
                    <el-table-column :label="item.label" :prop="item.fieldName" :key="key" header-align="center"
                                     align="left" :width="item.size" :show-overflow-tooltip="true" :fixed="item.fixed">
                      <template v-for="(item, key) in item.children">
                        <el-table-column :label="item.label" :prop="item.fieldName" header-align="center" align="center"
                                         :key="key" :width="item.size" :show-overflow-tooltip="true">
                          <template v-slot="scope">
                            {{ scope.row[item.fieldName] }}
                          </template>
                        </el-table-column>
                      </template>
                    </el-table-column>
                  </template>
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="设备(乙供)信息" name="4">
                <el-table
                  :data="tab.resultSecondDetailData"
                  style="width: 100%;"
                  height="600"
                  highlight-current-row
                  row-key="id"
                  border
                  lazy
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
                  <template v-for="(item, key) in twoFields">
                    <el-table-column :label="item.label" :prop="item.fieldName" :key="key" align="left"
                                     header-align="center" :width="item.size" :show-overflow-tooltip="true"
                                     :fixed="item.fixed">
                      <template v-for="(item, key) in item.children">
                        <el-table-column :label="item.label" :prop="item.fieldName" align="center" :key="key"
                                         :width="item.size" :show-overflow-tooltip="true">
                          <template v-slot="scope">
                            {{ scope.row[item.fieldName] }}
                          </template>
                        </el-table-column>
                      </template>
                    </el-table-column>
                  </template>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </el-dialog>

          <!--分页-->
          <el-pagination
            :page-size="resultTabData.size"
            :current-page="resultTabData.current"
            layout="prev, pager, next"
            @current-change="summaryPageChange"
            :total="resultTabData.total">
          </el-pagination>
        </el-tab-pane>

        <el-tab-pane label="审核信息" name="paneTwo">
          <el-table ref="table" default-expand-all :data="audit.auditRecords.records" row-key="id" border>
            <template v-for="(item, key) in auditTableListFields">
              <el-table-column :label="item.label" :prop="item.fieldName" :key="key" width="155" align="center">
                <template v-slot="scope">
                  <template v-if="item.fieldName === 'projectTypeCd'">
                    {{ showCurrentData(dict.project_type_new, scope.row[item.fieldName]) }}
                  </template>
                  <template v-if="item.fieldName === 'auditStatus'">
                    {{ showCurrentData(dict.audit_status, scope.row[item.fieldName]) }}
                  </template>
                  <template v-else-if="item.fieldName === 'projectNatureCd'">
                    {{ showCurrentData(dict.project_nature, scope.row[item.fieldName]) }}
                  </template>
                  <template v-else-if="item.fieldName === 'auditNatureCd'">
                    {{ showCurrentData(dict.audit_nature, scope.row[item.fieldName]) }}
                  </template>
                  <template v-else-if="item.fieldName === 'ifResultBefore'">
                    {{ showCurrentData(dict.if_result_before, scope.row[item.fieldName]) }}
                  </template>
                  <template v-else-if="item.fieldName === 'auditCode'">
                    <a @click="showAuditDetail(scope.row)" style="color:blue;cursor:pointer"> {{
                        scope.row[item.fieldName]
                      }}</a>
                  </template>
                  <template v-else>
                    {{ scope.row[item.fieldName] }}
                  </template>
                </template>

              </el-table-column>
            </template>
          </el-table>
          <el-pagination
            :page-size="audit.auditPageSize"
            :current-page="audit.auditPage"
            layout="prev, pager, next"
            @current-change="auditPageChange"
            :total="audit.auditRecords.total">
          </el-pagination>
        </el-tab-pane>
      </el-tabs>

    </el-dialog>
    <!--审核详情-->
    <el-dialog :visible.sync="auditDetail.projectDetailsVisible" title="项目详情" width="80%" destroy-on-close>
      <projectDetails
        :id="auditDetail.projectAuditId"
        :projectId="auditDetail.projectId"
        :visible="false"
        :record="auditDetail.auditRecord"
      />
    </el-dialog>
  </div>
</template>

<script>
import crudDept from './config/request'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import {getToken} from '@/utils/auth'
import {getPage as getAuditPage} from '../projectAudit/config/request'
import projectDetails from '@/views/tools/projectDetails'
import Category from '@/components/Echarts/Category'
import {upload} from '@/utils/upload'
import {getResultDetailTable, getSummary} from '../../compare/result/config/request'
import {getOrgCodeDict} from '@/views/user/org/config/request'
//注入配置文件
import config from './config/index'
import auditConfig from '../projectAudit/config/index'
import budgetConfig from '../budget/config/index'
import ProjectInfo from '@/components/ProjectInfo'
import {mapGetters} from "vuex";
import {getBudgetTree} from "../../compare/budget/config/request";
import configDetail from "../../compare/budgetDetail/config";
import emDetail from "../../compare/resultDetail/config/emIndex";
import otherDetail from "../../compare/resultDetail/config/otherIndex";
import installBuildConfig from "../../compare/resultDetail/config/installBuildIndex";
import moment from "moment";
import {getOneByType} from "../../fileTemplate/config/request";
import request from '@/utils/request'
import {downloadFile} from '@/utils/index'

const formFirstFields = config.getFields('form');
const tableListFields = config.getFields('tableList');
const defaultForm = config.getDefaultForm(formFirstFields);
const auditTableListFields = auditConfig.getFields('tableList');
const tableDialogFields = configDetail.getFields('tableDialog');
const firstFields = emDetail.getFields('firstList');
const tableOtherDialogFields = otherDetail.getFields('tabOtherDialog');
const tableDialogBuildFields = installBuildConfig.getFields('tableBuildDialog');
const tableDialogInstallFields = installBuildConfig.getFields('tableInstallDialog');
const twoFields = emDetail.getFields('twoList');

const formSecondFields = budgetConfig.getFields('form');
export default {
  name: 'simpleList',
  components: {
    Treeselect,
    crudOperation,
    rrOperation,
    udOperation,
    pagination,
    ProjectInfo,
    projectDetails,
    Category
  },
  cruds() {
    return CRUD({
      title: '项目',
      params: {projectCategory2CdIn: '1,2,3'},
      url: config.requestUrl,
      crudMethod: {...crudDept},
      query: {},
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 设置数据字典
  dicts: ['audit_nature', 'project_category_one', 'project_types', 'project_type_new', 'project_schedule', 'project_year', 'engineering_types', 'project_nature', 'audit_nature', 'if_result_before', 'audit_status'],
  computed: {
    ...mapGetters([
      'user',
    ])
  },

  data() {
    return {
      item: +new Date(),
      searchProjectName: false,
      orgCodeDict: [],
      elFileList: [],
      tableTabs: '',
      searchProOrOrgName: '请输入公司名称搜索',
      searchType: true,
      queryDate: [moment().startOf('year').toDate(), moment().endOf('year').toDate()],
      tab: {
        defaultTab: "0",
        resultDialogTableVisible: false,
        resultDetailInstallTable: [],
        resultDetailBuildTable: [],
        resultOtherDetailData: [],
        resultFirstDetailData: [],
        resultSecondDetailData: [],
        openKeys1: [],
        openKeys2: [],
        openKeys3: [],
        openKeys4: [],
        openKeys5: [],
      },
      resultTabData: {},
      budTabs: "paneOne",
      dialogBudVisible: true,
      budgetDetailTree: [],
      dubgetData: {},
      projectList: [],
      sheetList: [],
      activeName: "first",
      auditTableListFields: auditTableListFields,
      formInputStyle: 'width:171px',
      headers: {'Authorization': getToken()},
      dialogActiveName: '0',
      tableDialogFields: tableDialogFields,
      formFirstFields: formFirstFields,
      formSecondFields: formSecondFields,
      tableListFields: tableListFields,
      firstFields: firstFields,
      tableOtherDialogFields: tableOtherDialogFields,
      tableDialogBuildFields: tableDialogBuildFields,
      tableDialogInstallFields: tableDialogInstallFields,
      twoFields: twoFields,

      fourCurrent: 1,
      fourSize: 10,
      fourData: {},
      otherCurrent: 1,
      otherSize: 10,
      otherData: {},
      echartsData: {},
      auditDialogVisible: false,
      sort: 'create_date,desc',
      pickerOptions: {
        shortcuts: [{
          text: '本年',
          onClick(picker) {
            const start = new Date(new Date().getFullYear(), 0);
            const end = new Date(new Date().getFullYear(), 11);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()]);
          }
        }, {
          text: '第一季度',
          onClick(picker) {
            const start = new Date(new Date().getFullYear(), 0);
            const end = new Date(new Date().getFullYear(), 2);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '第二季度',
          onClick(picker) {
            const start = new Date(new Date().getFullYear(), 3);
            const end = new Date(new Date().getFullYear(), 5);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '第三季度',
          onClick(picker) {
            const start = new Date(new Date().getFullYear(), 6);
            const end = new Date(new Date().getFullYear(), 8);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '第四季度',
          onClick(picker) {
            const start = new Date(new Date().getFullYear(), 9);
            const end = new Date(new Date().getFullYear(), 11);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      rules: {
        auditInitCode: [
          {required: false, message: '请输入初设批复文号', trigger: 'blur'}
        ],
        estimateApprovedTime: [
          {required: false, message: '请选择概算批复时间', trigger: 'blur'}
        ],
        uri: [
          {required: false, message: '请上传概算文件', trigger: 'blur'}
        ],
        budgetSheetName: [
          {required: false, message: '请选择总概算表', trigger: 'blur'}
        ],
        otherSheetName: [
          {required: false, message: '请选择其他费', trigger: 'blur'}
        ],
        projectName: [
          {required: true, message: '请输入项目名称', trigger: 'blur'}
        ],
        buildThreeOrgName: [
          {required: true, message: '请选择建设单位（三级单位）', trigger: 'blur'}
        ],
        buildTwoOrgName: [
          {required: true, message: '请选择建设单位（三级单位）', trigger: 'blur'}
        ],
        projectCategory1Cd: [
          {required: true, message: '请选择项目类别', trigger: 'blur'}
        ],
        projectCategory2Cd: [
          {required: true, message: '请选择项目类别', trigger: 'blur'}
        ],
        designOrg: [
          {required: true, message: '请输入设计单位', trigger: 'blur'}
        ],
        supervisorOrg: [
          {required: true, message: '请输入监理单位', trigger: 'blur'}
        ],
        constructionOrg: [
          {required: true, message: '请输入施工单位', trigger: 'blur'}
        ],

        buildTotalInvestmentAmount: [
          {required: true, message: '请输入项目建设总投资', trigger: 'blur'}
        ],
        cumulativeInvestmentAmount: [
          {required: true, message: '请输入累计下达投资', trigger: 'blur'}
        ],
        projectPlannedInvestments: [
          {required: true, message: '请输入累计下达投资1', trigger: 'blur'}
        ],
        //projectPlannedInvestments
        planApprovalDate: [
          {required: true, message: '请选择', trigger: 'blur'}
        ],

        fundPlanCode: [
          {required: true, message: '请输入', trigger: 'blur'}
        ],
        planApprovalAmount: [
          {required: true, message: '请输入', trigger: 'blur'}
        ],
        projectTypeCd: [
          {required: true, message: '请选择项目类型', trigger: 'blur'}
        ]
      },
      audit: {
        projectId: '',
        auditRecords: {},
        auditPageSize: 10,
        auditPage: 1,
      },
      auditDetail: {
        projectDetailsVisible: false,
        projectAuditId: '',
        projectId: '',
        auditRecord: '',
      },
      buildThreeLevelOrg: [],
      permission: {
        add: ['admin', 'projectManagement:projectInfo:add'],
        edit: ['admin', 'projectManagement:projectInfo:edit'],
        del: ['admin', 'projectManagement:projectInfo:del'],
        details: ['admin', 'projectManagement:projectInfo:details']
      },
      permissionNoOperation: {
        add: ['adminaaasadasfds'],
        edit: ['adminaaasadasfds'],
        del: ['adminaaasadasfds'],
        details: ['admin', 'projectManagement:projectInfo:details']
      },
      enabledTypeOptions: [
        {key: 'true', display_name: '正常'},
        {key: 'false', display_name: '禁用'}
      ]
    }
  },
  created() {
    this.initData();
    if (this.user.orgTypeCd == 3) {
      this.tableTabs = 'paneTwo'
    } else {
      this.tableTabs = 'paneOne';
    }
  },
  mounted() {
    //获取所属公司字典
    this.getOrgCodeDictF();
  },
  methods: {
    tabsValue(val, companyName) {
      if (val === '一二三类项目') {
        this.tableTabs = 'paneTwo'
      }
      if (val === '四类项目') {
        this.tableTabs = 'paneThree'
      }
      if (val === '其它项目') {
        this.tableTabs = 'paneFour'
      }
      this.query.orgCodeRlike = companyName.split('|')[1]
      this.crud.toQuery()
    },

    formatQueryDate() {
      if (this.queryDate != null) {
        this.query.queryStartDate = this.queryDate[0]
        this.query.queryEndDate = this.queryDate[1]
      } else {
        this.query.queryStartDate = null
        this.query.queryEndDate = null
      }
    },
    queryEchartsData() {
      crudDept.getEchartsData(this.query)
        .then(res => {
          this.echartsData = res
        })
    },
    tabClick(tab, event) {
      // this.item = + new Date()
      this.$nextTick(() => {
        this.crud.attchTable()
      })
    },
    dialogHandleClick({name}) {
      this.$nextTick(() => {
        this.$refs['other'].doLayout()
      })
    },
    resultDetailButton(resultId) {
      this.tab.resultDialogTableVisible = true;
      getResultDetailTable(resultId).then(res => {
        if (res.resultInstallList && res.resultInstallList.length > 0) {
          this.tab.resultDetailInstallTable = res.resultInstallList;
          res.resultInstallList.forEach(p => {
            this.tab.openKeys1.push(p.id);
          })
        }
        if (res.resultBuildList && res.resultBuildList.length > 0) {
          this.tab.resultDetailBuildTable = res.resultBuildList;
          res.resultBuildList.forEach(p => {
            this.tab.openKeys2.push(p.id);
          })
        }
        if (res.otherList && res.otherList.length > 0) {
          this.tab.resultOtherDetailData = res.otherList;
          res.otherList.forEach(p => {
            this.tab.openKeys3.push(p.id);
          })
        }
        if (res.firstList && res.firstList.length > 0) {
          this.tab.resultFirstDetailData = res.firstList;
          res.firstList.forEach(p => {
            this.tab.openKeys4.push(p.id);
          })
        }
        if (res.secondList && res.secondList.length > 0) {
          this.tab.resultSecondDetailData = res.secondList;
          res.secondList.forEach(p => {
            this.tab.openKeys5.push(p.id);
          })
        }
      })
    },
    summaryPageChange(page) {
      //结算汇总
      getSummary({current: page, projectId: projectId}).then(res => {
        this.resultTabData = res
      })
    },
    //展开详情
    budgetDetailButton(projectId) {
      this.dialogTableVisible = true;
      //概算详情
      getBudgetTree(projectId).then(res => {
        this.budgetDetailTree = res;
      })
      //结算汇总
      getSummary({projectId: projectId}).then(res => {
        this.resultTabData = res
      })
    },
    httpRequest(data) {
      const loading = this.$loading({
        lock: true,
        text: '正在上传，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      upload('compare/budget/upload', data.file)
        .then(res => {
          if (res.data.status === 200) {
            this.crud.notify('上传成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
            this.form.budgetFileName = data.file.name;
            this.form.uri = res.data.data.uri;
            this.sheetList = res.data.data.sheetNames;
          } else {
            this.$message.error(res.data.message);
          }
          loading.close();

        })
        .catch(e => {
          loading.close();
          this.$message.error('上传失败！请稍后重试！');
        })
    },
    // 监听上传失败
    handleError(e, file, fileList) {
      const msg = JSON.parse(e.message)
      this.$notify({
        title: msg.message,
        type: 'error',
        duration: 2500
      })
      this.loading = false
    },
    beforeUploadFile(file) {
      let isLt2M = true
      isLt2M = file.size / 1024 / 1024 < 100
      if (!isLt2M) {
        this.loading = false
        this.$message.error('上传文件大小不能超过 100MB!')
      }
      this.form.name = file.name
      return isLt2M
    },
    selectChanged(item) {
      this.form.projectId = item.id;
      this.form.projectName = item.projectName;
    },
    buildThreeLevelOrgChange(org) {
      crudDept.getThreeLevelParentOrgCode(org.code)
        .then(res => {
          this.form.buildTwoOrgName = res.name
          this.form.buildTwoOrgCode = res.code
        })
      this.form.buildThreeOrgCode = org.code
      this.form.buildThreeOrgName = org.name
    },
    calculation(val) {
      let sum = 0;
      this.form.projectPlannedInvestments.map((item, index) => {
        sum += item.planApprovalAmount ? parseInt(item.planApprovalAmount) : 0
      })
      this.form.cumulativeInvestmentAmount = sum
    },
    //添加计划
    addPlan1(record) {
      record.push({
        "designOrg": "",
        "supervisorOrg": "",
        "constructionOrg": "",
        "id": record.length + 1
      })
    },
    //删除计划
    delPlan1(record, current) {
      this.form.contractor = this.form.contractor.filter(item => item.id != current.id);
    },
    //添加承包商
    addPlan(record) {
      record.push({
        "fundPlanCode": "",
        "planApprovalAmount": "",
        "planApprovalDate": "",
        "id": record.length + 1
      })
    },
    //删除承包商
    delPlan(record, current) {
      this.form.projectPlannedInvestments = this.form.projectPlannedInvestments.filter(item => item.id != current.id);
      this.calculation();
    },
    resetFormData(curd) {
      curd.resetForm({
        projectCategory1Cd: '',
        projectCategory2Cd: '',
        fundPlanCode: '',
        planApprovalDate: '',
        planApprovalAmount: '',
        buildThreeOrgName: '',
        buildTwoOrgCode: '',
        buildTwoOrgName: '',
        buildThreeOrgCode: '',
      });
    },
    projectCategory1CdChanged(val) {
      if (this.crud.status.add === 1) {
        if (['1'].includes(val) && ['1', '2'].includes(this.form.projectCategory2Cd)) {
          this.estimateRole(1, true)
        } else {
          this.estimateRole(1, false)
        }
      }
      if (['1'].includes(val) && ['1', '2', '3', '4'].includes(this.form.projectCategory2Cd)) {
        this.magicField('初设批复内容')
        this.estimateRole(2, true)
      } else {
        this.magicField('建设内容')
        this.estimateRole(2, false)
      }

      if (val === '1') {
        this.killRule(['buildTotalInvestmentAmount', 'cumulativeInvestmentAmount', 'planApprovalDate', 'fundPlanCode', 'planApprovalAmount'], true);
      } else {
        this.killRule(['buildTotalInvestmentAmount', 'cumulativeInvestmentAmount', 'planApprovalDate', 'fundPlanCode', 'planApprovalAmount'], false);
      }
    },
    projectCategory2CdChanged(val) {
      if (this.crud.status.add === 1) {
        if (['1', '2'].includes(val) && ['1'].includes(this.form.projectCategory1Cd)) {
          this.estimateRole(1, true)
        } else {
          this.estimateRole(1, false)
        }
      }
      if (['1', '2', '3', '4'].includes(val) && ['1'].includes(this.form.projectCategory1Cd)) {
        this.magicField('初设批复内容')
        this.estimateRole(2, true)
      } else {
        this.magicField('建设内容')
        this.estimateRole(2, false)
      }
    },
    //动态修字段名称
    magicField(fieldName) {
      this.formFirstFields.forEach(e => {
        if (e.fieldName === 'auditInitContext') {
          e.label = fieldName
        }
      })
    },
    //动态修改必填字段
    estimateRole(codename, required) {
      let roles = [];
      switch (codename) {
        case 1:
          roles = ['uri', 'budgetSheetName', 'otherSheetName'];
          break;
        case 2:
          roles = ['estimateApprovedTime', 'auditInitCode'];
          break;
      }
      this.killRule(roles, required);
    },
    killRule(rules, required) {
      rules.forEach(e => {
        this.rules[e].forEach(item => {
          item.required = required
        })
      })
    },
    [CRUD.HOOK.afterToAdd]() {
      this.dialogBudVisible = true
      this.estimateRole(1, false)
    },
    [CRUD.HOOK.beforeRefresh](curd) {
      this.formatQueryDate();
      this.queryEchartsData()
      this.sizeChangeHandler(10)
      this.otherSizeChangeHandler(10)
    },
    [CRUD.HOOK.afterEditCancel](curd) {
      this.sizeChangeHandler(10)
      this.otherSizeChangeHandler(10)
    },
    [CRUD.HOOK.beforeToAdd](curd) {
      this.resetFormData(curd)
      this.elFileList = []
    },
    [CRUD.HOOK.beforeToEdit](crud, form) {
      form.projectCategory1Cd = form.projectCategory1Cd ? form.projectCategory1Cd.toString() : ''
      form.projectCategory2Cd = form.projectCategory2Cd ? form.projectCategory2Cd.toString() : ''
      form.projectTypeCd = form.projectTypeCd ? form.projectTypeCd.toString() : ''
      this.activeName = "first"
      this.elFileList = []
      //概算详情
      getBudgetTree(form.id).then(res => {
        this.budgetDetailTree = res;
        if (res.length > 0) {
          //如果存在数据概算pane不可编辑
          this.dialogBudVisible = false
        } else {
          this.dialogBudVisible = true
        }
      })
      crudDept.getDetails({id: form.id}).then(res => {
          if (res.projectPlannedInvestments.length > 0) {
            form.planApprovalDate = res.projectPlannedInvestments[0].planApprovalDate;
            form.fundPlanCode = res.projectPlannedInvestments[0].fundPlanCode;
            form.planApprovalAmount = res.projectPlannedInvestments[0].planApprovalAmount;
          }
        })
      this.projectCategory1CdChanged(form.projectCategory1Cd);
      this.projectCategory2CdChanged(form.projectCategory2Cd);
    },
    [CRUD.HOOK.beforeAddCancel]() {
      this.uploadBeforeRemove()
    },
    [CRUD.HOOK.afterSubmit]() {
      this.uploadBeforeRemove()
    },
    [CRUD.HOOK.afterToEdit]() {
      this.estimateRole(1, false)
    },
    [CRUD.HOOK.beforeValidateCU]() {
      let rt = false;
      if (this.crud.status.add === 1) {
        this.$refs['form1'].validateField(['uri', 'budgetSheetName', 'otherSheetName'], (valid) => {
          if (valid) {
            this.activeName = 'second';
          } else {
            rt = true;
            return false;
          }
        });
      } else {
        rt = true;
      }
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          this.activeName = "first"
        }
      })
      return rt;
    },
    //删除概算文件钩子
    uploadBeforeRemove(file, fileList) {
      this.form.budgetFileName = '';
      this.form.uri = '';
      this.form.budgetSheetName = '';
      this.form.otherSheetName = '';
      this.sheetList = [];
    },
    checkboxT(row, rowIndex) {
      return row.id !== 1
    },
    getPageData(data, key) {
      data.yearEq = this.query.yearEq || '';
      data.queryStartDate = this.query.queryStartDate
      data.queryEndDate = this.query.queryEndDate
      crudDept.getPage(data)
        .then(res => {
          this[key] = res;
          res.records.map(item => {
            this.crud.crudoOperationData[item.id] = {
              delete: 0,
              edit: 0
            }
          })
        })
    },
    sizeChangeHandler(ev) {
      this.fourCurrent = 1;
      this.fourSize = ev;
      this.getPageData({
        current: this.fourCurrent,
        size: this.fourSize,
        sort: this.sort,
        projectCategory2CdIn: '4',
        orgCodeRlike: this.query.orgCodeRlike,
        projectNameLike: this.query.projectNameLike
      }, 'fourData')
    },
    pageChangeHandler(page) {
      this.fourCurrent = page;
      this.getPageData({
        current: this.fourCurrent,
        size: this.fourSize,
        sort: this.sort,
        orgCodeRlike: this.query.orgCodeRlike,
        projectCategory2CdIn: '4'
      }, 'fourData')
    },
    otherSizeChangeHandler(ev) {
      this.otherCurrent = 1;
      this.otherSize = ev;
      this.getPageData({
        current: this.otherCurrent,
        size: this.otherSize,
        sort: this.sort,
        projectCategory2CdIn: '5',
        orgCodeRlike: this.query.orgCodeRlike,
        projectNameLike: this.query.projectNameLike
      }, 'otherData')
    },
    otherPageChangeHandler(page) {
      this.otherCurrent = page;
      this.getPageData({
        current: this.otherCurrent,
        size: this.otherSize,
        sort: this.sort,
        projectCategory2CdIn: '5',
        orgCodeRlike: this.query.orgCodeRlike,
        projectNameLike: this.query.projectNameLike
      }, 'otherData')
    },
    initData() {
      this.getPageData({
        current: this.fourCurrent,
        size: this.fourSize,
        sort: this.sort,
        projectCategory2CdIn: '4'
      }, 'fourData')
      this.getPageData({
        current: this.otherCurrent,
        size: this.otherSize,
        sort: this.sort,
        projectCategory2CdIn: '5'
      }, 'otherData')

      crudDept.getBuildThreeOrg({})
        .then(res => {
          this.buildThreeLevelOrg = res;
        })
      this.queryEchartsData();
    },
    showAudit(row) {
      this.auditDialogVisible = true
      this.audit.projectId = row.id;
      this.getAuditPage();
      //结算汇总
      getSummary({projectId: row.id}).then(res => {
        this.resultTabData = res
      })
    },
    auditPageChange(val) {
      this.audit.auditPage = val;
      this.getAuditPage();
    },
    getAuditPage() {
      //获取审核列表
      getAuditPage({
        current: this.audit.auditPage,
        size: this.audit.auditPageSize,
        projectId: this.audit.projectId
      })
        .then(res => {
          this.audit.auditRecords = res
        })
    },
    showAuditDetail(row) {
      this.auditDetail.projectDetailsVisible = true;
      this.auditDetail.projectAuditId = row.id;
      this.auditDetail.projectId = row.projectId;
      this.auditDetail.auditRecord = row;
    },
    getOrgCodeDictF() {
      getOrgCodeDict().then(res => {
        this.orgCodeDict = res
      })
    },
    downTemplate() {
      //模板类型（1：结算，2：概算）
      getOneByType("2").then(item => {
        request({
          url: '/general/file/download?uri=' + item.templateUrl,
          type: 'get',
          responseType: 'blob'
        })
          .then(res => {
            downloadFile(res, item.templateFileName)
          })
      })
    },
  },
  watch: {
    deep: true,
    immediate: true,
    tableTabs(val) {
      switch (val) {
        case 'paneOne':
          this.searchProjectName = true
          break
        case 'paneTwo':
        case 'paneThree':
        case 'paneFour':
          this.searchProjectName = false
          break
      }
    }
  },

}
</script>

<style scoped>
.el-dialog {
  max-height: 600px;
  display: flex;
  flex-direction: column;
}

.el-upload__tip {
  margin-left: 10px;
  font-size: 10px;
  color: #1890ff;
  margin-top: 0;
}

.el-dialog__body {
  overflow: auto;
}

>>> .el-alert__title {
  color: #1890ff;
  font-size: 17px;
  font-weight: bold;
}

>>> .el-range-editor--mini.el-input__inner {
  height: 32px;
}
</style>
