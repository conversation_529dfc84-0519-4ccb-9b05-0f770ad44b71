export default {
  //获取某类型字段列表
  getFields(type) {
    return this.parames.filter(item => {
      return item.whereShow.some((citem) => citem == type)
    })
  },
  //加工页面formData
  getDefaultForm(data) {
    var json = {};
    data.map(item => {
      json[item.fieldName] = item.defaultVal;
    })
    return json;
  },
  //页面请求地址
  requestUrl: '/compare/budget',
  //项目地址
  porjectUrl: '/project/projectAudit',
  //页面字段参数，可根据实际需求扩展
  parames: [
    {
      label: '项目名称',
      fieldName: 'projectName',
      defaultVal: '',
      type: 'select',
      fixed: 'left',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },

    {
      label: '文件名称',
      fieldName: 'uri',
      defaultVal: '',
      type: 'upload',
      whereShow: ['form', 'add', 'edit']
    },

    {
      label: '文件名称',
      fieldName: 'fileName',
      defaultVal: '',
      type: 'upload',
      whereShow: ['tableList']
    },

    {
      label: '总概算表',
      fieldName: 'budgetSheetName',
      defaultVal: '',
      type: 'select',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '其他费表',
      fieldName: 'otherSheetName',
      defaultVal: '',
      type: 'select',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '创建人',
      fieldName: 'createUserName',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '创建时间',
      fieldName: 'createDate',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    }
  ]
}
