<template>
  <div class="dashboard-container">
    <myMarquee @click.native="myMarqueeClick" v-if="marquee"
               :sendVal="systemDynamics"></myMarquee>
    <div class="dashboard-editor-container">
      <el-row :gutter="10" style="margin-bottom:10px">
        <el-col :xs="24" :sm="12" :lg="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <img class="cardIcon" src="@/assets/images/homeCard1.png" alt="404">
              <span>待办任务</span>
              <el-button @click.native="$router.push('/myTask/myAgendaTask')" style="float: right; padding: 3px 0"
                         type="text">更多
              </el-button>
            </div>
            <el-empty description="暂无数据" :image-size="40"
                      v-if="myAgendaTask.records === undefined || myAgendaTask.records.length < 1"></el-empty>
            <div v-for="(item, index) in myAgendaTask.records" :key="index" class="text item">
              <el-link :underline="false" :href='`/myTask/myAgendaTask`'>
                {{ item.processInstanceName }}
              </el-link>
              <span style="float: right">{{ item.createdDate }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <img class="cardIcon" src="@/assets/images/homeCard2.png" alt="404">
              <span>政策通知</span>
              <el-button @click.native="$router.push('/costInformation/announce')" style="float: right; padding: 3px 0"
                         type="text">更多
              </el-button>
            </div>
            <el-empty description="暂无数据" :image-size="40"
                      v-if="announce.records === undefined || announce.records.length < 1"></el-empty>
            <div v-for="(item, index) in announce.records" :key="index" class="text item">
              <el-link :underline="false" :href='`/announceDetails/announceDetails?id=${item.id}`'>
                  <span class="linkStyle" style="width:600px;">
                    <span v-if="item.topValue > 0">
                       <svg-icon class="top-icon" icon-class="top1"/>
                    </span>
                    {{ item.title }}</span>
              </el-link>
              <span style="float: right;">
                {{ item.updateDate }}
              </span>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="10" style="margin-bottom:10px">
        <el-col :xs="24" :sm="12" :lg="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <img class="cardIcon" src="@/assets/images/homeCard4.png" alt="404">
              <span>企业通知</span>
              <el-button @click.native="$router.push('/costInformation/organizeCommunication')"
                         style="float: right; padding: 3px 0" type="text">更多
              </el-button>
            </div>
            <el-empty description="暂无数据" :image-size="40"
                      v-if="organize.records === undefined || organize.records.length < 1"></el-empty>
            <div v-for="(item, index) in organize.records" :key="index" class="text item">
              <el-link :underline="false" :href='`/organizeDetails?id=${item.id}`'>
                <span style="width:600px" class="linkStyle">
                    <span v-if="item.topValue > 0">
                       <svg-icon class="top-icon" icon-class="top1"/>
                    </span>
                  {{ item.title }}
                </span>
              </el-link>
              <span style="float: right">
                {{ item.updateDate }}
              </span>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <img class="cardIcon" src="@/assets/images/homeCard5.png" alt="404">
              <span>计价依据</span>
              <el-button @click.native="$router.push('/costInformation/pricingBasis')"
                         style="float: right; padding: 3px 0" type="text">更多
              </el-button>
            </div>
            <el-empty description="暂无数据" :image-size="40"
                      v-if="valuationList.records === undefined || valuationList.records.length < 1"></el-empty>
            <div v-for="(item, index) in valuationList.records" :key="index" class="text item">
              <el-link :underline="false" :href='`/pricingBasisDetails?id=${item.id}`'>
                  <span class="linkStyle" style="width:600px;">
                    <span v-if="item.topValue > 0">
                       <svg-icon class="top-icon" icon-class="top1"/>
                    </span>{{ item.title }}</span>
              </el-link>
              <span style="float: right">
                {{ item.updateDate }}
              </span>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="10" style="margin-bottom:10px">
        <el-col :xs="24" :sm="12" :lg="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <img class="cardIcon" src="@/assets/images/homeCard6.png" alt="404">
              <span>标准规范</span>
              <el-button @click.native="$router.push('/costInformation/standardSpecification')"
                         style="float: right; padding: 3px 0" type="text">更多
              </el-button>
            </div>
            <el-empty description="暂无数据" :image-size="40"
                      v-if="standardList.records === undefined || standardList.records.length < 1"></el-empty>
            <div v-for="(item, index) in standardList.records" :key="index" class="text item">
              <el-link :underline="false" :href='`/standardSpecificationDetails?id=${item.id}`'>
                <span style="width:600px" class="linkStyle">
                    <span v-if="item.topValue > 0">
                       <svg-icon class="top-icon" icon-class="top1"/>
                    </span>
                  {{ item.title }}
                </span>
              </el-link>
              <span style="float: right">
                {{ item.updateDate }}
              </span>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <img class="cardIcon" src="@/assets/images/homeCard3.png" alt="404">
              <span>学习园地</span>
              <el-button @click.native="$router.push('/costInformation/learningField')"
                         style="float: right; padding: 3px 0" type="text">更多
              </el-button>
            </div>
            <el-empty description="暂无数据" :image-size="40"
                      v-if="learningField.records === undefined || learningField.records.length < 1"></el-empty>
            <div v-for="(item, index) in learningField.records" :key="index" class="text item">
              <el-link :underline="false" :href='`/learningField/learningFieldDetails?id=${item.id}`'>
                  <span class="linkStyle" style="width:600px;">
                    <span v-if="item.topValue > 0">
                       <svg-icon class="top-icon" icon-class="top1"/>
                    </span>
                    {{ item.title }}</span>
              </el-link>
              <span style="float: right">
                {{ item.updateDate }}
              </span>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="10" style="margin-bottom:4px">
        <el-switch
          active-text="展示图表"
          v-model="showChart"
          style="float: right;margin-bottom: 10px;margin-right: 5px"
          active-color="#13ce66"
          inactive-color="#ff4949"
        >
        </el-switch>
        <el-col :xs="48" :sm="24" :lg="24">
          <el-card style="height: 320px"  v-if="showChart">
            <schart ref="bar" class="schart" canvasId="bar" :options="options"></schart>
          </el-card>
        </el-col>
      </el-row>

    </div>

    <el-dialog
      title="修改密码"
      :visible.sync="dialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :show-close="false"
      width="30%"
    >
      <el-form ref="form" :model="userPass" label-width="120px" :rules="resetRules">
        <el-form-item label="原密码" prop="oldPass">
          <el-input show-password v-model="userPass.oldPass" auto-complete="off" @blur="checkOldPass"
                    style="width: 80%"></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPass">
          <el-input show-password v-model="userPass.newPass" auto-complete="off" @blur="checkNewPass"
                    style="width: 80%"></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="newPass">
          <el-input show-password v-model="userPass.newPassTwo" auto-complete="off" @blur="checkNewPass"
                    style="width: 80%"></el-input>
          <div v-if="point" style="font-size: 12px;line-height: 1;padding-top: 10px">
            密码至少包含大写字母、小写字母、数字、特殊字符!@#$%^&*()，且不少于12位
          </div>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="info" @click="cancelPass" plain>取消</el-button>
        <el-button type="primary" @click="commitUpdatePassword" plain>确认</el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script>

import PanelGroup from './dashboard/PanelGroup'
import LineChart from './dashboard/LineChart'
import {decrypt, encrypt} from '@/utils/rsaEncrypt'
import {updatePassword} from '@/api/login'
import {downloadFile} from '@/utils/index'
import request from '@/utils/request'

const lineChartData = {
  newVisitis: {
    expectedData: [100, 120, 161, 134, 105, 160, 165],
    actualData: [120, 82, 91, 154, 162, 140, 145]
  },
  messages: {
    expectedData: [200, 192, 120, 144, 160, 130, 140],
    actualData: [180, 160, 151, 106, 145, 150, 130]
  },
  purchases: {
    expectedData: [80, 100, 121, 104, 105, 90, 100],
    actualData: [120, 90, 100, 138, 142, 130, 130]
  },
  shoppings: {
    expectedData: [130, 140, 141, 142, 145, 150, 160],
    actualData: [120, 82, 91, 154, 162, 140, 130]
  }
}
import {getPage} from './notice/announce/config/request'
import {getPage as messageRecord} from '@/views/messageCenter/messageRecord/config/request'
import {getPage as myAgendaTask} from '@/views/myTask/myAgendaTask/config/request'
import {getPage as myClaimTask} from '@/views/myTask/myClaimTask/config/request'
import {getPage as learningField} from '@/views/learningField/config/request'
import {getSummary, summaryEcharts} from '@/views/integratedQuery/quarterlySummary/config/request'
import {getPage as getOrganize} from '@/views/organizeCommunication/config/request'
import axios from 'axios'
import Schart from "vue-schart";

import {mapGetters} from "vuex";

import myMarquee from '../components/myMarquee/my-marquee';
import HomeMyTask from "./myTask/myAgendaTask/HomeMyTask";
import HomeAnnounce from "./notice/announce/HomeAnnounce";
import HomeLearningField from "./learningField/HomeLearningField";
import HomeOrganize from "./organizeCommunication/HomeOrganize";
import moment from "moment";
import qs from "qs";

export default {
  name: 'Dashboard',
  components: {
    Schart,
    HomeOrganize,
    HomeLearningField,
    HomeAnnounce,
    HomeMyTask,
    PanelGroup,
    LineChart,
    myMarquee,
  },
  computed: {
    ...mapGetters([
      'user',
      'updateAvatarApi',
      'baseApi',
    ])
  },
  data() {
    // 自定义验证
    const validOldPass = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入密码'))
      } else if (!this.checkOldPass(value)) {
        callback(new Error('密码不正确'))
      } else {
        callback()
      }
    }
    // 自定义验证
    const validNewPass = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入新密码'))
      } else if (!this.checkNewPassRule(value)) {
        this.point = false
        callback(new Error('密码至少包含大写字母、小写字母、数字、特殊字符!@#$%^&*()，且不少于12位'))
      } else if (!this.checkNewPass(value)) {
        callback(new Error('两次密码不一致'))
      } else {
        callback()
      }
    }
    return {
      options: {
        type: "bar",
        title: {
          text: "",
        },
        xRorate: 25,
        labels: ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
        datasets: [],
      },
      point: true,
      homeMyTaskDialog: false,
      homeAnnounceDialog: false,
      homeLearningFieldDialog: false,
      homeOrganizeDialog: false,
      marquee: false,
      newsList: [
        {
          name: "天然气省公司",
          detail: "信息公告：2020年10月14限公司成立，我行用信金额111111元",
        },
        {
          name: "河北分公司",
          detail: "信息公告：2020年9月30大幅度发大发的，我行用信金额222222元",
        },
        {
          name: "天津分公司",
          detail: "信息公告：2020年12月有限公司成立，我行用信金额333333元",
        },
        {
          name: "北京分公司",
          detail: "信息公告：2020年8月31有限公司成立，我行用信金额444444元",
        },
      ],
      lineChartData: lineChartData.newVisitis,
      announce: {},
      messageRecord: {},
      myAgendaTask: {},
      myClaimTask: {},
      systemDynamics: [],
      learningField: {},
      valuationList: {},
      standardList: {},
      organize: {},
      form: {},
      rules: {},
      userPass: {},
      dialogVisible: false,
      resetRules: {
        oldPass: [
          {required: true, trigger: 'blur', validator: validOldPass}
        ],
        newPass: [
          {required: true, trigger: 'blur', validator: validNewPass}
        ],
      },
      query: {
        queryStartDate: moment().startOf('year').format('YYYY-MM-DD HH:mm:ss'),
        queryEndDate: moment().endOf('year').format('YYYY-MM-DD HH:mm:ss')
      },
      showChart: false,
    }
  },
  methods: {
    checkNewPassRule(value) {
      var reg = /^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*,\.])[0-9a-zA-Z!@#$%^&*(),\.]{12,16}$/;
      if (reg.test(value)) {
        return true;
      }
      return false;
    },
    myMarqueeClick() {
      //Z01:预置的造价中心code
      if (this.user.orgCode === "Z01") {
        this.$router.push('/integratedQuery/quarterlySummary')
      }
    },
    myTaskChange() {
      console.log(this.homeMyTaskDialog)
      this.homeMyTaskDialog = true
    },
    handleSetLineChartData(type) {
      this.lineChartData = lineChartData[type]
    },
    initPage() {
      let params = {
        current: 1,
        size: 5,
        sort: ['top_value,desc', 'create_date,desc'],
        ifGetReadAnnounce: '1',
      };
      getPage(params)
        .then(res => {
          this.announce = res;
        })
      myAgendaTask(params)
        .then(res => {
          this.myAgendaTask = res;
        })
      learningField(params)
        .then(res => {
          this.learningField = res;
        })

      getSummary(this.query)
        .then(res => {
          this.sortKey(res, 'numberOfLogins')
          res.forEach((item, index) => {
            let text = '活跃度排名：第' + (index + 1) + '名' + item.name;
            this.systemDynamics.push({"text": text})
          })
          this.marquee = true
        })
      summaryEcharts().then(res => {
          this.$set(this.options, 'datasets', res);
        }
      )
      getOrganize({...params, type: 1})
        .then(res => {
          this.organize = res;
        })
      getOrganize({...params, type: 2})
        .then(res => {
          this.valuationList = res;
        })
      getOrganize({...params, type: 3})
        .then(res => {
          this.standardList = res;
        })
    },
    sortKey(array, key) {
      return array.sort(function (a, b) {
        let x = a[key];
        let y = b[key];
        return ((y < x) ? -1 : (x < y) ? 1 : 0)
      })
    },
    changePassword() {
      this.userPass = {};
      this.dialogVisible = true;
    },
    commitUpdatePassword() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.user.passwordCode = encrypt(this.userPass.newPass);
          this.user.password = encrypt(this.userPass.newPass);
          updatePassword(this.user).then(res => {
            this.$store.dispatch('LogOut').then(() => {
              location.reload()
            })
          })
        } else {
          return false
        }
      })
    },
    checkOldPass(val) {
      if (decrypt(this.user.passwordCode) === val) {
        return true
      } else {
        return false
      }
    },
    checkNewPass() {
      if (this.userPass.newPass && this.userPass.newPassTwo) {
        if (this.userPass.newPass === this.userPass.newPassTwo) {
          return true;
        } else {
          return false;
        }
      } else {
        return true;
      }
    },
    cancelPass() {
      this.$store.dispatch('LogOut').then(() => {
        location.reload()
      })
    },
    downLoad(item) {
      request({
        url: '/general/file/download?uri=' + item.fileUri,
        type: 'get',
        responseType: 'blob'
      })
        .then(res => {
          downloadFile(res, item.fileName)
        })
    },
    changeSwitch(value) {
      this.$forceUpdate()
    },
  },
  mounted() {
    this.initPage();
    if (this.user.ifChangeFlag === 1 && this.user.username !== 'admin') {
      this.dialogVisible = true;
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.top-icon {
  text-align: center;
  font-size: 20px;
}

.el-empty {
  padding: 20px 0;
}

.el-card {
  height: 190px; // 这里是固定高度
}

.box {
  height: 100%;
  overflow: auto;
}

.cardIcon {
  width: 1.2em;
  height: 1.2em;
  vertical-align: -0.25em;
  margin-right: 8px;
}

.linkStyle {
  display: block; /* 当前元素本身是inline的，因此需要设置成block模式 */
  white-space: nowrap; /* 因为设置了block，所以需要设置nowrap来确保不换行 */
  overflow: hidden; /* 超出隐藏结合width使用截取采用效果*/
  text-overflow: ellipsis; /* 本功能的主要功臣，超出部分的剪裁方式 */
  -o-text-overflow: ellipsis; /* 特定浏览器前缀 */
}

.seamless-warp2 {
  overflow: hidden;
  height: 33px;
  width: 90%;

  ul.item {
    width: 580px;

    li {
      float: left;
      margin-right: 10px;
    }
  }

  div {
    width: 1400px;
  }
}

.topcard {
  margin-bottom: 40px;
  height: 30px;
  margin-top: -15px
}

>>> .el-card__header {
  padding: 10px 10px;
  border-bottom: 1px solid #e6ebf5;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.box-card {
  box-shadow: none;
}

.dashboard-editor-container {
  padding: 10px 32px 32px 32px;
  background-color: rgb(240, 242, 245);
  position: relative;
  overflow: hidden;
  clear: both;

  .github-corner {
    position: absolute;
    top: 0;
    border: 0;
    right: 0;
  }

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}

@media (max-width: 1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}

.text {
  font-size: 14px;
}

.item {
  margin-bottom: 5px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both
}

.schart {
  width: 100%;
  height: 300px;
}
</style>
