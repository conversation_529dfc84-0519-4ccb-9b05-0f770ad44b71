<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.name" size="small" clearable placeholder="输入名称搜索" style="width: 200px;"
                  class="filter-item" @keyup.enter.native="crud.toQuery"/>
        <el-select
          v-model="query.treeData"
          placeholder="选择公司搜索"
          class="filter-item"
          clearable
          @change="crud.toQuery"
        >
          <el-input
            style="margin-left:21px;width: 88%"
            clearable
            placeholder="输入关键字进行过滤"
            v-model="filterText">
          </el-input>
          <el-option
            :value="treeDataValue"
            style="height: auto"
          >
            <el-tree
              ref="tree"
              :data="orgTree"
              node-key="id"
              :props="searchProps"
              @node-click="handleNodeClick"
              :filter-node-method="filterNode"
              highlight-current>
            </el-tree>
          </el-option>
        </el-select>
        <date-range-picker v-model="query.createTime" class="date-item"  @change="crud.toQuery"/>
        <rrOperation/>
      </div>
      <crudOperation :permission="permission"/>
    </div>

    <!-- 表单渲染 -->
    <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU"
               :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="520px">
      <el-form ref="form" :inline="true" :model="form" :rules="rules" size="small" label-width="80px">
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="form.name" style="width: 380px;"/>
        </el-form-item>

        <el-form-item label="描述信息" prop="remark">
          <el-input v-model="form.remark" style="width: 380px;" rows="5" type="textarea"/>
        </el-form-item>
        <!-- <el-form-item prop="ifEnabled" label="启用禁用">
             <el-radio v-for="item in dict.disable_enable"  :key="item.code" v-model="form.ifEnabled" :label="item.code">{{ item.name }}</el-radio>
        </el-form-item> -->

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>
    <el-row :gutter="15">
      <!--角色管理-->
      <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="17" style="margin-bottom: 10px">
        <el-card class="box-card" shadow="never">
          <div slot="header" class="clearfix">
            <span class="role-span">角色列表</span>
          </div>
          <el-table ref="table" v-loading="crud.loading" highlight-current-row style="width: 100%;" :data="crud.data"
                    @selection-change="crud.selectionChangeHandler" @current-change="handleCurrentChange" border>
            <el-table-column :selectable="checkboxT" type="selection" width="55"/>
            <el-table-column prop="name" label="角色名称"/>
            <!-- <el-table-column label="角色类型">
                <template v-slot="scope">
                  {{dict.role_types.filter(item=>item.code == scope.row.typeCd)[0].name}}
                </template>
            </el-table-column> -->
            <!-- <el-table-column label="是否启用" >
              <template v-slot="scope">
                  {{ showCurrentData(dict.disable_enable, scope.row.ifEnabled) }}
                </template>
            </el-table-column> -->
            <el-table-column :show-overflow-tooltip="true" prop="remark" label="描述"/>
            <el-table-column :show-overflow-tooltip="true" width="135px" prop="createDate"
                             label="创建日期"></el-table-column>
            <el-table-column v-permission="['admin','user:roles:edit','user:roles:del']" label="操作" width="130px"
                             align="center" fixed="right">
              <template v-slot="scope">
                <udOperation
                  :data="{
                    ...scope.row,
                    ifEnabled: scope.row.ifEnabled.toString()
                  }"
                  :permission="permission"
                />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination/>
        </el-card>
      </el-col>
      <!-- 菜单授权 -->
      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="7">
        <el-card class="box-card" shadow="never">
          <div slot="header" class="clearfix">
            <el-tooltip class="item" effect="dark" content="选择指定角色分配菜单" placement="top">
              <span class="role-span">菜单分配</span>
            </el-tooltip>
            <el-button
              v-permission="['admin','user:roles:edit']"
              :disabled="!showButton"
              :loading="menuLoading"
              icon="el-icon-check"
              size="mini"
              style="float: right; padding: 6px 9px"
              type="primary"
              @click="saveMenu"
            >保存
            </el-button>
          </div>
          <el-tree
            ref="menu"
            :data="menus"
            :default-checked-keys="menuIds"
            :props="defaultProps"
            accordion
            show-checkbox
            node-key="id"
            @check="menuChange"
          />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>

import crudDept from './config/request'
import crudRoles from '@/api/system/role'
import {getDepts, getDeptSuperior} from '@/api/system/dept'
import {getMenusTree} from '@/api/system/menu'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import {LOAD_CHILDREN_OPTIONS} from '@riophae/vue-treeselect'
import DateRangePicker from '@/components/DateRangePicker'
import {getDepts as contactsTree} from '@/views/user/mailList/config/request'

//注入配置文件
import config from './config/index'

const formFields = config.getFields('form');
const tableListFields = config.getFields('tableList');
//const  defaultForm = config.getDefaultForm( formFields );

const defaultForm = {name: null, ifEnabled: '1', remark: ''}
export default {
  name: 'Role',
  components: {Treeselect, pagination, crudOperation, rrOperation, udOperation, DateRangePicker},
  cruds() {
    return CRUD({title: '角色', url: config.requestUrl, sort: 'asc', crudMethod: {...crudDept}})
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据字典
  dicts: ['disable_enable'],

  data() {
    return {
      filterText: "",
      treeData: "",
      treeDataValue: "",
      orgTree: [],
      jobs: [],
      menuEls: [],
      currentData: {},
      defaultProps: {children: 'children', label: 'title', isLeaf: 'leaf'},
      searchProps: {children: 'children', label: 'name', isLeaf: 'leaf'},
      dateScopes: ['全部', '本级', '自定义'], level: 3,
      currentId: 0, menuLoading: false, showButton: false,
      menus: [], menuIds: [], depts: [],
      permission: {
        add: ['admin', 'user:roles:add'],
        edit: ['admin', 'user:roles:edit'],
        del: ['admin', 'user:roles:del']
      },
      rules: {
        name: [
          {required: true, message: '请输入名称', trigger: 'blur'}
        ],
        permission: [
          {required: true, message: '请输入权限', trigger: 'blur'}
        ]
      }
    }
  },
  watch: {
    dict(val) {
      this.jobs = val.role_types;
    },
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    this.contactsTree();
    crudDept.getMenusTree()
      .then(res => {
        this.buildData(res, 'children');
        this.menus = res;
      })
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    handleNodeClick(data, node, nodeData) {
      this.treeDataValue = data
      this.query.treeData = data.name
      this.query.orgCodeEq = data.code
      this.crud.toQuery()
    },
    // 获取弹窗内部门数据
    contactsTree() {
      contactsTree({}).then(res => {
        this.buildData(res, 'children');
        this.orgTree = res;
        console.log(this.orgTree)
      })
    },
    [CRUD.HOOK.afterRefresh]() {
      this.$refs.menu.setCheckedKeys([]);
      this.currentData = [];
      this.showButton = false;
    },

    // 提交前做的操作
    [CRUD.HOOK.afterValidateCU](crud) {
      return true
    },
    [CRUD.HOOK.afterRefresh]() {
      this.$refs.menu.setCheckedKeys([]);
      this.currentData = [];
      this.showButton = false;
    },
    // 触发单选
    handleCurrentChange(val) {
      if (!val) return;
      this.$refs.menu.setCheckedKeys([]);
      this.currentData = val;
      val.relResourceIds.forEach(id => {
        var node = this.$refs.menu.getNode(id);
        if (node && node.isLeaf) {
          this.$refs.menu.setChecked(node, true);
        }
      })
      this.showButton = true;
    },
    menuChange(menu) {
      this.menuEls = this.$refs.menu.getCheckedNodes();
      this.menuIds = this.$refs.menu.getCheckedKeys();

    },
    // 保存菜单
    saveMenu() {
      this.menuLoading = true
      let ids = this.$refs.menu.getCheckedKeys().concat(this.$refs.menu.getHalfCheckedKeys());
      crudDept.editMenu({
        roleId: this.currentData.id,
        relResourceIds: ids
      }).then(() => {
        this.crud.notify('保存成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
        this.menuLoading = false
        this.update()
      }).catch(err => {
        this.menuLoading = false
      })
    },
    // 改变数据
    update() {
      for (let i = 0; i < this.crud.data.length; i++) {
        if (this.currentData.id === this.crud.data[i].id) {
          this.crud.data[i].relResourceIds = this.menuEls.map(item => item.id);
          break
        }
      }
      //this.crud.toQuery();
    },
    // 获取部门数据
    getDepts() {
      getDepts({enabled: true}).then(res => {
        this.depts = res.content.map(function (obj) {
          if (obj.hasChildren) {
            obj.children = null
          }
          return obj
        })
      })
    },
    getSupDepts(depts) {
      const ids = []
      depts.forEach(dept => {
        ids.push(dept.id)
      })
      getDeptSuperior(ids).then(res => {
        const date = res.content
        this.buildDepts(date)
        this.depts = date
      })
    },
    buildDepts(depts) {
      depts.forEach(data => {
        if (data.children) {
          this.buildDepts(data.children)
        }
        if (data.hasChildren && !data.children) {
          data.children = null
        }
      })
    },
    // 获取弹窗内部门数据
    loadDepts({action, parentNode, callback}) {
      if (action === LOAD_CHILDREN_OPTIONS) {
        getDepts({enabled: true, pid: parentNode.id}).then(res => {
          parentNode.children = res.content.map(function (obj) {
            if (obj.hasChildren) {
              obj.children = null
            }
            return obj
          })
          setTimeout(() => {
            callback()
          }, 200)
        })
      }
    },
    // 如果数据权限为自定义则获取部门数据
    changeScope() {
      if (this.form.dataScope === '自定义') {
        this.getDepts()
      }
    },
    checkboxT(row) {
      return true
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.role-span {
  font-weight: bold;
  color: #303133;
  font-size: 15px;
}
</style>

<style rel="stylesheet/scss" lang="scss" scoped>
>>> .el-input-number .el-input__inner {
  text-align: left;
}

>>> .vue-treeselect__multi-value {
  margin-bottom: 0;
}

>>> .vue-treeselect__multi-value-item {
  border: 0;
  padding: 0;
}
</style>
