import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;

export function getPage(params) {
    return new Promise(function(resolve, reject){
       
        request({
            url: requestUrl,
            method: 'get',
            params
        }).then((data)=>{
            resolve(data)
        })
        .catch(reject)
       
    })
    
}
export function getDepts(params) {
    return request({
      url: requestUrl,
      method: 'get',
      params
    })
  }
  


export function add(data) {
    return request({
        url: requestUrl,
        method: 'post',
        data
    })
}

export function del(ids) {
    return request({
        url: requestUrl,
        method: 'delete',
        data: ids
    })
}

export function edit(data) {
    return request({
        url: requestUrl,
        method: 'put',
        data
    })
}

export function getTree(data){
    return request({
        url: '/user/dept/tree',
        method: 'get',
        data
    })
}


export default { add, edit, del, getPage, getDepts, getTree }
