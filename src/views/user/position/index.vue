<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.name" clearable size="small" placeholder="输入岗位名称搜索" style="width: 200px;"
                  class="filter-item" @keyup.enter.native="crud.toQuery"/>
        <date-range-picker v-model="query.createTime" class="date-item" @change="crud.toQuery"/>
        <rrOperation/>
      </div>
      <crudOperation :permission="permission"/>
    </div>
    <!--表单组件-->
    <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU"
               :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
      <el-form :disabled="crud.status.details > 0" ref="form" inline :model="form" :rules="rules" size="small"
               label-width="80px">
        <el-form-item label="岗位名称" prop="name">
          <el-input v-model="form.name" style="width: 370px;"/>
        </el-form-item>
        <el-form-item label="类型" prop="remark">
          <el-select v-model="form.typeCd" clearable :disabled="form.ifTop != 1 || form.isEdit" placeholder="请选择">
            <el-option
              v-for="item in dict.org_type"
              :key="item.code"
              :label="item.name"
              :value="item.code">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" style="width: 370px;" type="textarea"/>
        </el-form-item>
        <el-form-item label="岗位排序" prop="seq">
          <el-input-number
            v-model.number="form.seq"
            :min="0"
            :max="999"
            controls-position="right"
            style="width: 370px;"
          />
        </el-form-item>
        <el-form-item label="顶级岗位">

          <el-radio-group v-model="form.ifTop" @change="topPositionChange" style="width: 140px" :disabled="form.isEdit">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- <el-form-item label="状态" prop="ifEnabled">

          <el-radio v-for="item in dict.disable_enable" :key="item.code" v-model="form.ifEnabled" :label="item.code">{{ item.name }}</el-radio>
        </el-form-item> -->

        <el-form-item v-if="form.ifTop === '0'" style="margin-bottom: 0;" label="上级岗位" prop="parentCode">
          <treeselect
            v-model="form.parentCode"
            :options="depts"
            style="width: 370px;"
            @select="selectDepart"
            placeholder="选择上级类目"
            :disabled="form.isEdit"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>
    <!--表格渲染-->

    <el-table
      ref="table"
      v-loading="crud.loading"
      lazy
      :load="getDeptDatas"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      :data="crud.data.map(item=>({...item, enabled: item.ifEnabled == 1, ifEnabled: item.ifEnabled.toString(), isEdit: true}))"
      row-key="id"
      @select="crud.selectChange"
      @select-all="crud.selectAllChange"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column :selectable="checkboxT" type="selection" width="55"/>
      <el-table-column label="岗位名称" prop="name"/>
      <el-table-column label="类型">
        <template v-slot="scope">
          {{ showCurrentData(dict.org_type, scope.row.typeCd) }}
        </template>
      </el-table-column>
      <el-table-column label="排序" prop="seq"/>

      <!-- <el-table-column label="状态" align="center" prop="enabled">
        <template v-slot="scope">

          <el-switch
            v-model="{ifEnabled:scope.row.ifEnabled == 1}.ifEnabled"
            :disabled="scope.row.id === 1"
            active-color="#409EFF"
            inactive-color="#F56C6C"
            @change="changeEnabled(scope.row, scope.row.ifEnabled)"
          />
        </template>
      </el-table-column> -->
      <el-table-column prop="createDate" label="创建日期">
        <template v-slot="scope">
          <span>{{ scope.row.createDate }}</span>
        </template>
      </el-table-column>
      <el-table-column v-permission="['admin','user:position:edit','user:position:del', 'user:position:details']"
                       label="操作" width="180px" align="center" fixed="right">
        <template v-slot="scope">
          <udOperation
            :data="{...scope.row,typeCd: scope.row.typeCd && scope.row.typeCd.toString(), ifEnabled: scope.row.ifEnabled.toString(), enabled: scope.row.ifEnabled == 1, isEdit: true}"
            :permission="permission"
            :disabledDle="scope.row.ownerOrgCode != user.orgCode"
            :disabledEdit="scope.row.ownerOrgCode != user.orgCode"
            msg="确定删除吗,如果存在下级节点则一并删除，此操作不能撤销！"
          />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>

function flatten(list, key, pindex) {
  pindex = pindex || 0;
  if (!list) return [];

  var newList = [];
  list.map((item, index) => {
    item.pindex = pindex;
    item.cindex = index;
    newList.push(item);
    (pindex => {
      if (item[key] && item[key].length > 0) {
        newList = [...newList, ...flatten(item[key], key, ++pindex)];
      }
    })(pindex)
  })

  return newList;
}

//import crudDept from '@/api/system/dept'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import {LOAD_CHILDREN_OPTIONS} from '@riophae/vue-treeselect'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import DateRangePicker from '@/components/DateRangePicker'
import {mapGetters} from 'vuex'
import config from './config/index'
import crudDept from './config/request'

//const defaultForm = { id: null, name: null, isTop: '1', subCount: 0, pid: null, deptSort: 999, enabled: 'true' }
const defaultForm = {ifTop: '1', ifEnabled: '1', name: null, parentCode: null, remark: null, seq: 999};
export default {
  name: 'Dept',
  components: {Treeselect, crudOperation, rrOperation, udOperation, DateRangePicker},
  cruds() {
    return CRUD({title: '岗位', url: config.requestUrl, crudMethod: {...crudDept}})
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 设置数据字典
  dicts: ['disable_enable', 'org_type'],
  data() {
    return {
      selectDepartRecord: {},
      depts: [],
      rules: {
        name: [
          {required: true, message: '请输入名称', trigger: 'blur'}
        ],
        deptSort: [
          {required: true, message: '请输入序号', trigger: 'blur', type: 'number'}
        ]
      },
      permission: {
        add: ['admin', 'user:position:add'],
        edit: ['admin', 'user:position:edit'],
        del: ['admin', 'user:position:del'],
        details: ['admin', 'user:position:details']
      },
      enabledTypeOptions: [
        {key: 'true', display_name: '正常'},
        {key: 'false', display_name: '禁用'}
      ]
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ]),
  },
  methods: {
    topPositionChange(val) {
      this.form.typeCd = null;
    },
    selectDepart(val) {
      this.buildData(this.depts, 'children', (data) => {
        if (data.code == val.code) {
          this.selectDepartRecord = this.depts[data.leafIndex];
          this.form.typeCd = this.selectDepartRecord.typeCd && this.selectDepartRecord.typeCd.toString();
        }
      })
      //  this.selectDepartRecord = val;
      this.form.parentCode = val.code;
    },
    getDeptDatas(tree, treeNode, resolve) {
      const params = {pid: tree.id}
      setTimeout(() => {
        crudDept.getDepts(params).then(res => {
          resolve(res.content)
        })
      }, 100)
    },
    [CRUD.HOOK.beforeToAdd]() {
      this.form.isEdit = false;
    },
    // 新增与编辑前做的操作
    [CRUD.HOOK.afterToCU](crud, form) {
      this.getDepts()

    },
    getSupDepts(id) {
      crudDept.getDeptSuperior(id).then(res => {
        const date = res.content
        this.buildDepts(date)
        this.depts = date
      })
    },
    buildDepts(depts, callback) {
      depts.forEach(data => {
        callback && callback(data)
        if (data.children) {
          this.buildDepts(data.children, callback)
        }

        if (data.children && data.children.length <= 0) {
          delete data.children;
        }

        data.label = data.name
      })
    },
    getDepts() {
      crudDept.getTree({ifEnabled: 1}).then(res => {
        this.buildDepts(res);
        this.depts = res;
      })
    },
    // 获取弹窗内部门数据
    loadDepts({action, parentNode, callback}) {
      if (action === LOAD_CHILDREN_OPTIONS) {
        crudDept.getDepts({enabled: true, pid: parentNode.id}).then(res => {
          parentNode.children = res.content.map(function (obj) {
            if (obj.hasChildren) {
              obj.children = null
            }
            return obj
          })
          setTimeout(() => {
            callback()
          }, 100)
        })
      }
    },
    handleListData(data, parent) {
      parent = parent || {};
      data.map(item => {
        if (item.children) {
          this.handleListData(item.children, item);
        }
        item.ifTop = item.parentCode ? '0' : '1';
        item.parentCode = parent.id || null;
      })
    },
    //刷新数据
    [CRUD.HOOK.afterRefresh]() {
      this.handleListData(this.crud.data);
    },
    // 提交前的验证
    [CRUD.HOOK.afterValidateCU]() {
      try {
        //如果不是顶级单位

        if (this.crud.form.ifTop == 0) {
          this.crud.form.typeCd = null;
        }
        this.crud.form.parentCode = flatten(this.crud.data, 'children').filter(item => item.id == this.crud.form.parentCode)[0].code;
      } catch (e) {
      }
      return true;
    },
    // 改变状态
    changeEnabled(data, val) {

      val = !(val == 1);
      this.$confirm('此操作将 "' + this.dict.disable_enable.filter(item => item.code == val)[0].name + '" ' + data.name + '岗位, 是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        crudDept.edit({...data, ifEnabled: val ? 1 : 0}).then(res => {
          this.crud.notify(this.dict.disable_enable.filter(item => item.code == val)[0].name + '成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
          data.ifEnabled = val ? "1" : "0";
          this.crud.toQuery();
        }).catch(err => {
          data.ifEnabled = val ? "0" : "1";
        })
      }).catch(() => {
        data.ifEnabled = val ? "0" : "1";
      })
    },
    checkboxT(row, rowIndex) {
      return row.ownerOrgCode == this.user.orgCode
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
>>> .vue-treeselect__control, >>> .vue-treeselect__placeholder, >>> .vue-treeselect__single-value {
  height: 30px;
  line-height: 30px;
}
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
>>> .el-input-number .el-input__inner {
  text-align: left;
}
</style>
