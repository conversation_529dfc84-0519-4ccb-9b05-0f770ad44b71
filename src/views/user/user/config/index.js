export default {
    //获取某类型字段列表
    getFields(type){
        return  this.parames.filter(item=>{
            return item.whereShow.some((citem)=>citem == type)
    })
    },
    //加工页面formData
    getDefaultForm(data){
        var json = {};
        data.map(item=>{
            json[ item.fieldName ] = item.defaultVal;
    })
        return json;
    },
    //页面请求地址
    requestUrl: '/user/user',

    //页面字段参数，可根据实际需求扩展
    parames: [
        {
            label: '主键ID',
            fieldName: 'id',
            defaultVal: '',
            type: 'input',
            whereShow: ['form', 'edit']
        },
        {
            label: '是否组织管理员',
            fieldName: 'ifOrgAdmin',
            defaultVal: '',
            type: 'input',
            whereShow: ['form', 'add', 'edit']
        },
        {
            label: '用户名',
            fieldName: 'username',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '手机号',
            fieldName: 'phone',
            defaultVal: '',
            type: 'input',
            whereShow: ['form', 'add', 'edit']
        },
        {
            label: '邮箱',
            fieldName: 'email',
            defaultVal: '',
            type: 'input',
            whereShow: ['form', 'add', 'edit']
        },
        {
            label: '密码',
            fieldName: 'password',
            defaultVal: '',
            type: 'input',
            whereShow: [ 'form', 'add', 'edit']
        },
        {
            label: '昵称',
            fieldName: 'nickName',
            defaultVal: '',
            type: 'input',
            whereShow: ['form', 'add', 'edit']
        },
        {
            label: '真实姓名',
            fieldName: 'realName',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '省编码',
            fieldName: 'provinceCd',
            defaultVal: '',
            type: 'input',
            whereShow: ['form', 'add', 'edit']
        },
        {
            label: '市编码',
            fieldName: 'cityCd',
            defaultVal: '',
            type: 'input',
            whereShow: ['form', 'add', 'edit']
        },
        {
            label: '区县编码',
            fieldName: 'districtCd',
            defaultVal: '',
            type: 'input',
            whereShow: ['form', 'add', 'edit']
        },
        {
            label: '详细地址',
            fieldName: 'detailAddress',
            defaultVal: '',
            type: 'input',
            whereShow: [ 'form', 'add', 'edit']
        },
        {
            label: '完整地址',
            fieldName: 'fullAddress',
            defaultVal: '',
            type: 'input',
            whereShow: [ 'form', 'add', 'edit']
        },
        {
            label: '状态',
            fieldName: 'statusCd',
            defaultVal: '',
            type: 'input',
            whereShow: [ 'form', 'add', 'edit']
        },
        {
            label: '创建用户姓名',
            fieldName: 'createUserName',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '创建时间',
            fieldName: 'createDate',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '更新用户姓名',
            fieldName: 'updateUserName',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '更新时间',
            fieldName: 'updateDate',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '数据版本',
            fieldName: 'dataVersion',
            defaultVal: '',
            type: 'input',
            whereShow: [ 'form', 'edit']
        },
    ]
}
