import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;

export function getPage(params) {
    return request({
        url: requestUrl,
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: requestUrl,
        method: 'post',
        data
    })
}

export function del(ids) {
    return request({
        url: requestUrl,
        method: 'delete',
        data: ids
    })
}

export function edit(data) {
    return request({
        url: requestUrl,
        method: 'put',
        data
    })
}

export function getDepts(params) {
    return request({
      url: '/user/dept/completeTree',
      method: 'get',
      params
    })
  }

export function getAllJob(params){
    return request({
        url: '/user/position/tree',
        method: 'get',
        params
      })
}

export function getRoles(params){
    return request({
        url: '/user/role/page',
        method: 'get',
        params
      })
}

export function getRoleDict(params){
  return request({
    url: '/system/role/getRoleDict',
    method: 'get',
    params
  })
}

export function resetPassword(data){
  return request({
    url: 'user/user/resetPassword',
    method: 'put',
    data
  })
}

export function userList(data){
  return request({
    url: 'user/user/userList/'+data,
    method: 'get',
  })
}

export function getJstOrgTree(){
  return request({
    url: 'user/user/getJstOrgTree',
    method: 'get',
  })
}

export function uploadSeal(data){
  return request({
    url: requestUrl + '/uploadSeal',
    method: 'post',
    data
})
}
export default { add, edit, del, getPage, getDepts, getAllJob, getRoles,resetPassword,getJstOrgTree ,getRoleDict, uploadSeal}
