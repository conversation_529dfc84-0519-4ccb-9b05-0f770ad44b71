<template>
  <div class="app-container">
    <el-row :gutter="20" style="width:100%">
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" style="margin-bottom: 10px">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>个人信息</span>
          </div>
          <div>
            <div style="text-align: center">
              <!-- <el-upload
                :show-file-list="false"
                :on-success="handleSuccess"
                :on-error="handleError"
                :headers="headers"
                :action="updateAvatarApi"
                class="avatar-uploader"
              >

              </el-upload> -->
              <img :src="user.avatar ? baseApi + '/avatar/' + user.avatar : Avatar" title="点击上传头像" class="avatar">
            </div>
            <ul class="user-info">
              <li><div style="height: 100%"><svg-icon icon-class="login" /> 登录账号<div class="user-right">{{ user.username }}</div></div></li>
              <li><svg-icon icon-class="user1" /> 是否组织管理员 <div class="user-right">{{ user.ifOrgAdmin ? '是' : '否' }}</div></li>
              <li><svg-icon icon-class="peoples" /> 昵称 <div class="user-right">{{ user.nickname }}</div></li>
              <li><svg-icon icon-class="tree" /> 组织名称 <div class="user-right">{{ user.orgName }}</div></li>

            </ul>
            <el-button type="primary" plain @click="changePassword" v-if="user.username!='admin'">修改密码</el-button>
          </div>
        </el-card>

      </el-col>
    </el-row>
    <el-dialog
      title="修改密码"
      :visible.sync="dialogVisible"
      width="30%"
      >
      <el-form ref="form" :model="userPass" label-width="80px" :rules="resetRules" >
        <el-form-item label="原密码" prop="oldPass">
          <el-input show-password v-model="userPass.oldPass" auto-complete="off" @blur="checkOldPass" ></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPass">
          <el-input show-password v-model="userPass.newPass" auto-complete="off" @blur="checkNewPass" ></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="newPass">
          <el-input show-password v-model="userPass.newPassTwo" auto-complete="off" @blur="checkNewPass" ></el-input>
          <div v-if="point" style="font-size: 12px;line-height: 1;padding-top: 10px">密码至少包含大写字母、小写字母、数字、特殊字符!@#$%^&*()，且不少于12位</div>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="info"  @click="dialogVisible=false" plain>取消</el-button>
        <el-button type="primary" @click="commitUpdatePassword" plain>确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import updatePass from './center/updatePass'
import updateEmail from './center/updateEmail'
import { getToken } from '@/utils/auth'
import store from '@/store'
import { isvalidPhone } from '@/utils/validate'
import { parseTime } from '@/utils/index'
import crud from '@/mixins/crud'
import { editUser } from '@/api/system/user'
import Avatar from '@/assets/images/headGreen.png'
import { decrypt,encrypt } from '@/utils/rsaEncrypt'
import { updatePassword } from '@/api/login'

export default {
  name: 'Center',
  components: { updatePass, updateEmail },
  mixins: [crud],
  data() {
    // 自定义验证
    const validPhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入电话号码'))
      } else if (!isvalidPhone(value)) {
        callback(new Error('请输入正确的11位手机号码'))
      } else {
        callback()
      }
    }
    // 自定义验证
    const validOldPass = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入密码'))
      } else if (!this.checkOldPass(value)) {
        callback(new Error('密码不正确'))
      } else {
        callback()
      }
    }

    // 自定义验证
    const validNewPass = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入新密码'))
      } else if (!this.checkNewPassRule(value)) {
        this.point=false
        callback(new Error('密码至少包含大写字母、小写字母、数字、特殊字符!@#$%^&*()，且不少于12位'))
      } else if (!this.checkNewPass(value)) {
        callback(new Error('两次密码不一致'))
      } else {
        callback()
      }
    }

    return {
      point:true,
      Avatar: Avatar,
      activeName: 'first',
      saveLoading: false,
      dialogVisible:false,
      headers: {
        'Authorization': getToken()
      },
      form: {},
      rules: {
        nickName: [
          { required: true, message: '请输入用户昵称', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        phone: [
          { required: true, trigger: 'blur', validator: validPhone }
        ],

      },
      userPass:{},
      resetRules:{
          oldPass: [
            { required: true, trigger: 'blur', validator: validOldPass }
          ],
          newPass:[
            {required: true, trigger: 'blur', validator: validNewPass}
          ],
      }
    }

  },
  computed: {
    ...mapGetters([
      'user',
      'updateAvatarApi',
      'baseApi',
      'password',
    ])
  },
  created() {
    this.form = { id: this.user.id, nickName: this.user.nickName, sex: this.user.sex, phone: this.user.phone }
    store.dispatch('GetInfo').then(() => {})
  },
  methods: {
    parseTime,
    checkNewPassRule(value) {
      var reg=/^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*,\.])[0-9a-zA-Z!@#$%^&*(),\.]{12,16}$/;
      if (reg.test(value)) {
        return true;
      }
      return false;
    },
    handleClick(tab, event) {
      if (tab.name === 'second') {
        this.init()
      }
    },
    changePassword(){
      this.userPass={};
      this.dialogVisible=true;
    },
    commitUpdatePassword(){
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.user.passwordCode=encrypt(this.userPass.newPass);
          this.user.password=encrypt(this.userPass.newPass);
          updatePassword(this.user).then(res => {
            this.$store.dispatch('LogOut').then(() => {
              location.reload()
            })
          })
        } else {
          return false;
        }
      });
    },
    checkOldPass(value){
      if (decrypt(this.user.passwordCode)===value){
        return true
      } else {
        return false
      }
    },
    checkNewPass(value){
      if (this.userPass.newPass&&this.userPass.newPassTwo){
        if (this.userPass.newPass===this.userPass.newPassTwo){
          return true;
        }else {
          return false;
        }
      }else {
        return true;
      }
    },
    beforeInit() {
      this.url = 'api/logs/user'
      return true
    },
    handleSuccess(response, file, fileList) {
      this.$notify({
        title: '头像修改成功',
        type: 'success',
        duration: 2500
      })
      store.dispatch('GetInfo').then(() => {})
    },
    // 监听上传失败
    handleError(e, file, fileList) {
      const msg = JSON.parse(e.message)
      this.$notify({
        title: msg.message,
        type: 'error',
        duration: 2500
      })
    },
    doSubmit() {
      if (this.$refs['form']) {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            this.saveLoading = true
            editUser(this.form).then(() => {
              this.editSuccessNotify()
              store.dispatch('GetInfo').then(() => {})
              this.saveLoading = false
            }).catch(() => {
              this.saveLoading = false
            })
          }
        })
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
  .avatar-uploader-icon {
    font-size: 28px;
    width: 120px;
    height: 120px;
    line-height: 120px;
    text-align: center
  }

  .avatar {
    width: 60px;
    height: 60px;
    display: block;
    border-radius: 50%;
    margin: 0 auto;
  }
  .user-info {
    padding-left: 0;
    list-style: none;
    li{
      border-bottom: 1px solid #F0F3F4;
      padding: 11px 0;
      font-size: 13px;
    }
    .user-right {
      float: right;

      a{
        color: #317EF3;
      }
    }
  }
</style>
