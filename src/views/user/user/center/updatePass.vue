<template>
  <div style="display: inline-block">
    <el-dialog :visible.sync="dialog" :close-on-click-modal="false" :before-close="cancel" :title="title" append-to-body width="800px" @close="cancel">
      <el-form ref="form" :model="form" :rules="rules" size="small" label-width="88px">
        <el-form-item label="旧密码" prop="oldPass">
          <el-input v-model="form.oldPass" type="password" auto-complete="on" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="新密码" prop="newPass">
          <el-input v-model="form.newPass" type="password" auto-complete="on" style="width: 370px;" />
        </el-form-item>
        <el-form-item label="确认密码" prop="newPass">
          <el-input v-model="form.newPassTwo" type="password" auto-complete="on" style="width: 370px;" />
          <div v-if="point" style="font-size: 12px;line-height: 1;padding-top: 10px">密码至少包含大写字母、小写字母、数字、特殊字符，且不少于12位</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="cancel">取消</el-button>
        <el-button :loading="loading" type="primary" @click="doSubmit">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import store from '@/store'
import { updatePass } from '@/api/system/user'
export default {
  data() {
    // 自定义验证
    const validNewPass = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入新密码'))
      } else if (!this.checkNewPassRule(value)) {
        this.point = false
        callback(new Error('密码至少包含大写字母、小写字母、数字、特殊字符，且不少于12位'))
      } else if (!this.checkNewPass(value)) {
        callback(new Error('两次密码不一致'))
      } else {
        callback()
      }
    }
    return {
      point:true,
      loading: false, dialog: false, title: '修改密码', form: { oldPass: '', newPass: '', confirmPass: '' },
      rules: {
        oldPass: [
          { required: true, message: '请输入旧密码', trigger: 'blur' }
        ],
        newPass: [
          { required: true, trigger: 'blur',validator: validNewPass }
        ]
      }
    }
  },
  methods: {
    checkNewPassRule(value) {
      var reg=/^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*,\.])[0-9a-zA-Z!@#$%^&*(),\.]{12,16}$/;
      if (reg.test(value)) {
        return true;
      }
      return false;
    },
    checkNewPass() {
      if (this.form.newPass && this.form.newPassTwo) {
        if (this.form.newPass === this.form.newPassTwo) {
          return true;
        } else {
          return false;
        }
      } else {
        return true;
      }
    },
    cancel() {
      this.resetForm()
    },
    doSubmit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          updatePass(this.form).then(res => {
            this.resetForm()
            this.$notify({
              title: '密码修改成功，请重新登录',
              type: 'success',
              duration: 1500
            })
            setTimeout(() => {
              store.dispatch('LogOut').then(() => {
                location.reload() // 为了重新实例化vue-router对象 避免bug
              })
            }, 1500)
          }).catch(err => {
            this.loading = false
            console.log(err.response.data.message)
          })
        } else {
          return false
        }
      })
    },
    resetForm() {
      this.dialog = false
      this.$refs['form'].resetFields()
      this.form = { oldPass: '', newPass: '', confirmPass: '' }
    }
  }
}
</script>

<style scoped>

</style>
