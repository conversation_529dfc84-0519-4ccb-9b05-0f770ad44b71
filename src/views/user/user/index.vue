<template>
  <div class="app-container">
    <!--侧边部门数据-->
    <div id="box">
      <div id="left">
        <el-scrollbar style="height:100%">
          <el-input
            size="small"
            placeholder="输入关键字进行过滤"
            v-model="filterText">
          </el-input>
          <el-tree
            style="margin-top: 20px"
            ref="tree"
            :data="deptDatas"
            :props="defaultProps"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
            :filter-node-method="filterNode"
            highlight-current
          />
        </el-scrollbar>
      </div>
      <div id="resize"></div>
      <div id="right" v-bind:style="{ width: detailWidth + 'px' }">
        <!--用户数据-->
        <!--工具栏-->
        <div class="head-container">
          <div v-if="crud.props.searchToggle">
            <!-- 搜索 -->
            <el-input
              v-model="query.queryName"
              clearable
              size="small"
              placeholder="输入名称搜索"
              style="width: 200px;"
              class="filter-item"
              @keyup.enter.native="crud.toQuery"
            />
            <el-select
              @change="crud.toQuery"
              v-model="query.statusCd"
              clearable
              size="small"
              placeholder="状态"
              class="filter-item"
            >
              <el-option
                v-for="item in dict.user_status"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
            <el-date-picker
              @change="crud.toQuery"
              v-model="query.queryTime"
              :default-time="['00:00:00','23:59:59']"
              type="daterange"
              range-separator=":"
              size="small"
              class="date-item"
              value-format="yyyy-MM-dd HH:mm:ss"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
            <rrOperation/>
          </div>
          <crudOperation show="" :permission="permission"/>
        </div>
        <!--表单渲染-->
        <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU"
                   :visible.sync="crud.status.cu > 0" :title="crud.status.title" :width="formStyle.width"
                   destroy-on-close width="1180px">
          <el-form ref="form" :disabled="crud.status.details > 0" :inline="true" :model="form" :rules="rules"
                   size="small"
                   label-position="top" label-width="120px" style="padding: 20px 20px 0 20px">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="form.username" :style="formInputStyle"/>
            </el-form-item>
            <el-form-item label="密码" prop="password">
              <el-input v-model="form.password" type="password" :disabled="crud.status.edit == 1"
                        :style="formInputStyle"/>
            </el-form-item>
            <el-form-item label="真实姓名" prop="realName">
              <el-input v-model="form.realName" :style="formInputStyle"/>
            </el-form-item>
            <el-form-item label="手机号" prop="phone" v-if="!crud.form.ifOrgAdmin == 1">
              <el-input v-model="form.phone" :style="formInputStyle"/>
            </el-form-item>

            <el-form-item label="昵称" prop="nickName">
              <el-input v-model="form.nickName" :style="formInputStyle"/>
            </el-form-item>

            <el-form-item label="邮箱" prop="email" v-if="!crud.form.ifOrgAdmin == 1">
              <el-input v-model="form.email" :style="formInputStyle"/>
            </el-form-item>


            <el-form-item label="部门" prop="deptCode" v-if="!crud.form.ifOrgAdmin == 1">
              <treeselect
                v-model="form.deptCode"
                :options="depts"
                :style="formInputStyle"
                placeholder="选择部门"
                :disabled="crud.status.details > 0"
                :normalizer="normalizer"
              />
            </el-form-item>
            <el-form-item label="岗位" prop="positions" v-if="!crud.form.ifOrgAdmin == 1">
              <el-cascader
                :style="formInputStyle"
                :options="jobs"
                v-model="form.positions"
                @change="cascaderChange"
                clearable
                ref="refPositions"
                :show-all-levels="false"
                :props="{ multiple: true, value: 'code', label: 'name', checkStrictly: true, emitPath: false }"
              >
              </el-cascader>
            </el-form-item>
            <el-form-item label="角色" prop="roles" v-if="!crud.form.ifOrgAdmin == 1">
              <el-select v-model="form.roles" multiple :style="formInputStyle" placeholder="请先选择角色" clearable>
                <el-option
                  v-for="(item) in roles"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                  :disabled="item.disabled"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="即时通信" prop="jstUserId">
              <el-select
                @clear="jstClear"
                v-model="form.jstUserName"
                placeholder="选择即时通信"
                :style="formInputStyle"
                clearable
              >
                <el-input
                  style=""
                  placeholder="输入关键字进行过滤"
                  v-model="jstFilterText">
                </el-input>
                <el-option
                  :value="treeDataValue"
                  style="height: auto"
                >
                  <el-tree
                    style="height: auto"
                    ref="jstTree"
                    :data="jstOrgTreeData"
                    @node-click="jstHandleNodeClick"
                    :filter-node-method="filterNode"
                    node-key="id"
                    :props="searchProps"
                    :accordion="true"
                    :default-expanded-keys="defaultExpandedKeys"
                    :default-checked-keys="defaultCheckedKeys"
                    highlight-current>
                  </el-tree>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="状态" prop="statusCd">
              <el-select v-model="form.statusCd" :style="formInputStyle" placeholder="请选择">
                <el-option
                  v-for="(item) in dict.user_status"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
            <el-form-item style="margin-bottom: 0;" label="省市区" prop="region" v-if="!crud.form.ifOrgAdmin == 1">
              <el-cascader
                :style="formInputStyle"
                :options="regionData"
                v-model="form.region"
              >
              </el-cascader>
            </el-form-item>
            <el-form-item label="详细地址" prop="detailAddress">
              <el-input v-model="form.detailAddress" style="width:630px"/>
            </el-form-item>
            <el-divider>印章</el-divider>
            <uploadImage
              :file-list="form.imgList"
              :remove-file="removeFile">
            </uploadImage>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="text" @click="crud.cancelCU">取消</el-button>
            <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU"
                       :disabled="crud.status.details > 0">确认
            </el-button>
          </div>
        </el-dialog>
        <!--表格渲染-->
        <el-table ref="table" v-loading="crud.loading" :data="crud.data" style="width: 100%;"
                  @selection-change="crud.selectionChangeHandler" border>
          <el-table-column :selectable="checkboxT" type="selection" width="55"/>
          <el-table-column :show-overflow-tooltip="true" prop="username" label="用户名"/>
          <el-table-column :show-overflow-tooltip="true" prop="realName" label="真实姓名"/>
          <el-table-column prop="roleIds" label="角色" width="150" show-overflow-tooltip>
            <template v-slot="scope">
              <el-tag style="margin-right: 10px" v-for="(item, key) in scope.row.roles" :key="key">{{ item.name }}
              </el-tag>
            </template>
          </el-table-column>

          <!--          <el-table-column prop="createDate" width="140" label="创建日期">-->
          <!--          </el-table-column>-->
          <el-table-column
            v-permission="['admin','user:user:edit','user:user:del']"
            label="操作"
            width="180"
            align="center"
            fixed="right"
          >
            <template v-slot="scope">
              <div style="display:inline-block;">
                <udOperation
                  :data="{
                      ...scope.row,
                      roles:scope.row.roles.map(item=>item.key),
                      positions:scope.row.positions.map(item=>item.key),
                      password: '******',
                      region: [scope.row.provinceCd, scope.row.cityCd, scope.row.districtCd]
                    }"
                  :permission="permission"
                  :disabledDle="scope.row.id === user.id"
                />
              </div>
              <div style="display:inline-block;">
                <el-tooltip class="item" effect="dark" content="重置密码" placement="top-start">
                  <el-button type="danger" size="mini" icon="el-icon-warning" v-if="user.username!='admin'"
                             @click="resetDialog(scope.row)"></el-button>
                </el-tooltip>
              </div>
            </template>

          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination/>
        <el-dialog
          title="重置密码"
          :visible.sync="dialogVisible"
          width="30%">
          <span>是否重置密码为Gczjglxt@2.0</span>
          <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="resetPassword">确 定</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import {isvalidPhone} from '@/utils/validate'
import {getAll, getLevel} from '@/api/system/role'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import {mapGetters} from 'vuex'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import {regionData} from 'element-china-area-data'
import {decrypt, encrypt} from '@/utils/rsaEncrypt'
import crudUser from './config/request'
import Treeselect from '@riophae/vue-treeselect'
import uploadImage from '@/components/AttachmentsUploadFile/uploadImage'

const getDepts = crudUser.getDepts;
const getAllJob = crudUser.getAllJob;
const getRoles = crudUser.getRoles;
const getRoleDict = crudUser.getRoleDict;
//注入配置文件
import config from './config/index'

const formFields = config.getFields('form');
const tableListFields = config.getFields('tableList');
import {flatten, formatArea} from '@/utils/index'

let userRoles = []
const defaultForm = {
  region: null,
  realName: null,
  statusCd: '1',
  ifOrgAdmin: 0,
  password: null,
  username: null,
  nickName: null,
  email: null,
  roles: null,
  positions: null,
  detailAddress: null,
  deptCode: null,
  phone: null,
  passwordCode: null,
  imgList: []
}
export default {
  watch: {
    // 监听deptId
    'form.deptCode': 'currDeptChange',
    filterText(val) {
      this.$refs.tree.filter(val);
    },
    jstFilterText(val) {
      this.$refs.jstTree.filter(val);
    }
  },
  name: 'User',
  components: {crudOperation, rrOperation, udOperation, pagination, Treeselect, uploadImage},
  cruds() {
    return CRUD({title: '用户', sort: ['asc'], url: config.requestUrl, crudMethod: {...crudUser}})
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据字典
  dicts: ['if_admin', 'user_status'],
  data() {
    // 自定义验证
    const validPhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入电话号码'))
      } else if (!isvalidPhone(value)) {
        callback(new Error('请输入正确的11位手机号码'))
      } else {
        callback()
      }
    }
    return {
      isOrgAdmin: false,
      defaultCheckedKeys: [],
      defaultExpandedKeys: [],
      treeDataValue: {},
      jstFilterText: '',
      searchProps: {children: 'children', label: 'name', isLeaf: 'leaf'},
      jstOrgTreeData: [],
      detailWidth: document.documentElement.clientWidth / 1.5,
      filterText: '',
      regionData: regionData,
      formInputStyle: {
        width: '200px'
      },
      height: document.documentElement.clientHeight - 180 + 'px;',
      deptName: '', depts: [], deptDatas: [], jobs: [], level: 3, roles: [],
      defaultProps: {children: 'children', label: 'name'},
      permission: {
        add: ['admin', 'user:user:add'],
        edit: ['admin', 'user:user:edit'],
        del: ['admin', 'user:user:del'],
        details: ['admin', 'user:user:details']
      },
      dialogVisible: false,
      selectedRow: {},
      rules: {
        deptCode: [
          {required: true, message: '请选择部门', trigger: 'blur'},
        ],
        roles: [
          {required: true, message: '请选择角色', trigger: 'blur'},
        ],
        username: [
          {required: true, message: '请输入用户名', trigger: 'blur'},
          {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
        ],
        password: [
          {required: true, message: '请输入密码', trigger: 'blur'},
          {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
        ],
        nickName: [
          {required: true, message: '请输入用户昵称', trigger: 'blur'},
          {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
        ],
        email: [
          {required: true, message: '请输入邮箱地址', trigger: 'blur'},
          {type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur'}
        ],
        phone: [
          {required: true, trigger: 'blur', validator: validPhone}
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  created() {
    this.$nextTick(() => {
      this.getJobs()
      this.getDepts()
      // this.getRoles()
      this.getDeptDatas()
      this.crud.toQuery()
      this.crud.msg.add = '新增成功'
    })
  },
  mounted: function () {
    this.dragControllerDiv();
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 180 + 'px;'
    }
    //获取即时通tree
    this.jstOrgTree();
  },
  methods: {
    // 岗位勾选事件
    cascaderChange(nodes) {
      if (this.$refs['refPositions']) {
        let checkedNodes = this.$refs['refPositions'].getCheckedNodes();
        if (checkedNodes.length > 0) {
          if (checkedNodes.filter(item => item.label.indexOf('领导') !== -1).length > 0) {
            this.jobs = this.filterTree(this.jobs, '造价员');
          } else if (checkedNodes.filter(item => item.label.indexOf('造价员') !== -1).length > 0) {
            this.jobs = this.filterTree(this.jobs, '领导');
          } else {
            this.jobs = this.filterTree(this.jobs, null);
          }
        } else {
          this.jobs = this.filterTree(this.jobs, null);
        }
      }
    },
    filterTree(nodes, query) {
      // 条件就是节点的title过滤关键字
      let predicate = function (node) {
        return node.name.indexOf(query) !== -1;
      };

      // 结束递归的条件
      if (!(nodes && nodes.length)) {
        return [];
      }
      let newChildren = [];
      for (let node of nodes) {
        //一、带父节点     以下两个条件任何一个成立，当前节点都应该加入到新子节点集中
        // 1. 子孙节点中存在符合条件的，即 subs 数组中有值
        // 2. 自己本身符合条件
        this.filterTree(node.children, query);
        if (predicate(node)) {
          node['disabled'] = true;
        } else {
          node['disabled'] = false;
        }
        newChildren.push(node);
      }
      return newChildren.length ? newChildren : [];
    },
    //收缩即时通tree
    visibleChange(val) {
      this.jstClear();
      if (!val) {
        this.changeTreeNodeStatus(this.$refs.jstTree.store.root)
      }
    },
    // 改变节点的状态
    changeTreeNodeStatus(node) {
      node.expanded = false
      for (let i = 0; i < node.childNodes.length; i++) {
        // 改变节点的自身expanded状态
        node.childNodes[i].expanded = false
        // 遍历子节点
        if (node.childNodes[i].childNodes.length > 0) {
          this.changeTreeNodeStatus(node.childNodes[i])
        }
      }
    },
    //即时通选中事件
    jstHandleNodeClick(data, node, nodeData) {
      if (data.children.length === 0) {
        this.form.jstUserId = data.id
        this.form.jstUserName = data.name
        this.treeDataValue = data
      }
    },
    dragControllerDiv() {
      // 保留this引用
      let data = this;
      let resize = document.getElementById("resize");
      resize.onmousedown = function (e) {
        // 颜色改变提醒
        resize.style.background = "#818181";
        let startX = e.clientX;
        resize.left = resize.offsetLeft;
        document.onmousemove = function (e) {
          // 计算并应用位移量
          let endX = e.clientX;
          let moveLen = endX - startX;
          startX = endX;
          data.detailWidth -= moveLen;
        };
        document.onmouseup = function () {
          // 颜色恢复
          resize.style.background = "";
          document.onmousemove = null;
          document.onmouseup = null;
        };
        return false;
      };
    },
    currDeptChange(val) {
      if (val) {
        this.getRoles(val)
      }
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    handleData(data) {
      if (data && data.length) {
        return data.map(citem => {
          return this.showCurrentData(this.jobs, citem, 'code')
        });
      }
    },
    jstOrgTree() {
      crudUser.getJstOrgTree().then(res => {
        this.jstOrgTreeData = res
      })
    },
    normalizer(node) {
      return {
        id: node.code,
        label: node.name,
        children: node.children
      }
    },
    formatArea(args) {
      if (!args[0]) return '';
      let region = formatArea(args, regionData);
      if (region.length > 0) {
        return region.map(item => item.label).join('');
      }
      return '';
    },
    [CRUD.HOOK.beforeAddCancel](crud) {
      this.visibleChange()
    },
    [CRUD.HOOK.beforeEditCancel](crud) {
      this.visibleChange()
    },
    [CRUD.HOOK.afterAddError](crud) {
      this.afterErrorMethod(crud)
    },
    [CRUD.HOOK.beforeToEdit](crud) {
      //即时通信设置默认值
      if (crud.form.jstUserId != undefined && crud.form.jstUserId.length > 0) {
        this.defaultCheckedKeys = [crud.form.jstUserId]
        this.defaultExpandedKeys = [crud.form.jstUserId]
        this.$nextTick(() => {
          this.$refs.jstTree.setCurrentKey(crud.form.jstUserId)
        })
      }
    },
    [CRUD.HOOK.afterEditError](crud) {
      this.afterErrorMethod(crud)
    },
    [CRUD.HOOK.beforeToAdd](crud) {
      this.ifOrgAdmin = 0;
      this.jstClear();
    },
    jstClear() {
      this.jstFilterText = ''
      this.form.jstUserId = ''
      this.form.jstUserName = ''
    },
    afterErrorMethod(crud) {
      // 恢复select
      const initRoles = []
      userRoles.forEach(function (role, index) {
        initRoles.push(role.id)
      })
      crud.form.roles = initRoles
    },
    [CRUD.HOOK.afterRefresh](crud) {
      this.crud.data.map(item => {
        item.statusCd = item.statusCd.toString();
      })
      return true;
    },

    // 提交前做的操作
    [CRUD.HOOK.afterValidateCU](crud) {
      this.crud.formData = this.getFormData(crud.form);
      if (crud.form.password !== '******') {
        this.crud.formData.passwordCode = encrypt(crud.form.password)
      }
      return true
    },
    getFormData(form) {
      let editField = {};
      if (this.crud.status.edit == 1) {
        editField = {
          id: form.id,
          dataVersion: form.dataVersion,
        }
      }
      let convertPositions = null
      if (this.$refs["refPositions"]) {
        convertPositions = this.$refs["refPositions"].getCheckedNodes().length === 0 ? null : [...this.$refs["refPositions"].getCheckedNodes()].map(item => ({
          key: item.value,
          name: item.label
        }))
      }
      return {
        ...editField,
        ...form,
        provinceCd: form.region === null ? '' : form.region[0],
        cityCd: form.region === null ? '' : form.region[1],
        districtCd: form.region === null ? '' : form.region[2],
        password: form.password,
        positions: convertPositions,
        roles: form.roles.map(item => ({name: this.showCurrentData(this.roles, item, 'id'), key: item})),
      }
    },
    // 获取左侧部门数据
    getDeptDatas() {
      const sort = 'id,desc'
      const params = {sort: sort}
      if (this.deptName) {
        params['name'] = this.deptName
      }
      getDepts(params).then(res => {
        this.deptDatas = res
      })
    },
    // 获取弹窗内部门数据
    getDepts() {
      getDepts({}).then(res => {
        this.buildData(res, 'children');
        this.depts = res;
      })
    },
    buildDepts(depts) {
      depts.forEach(data => {
        if (data.children) {
          this.buildDepts(data.children)
        }
        data.label = data.name
      })
    },
    // 切换部门
    handleNodeClick(data) {
      this.query.orgCodeEq = data.code;
      this.crud.toQuery()
    },
    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('此操作将 "' + this.dict.label.user_status[val] + '" ' + data.username + ', 是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        crudUser.edit(data).then(res => {
          this.crud.notify(this.dict.label.user_status[val] + '成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
        }).catch(() => {
          data.enabled = !data.enabled
        })
      }).catch(() => {
        data.enabled = !data.enabled
      })
    },
    // 获取弹窗内角色数据
    getRoles(deptCode) {
      getRoleDict({
        deptCode: deptCode
      }).then(res => {
        this.roles = res;
      }).catch(() => {
      })
    },
    // 获取弹窗内岗位数据
    getJobs(id) {
      let typeCd = this.user.orgTypeCd;
      getAllJob({positionTypeCd: typeCd}).then(res => {
        this.buildData(res, 'children');
        this.jobs = res;
      }).catch(() => {
      })
    },
    // 点击部门搜索对应的岗位
    selectFun(node, instanceId) {
      this.getJobs(node.id)
      this.form.job.id = null
    },
    // 获取权限级别
    getRoleLevel() {
      getLevel().then(res => {
        this.level = res.level
      }).catch(() => {
      })
    },
    checkboxT(row, rowIndex) {
      return row.id !== this.user.id
    },
    //重置密码
    resetPassword() {
      this.selectedRow.passwordCode = encrypt('Gczjglxt@2.0');
      this.selectedRow.password = encrypt('Gczjglxt@2.0');
      crudUser.resetPassword(this.selectedRow).then(res => {
        this.$message.success("操作成功")
        this.dialogVisible = false;
      })
    },
    //重置密码
    resetDialog(row) {
      this.dialogVisible = true;
      this.selectedRow = row;
    },
    removeFile(file) {
      this.form.imgList = this.form.imgList.filter(p => p.id !== file.id)
    }
  }
}
</script>

<style type="css" scoped>
#box {
  width: 100%;
  height: 700px;
  position: relative;
  overflow: hidden;
  display: flex;
}

#left {
  height: 100%;
  flex: 1;
}

#resize {
  width: 8px;
  height: 100%;
  cursor: w-resize;
}

#right {
  height: 100%;
}

.el-scrollbar >>> .el-scrollbar__wrap {
  overflow-x: hidden;
}

>>> .el-tree-node__label {
  font-size: 12px;
}

.el-select-dropdown__item.hover, .el-select-dropdown__item:hover {
  background-color: #ffffff;
}

>>> .vue-treeselect__single-value {
  color: #333;
  line-height: 28px;
}

>>> .el-input--small .el-input__inner {
  height: 30.5px;
  line-height: 32px;
}

>>> .vue-treeselect__control {
  padding-left: 5px;
  padding-right: 5px;
  display: table;
  table-layout: fixed;
  width: 100%;
  height: 25px;
  border: 1px solid #ddd;
  border-radius: 5px;
  background: #fff;
  -webkit-transition-duration: 200ms;
  transition-duration: 200ms;
  -webkit-transition-property: border-color, width, height, background-color, opacity, -webkit-box-shadow;
  transition-property: border-color, box-shadow, width, height, background-color, opacity, -webkit-box-shadow;
  -webkit-transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
}

>>> .el-form-item--small .el-form-item__label {
  line-height: 10px !important;
}
</style>
