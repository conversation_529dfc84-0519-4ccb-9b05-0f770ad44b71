<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--侧边部门数据-->
      <el-col :xs="9" :sm="6" :md="4" :lg="4" :xl="4">
        <el-tree
          :data="deptDatas"
          :props="defaultProps"
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
        >
          <template v-slot="{ node, data }">
            <span class="span-ellipsis">
              <span :title="node.label">{{ node.label }}</span>
            </span>
          </template>
        </el-tree>
      </el-col>
      <!--用户数据-->
      <el-col :xs="15" :sm="18" :md="20" :lg="20" :xl="20">
        <!--表格渲染-->
        <el-table ref="table" v-loading="crud.loading" :data="crud.data" style="width: 100%;"
                  @selection-change="crud.selectionChangeHandler" border>
          <el-table-column :show-overflow-tooltip="true" prop="username" label="用户名"/>
          <el-table-column :show-overflow-tooltip="true" prop="realName" label="真实姓名"/>
          <el-table-column :show-overflow-tooltip="true" prop="phone" width="100" label="手机号"/>
          <el-table-column :show-overflow-tooltip="true" prop="nickName" width="100" label="昵称"/>
          <el-table-column :show-overflow-tooltip="true" width="125" prop="email" label="邮箱"/>
          <el-table-column :show-overflow-tooltip="true" width="125" label="是否组织管理员">
            <template v-slot="scope">
              {{ showCurrentData(dict.if_admin, scope.row.ifOrgAdmin) }}
            </template>
          </el-table-column>
          <el-table-column :show-overflow-tooltip="true" width="125" prop="statusCd" label="状态">
            <template v-slot="scope">
              {{ showCurrentData(dict.user_status, scope.row.statusCd) }}
            </template>
          </el-table-column>
          <el-table-column :show-overflow-tooltip="true" width="125" prop="deptCode" label="部门">
            <template v-slot="scope">
              {{ showCurrentData(depts, scope.row.deptCode, 'code') }}
            </template>
          </el-table-column>
          <el-table-column prop="positions" label="岗位">
            <template v-slot="scope">
              <el-tag style="margin-right: 10px" v-for="(item, key) in  scope.row.positions" :key="key">{{item.name}}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column :show-overflow-tooltip="true" width="125" label="省市区">
            <template v-slot="scope">
              {{ formatArea([scope.row.provinceCd, scope.row.cityCd, scope.row.districtCd]) }}
            </template>
          </el-table-column>
          <el-table-column width="200" prop="roleIds" label="角色">
            <template v-slot="scope">
              <el-tag style="margin-right: 10px" v-for="(item, key) in scope.row.roles" :key="key">{{item.name}}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createDate" width="140" label="创建日期">
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination/>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import {isvalidPhone} from '@/utils/validate'
  import {getAll, getLevel} from '@/api/system/role'
  import CRUD, {presenter, header, form, crud} from '@crud/crud'
  import rrOperation from '@crud/RR.operation'
  import crudOperation from '@crud/CRUD.operation'
  import udOperation from '@crud/UD.operation'
  import pagination from '@crud/Pagination'
  import Treeselect from '@riophae/vue-treeselect'
  import {mapGetters} from 'vuex'
  import '@riophae/vue-treeselect/dist/vue-treeselect.css'
  import {regionData} from 'element-china-area-data'
  import crudUser from './config/request'

  const getDepts = crudUser.getDepts;
  const getAllJob = crudUser.getAllJob;
  const getRoles = crudUser.getRoles;
  //注入配置文件
  import config from './config/index'

  const formFields = config.getFields('form');
  const tableListFields = config.getFields('tableList');
  import {flatten, formatArea} from '@/utils/index'

  let userRoles = []
  const defaultForm = {
    region: null,
    realName: null,
    statusCd: '1',
    ifOrgAdmin: '0',
    password: null,
    username: null,
    nickName: null,
    email: null,
    roles: null,
    positions: null,
    detailAddress: null,
    deptCode: null,
    phone: null,

  }
  export default {
    name: 'User',
    components: {Treeselect, crudOperation, rrOperation, udOperation, pagination},
    cruds() {
      return CRUD({title: '用户', sort: ['asc'], url: config.requestUrl, crudMethod: {...crudUser}})
    },
    mixins: [presenter(), header(), form(defaultForm), crud()],
    // 数据字典
    dicts: ['if_admin', 'user_status'],
    data() {
      // 自定义验证
      const validPhone = (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入电话号码'))
        } else if (!isvalidPhone(value)) {
          callback(new Error('请输入正确的11位手机号码'))
        } else {
          callback()
        }
      }
      return {
        regionData: regionData,
        formInputStyle: {
          width: '178px'
        },
        height: document.documentElement.clientHeight - 180 + 'px;',
        deptName: '', depts: [], deptDatas: [], jobs: [], level: 3, roles: [],
        defaultProps: {children: 'children', label: 'name'},
        permission: {
          add: ['admin', 'user:user:add'],
          edit: ['admin', 'user:user:edit'],
          del: ['admin', 'user:user:del'],
          details: ['admin', 'user:user:details']
        },
        enabledTypeOptions: [
          {key: 'true', display_name: '激活'},
          {key: 'false', display_name: '锁定'}
        ],
        rules: {
          username: [
            {required: true, message: '请输入用户名', trigger: 'blur'},
            {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
          ],
          password: [
            {required: true, message: '请输入密码', trigger: 'blur'},
            {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
          ],
          nickName: [
            {required: true, message: '请输入用户昵称', trigger: 'blur'},
            {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
          ],
          email: [
            {required: true, message: '请输入邮箱地址', trigger: 'blur'},
            {type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur'}
          ],
          phone: [
            {required: true, trigger: 'blur', validator: validPhone}
          ]
        }
      }
    },
    computed: {
      ...mapGetters([
        'user'
      ])
    },
    created() {
      this.$nextTick(() => {
        this.getJobs()
        this.getDepts()
        this.getRoles()

        this.getDeptDatas()
        this.crud.toQuery()
        this.crud.msg.add = '新增成功'
      })
    },
    mounted: function () {
      const that = this
      window.onresize = function temp() {
        that.height = document.documentElement.clientHeight - 180 + 'px;'
      }
      //避免一开始请求过多
      this.query.ownerOrgCodeEq='ownerOrgCode'
    },
    methods: {
      handleData(data) {
        if (data && data.length) {

          return data.map(citem => {
            return this.showCurrentData(this.jobs, citem, 'code')
          });
        }
      },
      normalizer(node) {
        return {
          id: node.code,
          label: node.name,
          children: node.children
        }
      },
      formatArea(args) {
        if (!args[0]) return '';
        let region = formatArea(args, regionData);
        if (region.length > 0) {
          return region.map(item => item.label).join('');
        }

        return '';

      },

      [CRUD.HOOK.afterAddError](crud) {
        this.afterErrorMethod(crud)
      },
      [CRUD.HOOK.afterEditError](crud) {
        this.afterErrorMethod(crud)
      },
      afterErrorMethod(crud) {
        // 恢复select
        const initRoles = []
        userRoles.forEach(function (role, index) {
          initRoles.push(role.id)
        })
        crud.form.roles = initRoles
      },
      [CRUD.HOOK.afterRefresh](crud) {
        this.crud.data.map(item => {
          item.statusCd = item.statusCd.toString();
        })
        return true;

      },
      // 获取左侧部门数据
      getDeptDatas() {
        const sort = 'id,desc'
        const params = {sort: sort}
        if (this.deptName) {
          params['name'] = this.deptName
        }
        getDepts(params).then(res => {
          this.deptDatas = res
        })
      },
      // 获取弹窗内部门数据
      getDepts() {
        getDepts({}).then(res => {
          this.buildData(res, 'children');
          this.depts = res;
        })
      },
      buildDepts(depts) {
        depts.forEach(data => {
          if (data.children) {
            this.buildDepts(data.children)
          }
          data.label = data.name
        })
      },
      // 切换部门
      handleNodeClick(data) {
        this.query.ownerOrgCodeEq = data.code;
        this.crud.toQuery()
      },
      // 改变状态
      changeEnabled(data, val) {
        this.$confirm('此操作将 "' + this.dict.label.user_status[val] + '" ' + data.username + ', 是否继续？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          crudUser.edit(data).then(res => {
            this.crud.notify(this.dict.label.user_status[val] + '成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
          }).catch(() => {
            data.enabled = !data.enabled
          })
        }).catch(() => {
          data.enabled = !data.enabled
        })
      },
      // 获取弹窗内角色数据
      getRoles() {
        getRoles({
          current: 1,
          size: 1000,
          sort: 'asc'
        }).then(res => {
          this.roles = res.records;
        }).catch(() => {
        })
      },
      // 获取弹窗内岗位数据
      getJobs(id) {
        getAllJob().then(res => {
          this.buildData(res, 'children');
          this.jobs = res;
        }).catch(() => {
        })
      },
      // 点击部门搜索对应的岗位
      selectFun(node, instanceId) {
        this.getJobs(node.id)
        this.form.job.id = null
      },
      // 获取权限级别
      getRoleLevel() {
        getLevel().then(res => {
          this.level = res.level
        }).catch(() => {
        })
      },
      checkboxT(row, rowIndex) {
        return row.id !== this.user.id
      }
    }
  }
</script>

<style scoped>
  .span-ellipsis {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
</style>
