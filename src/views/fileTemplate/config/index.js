export default {
  //获取某类型字段列表
  getFields(type) {
    return this.parames.filter(item => {
      return item.whereShow.some((citem) => citem == type)
    })
  },
  //加工页面formData
  getDefaultForm(data) {
    var json = {};
    data.map(item => {
      json[item.fieldName] = item.defaultVal;
    })
    return json;
  },
  //页面请求地址
  requestUrl: '/system/fileTemplate',

  //页面字段参数，可根据实际需求扩展
  parames: [
    {
      label: '',
      fieldName: 'id',
      defaultVal: '',
      type: 'input',
      whereShow: []
    },
    {
      label: '模板名称',
      fieldName: 'templateName',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '附件',
      fieldName: 'templateFileName',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '模板附件',
      fieldName: 'templateUrl',
      defaultVal: '',
      type: 'file',
      whereShow: ['form', 'add', 'edit']
    },
    {
      label: '模板类型',
      fieldName: 'templateType',
      defaultVal: '',
      type: 'select',
      whereShow: ['tableList','form', 'add', 'edit']
    },
    {
      label: '',
      fieldName: 'createDate',
      defaultVal: '',
      type: 'input',
      whereShow: []
    },
    {
      label: '',
      fieldName: 'updateDate',
      defaultVal: '',
      type: 'input',
      whereShow: []
    },
  ]
}
