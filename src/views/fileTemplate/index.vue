<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.templateName" clearable size="small" placeholder="输入模板名称搜索" style="width: 200px;"
                  class="filter-item" @keyup.enter.native="crud.toQuery"/>
        <rrOperation/>
      </div>
      <crudOperation :permission="permission"/>
    </div>
    <!--表单组件-->
    <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU"
               :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
      <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
        <template v-for="(item, key) in formFields">
          <template v-if="item.type == 'file'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key" style="text-align: left">
              <el-upload
                class="upload-demo"
                action=""
                :multiple="false"
                :limit="1"
                :show-file-list="true"
                :http-request="httpRequest"
                :file-list="fileList"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
                :on-exceed="handleExceed"
              >
                <el-button size="small" plain>选择文件</el-button>
              </el-upload>
            </el-form-item>
          </template>
          <template v-if="item.type == 'radio'">
            <el-form-item label="状态" :prop="item.fieldName" :key="key">
              <el-radio v-for="item in dict.dept_status" :key="item.id" v-model="form.enabled" :label="item.value">{{
                item.label }}
              </el-radio>
            </el-form-item>
          </template>
          <template v-if="item.type == 'input'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-input v-model="form[item.fieldName]" :style="formInputStyle"/>
            </el-form-item>
          </template>
          <template v-if="item.type == 'date'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-date-picker
                v-model="form[item.fieldName]"
                value-format="yyyy年MM月dd日 HH:mm:ss"
                type="datetime"
                :style="formInputStyle"
                placeholder="选择日期时间">
              </el-date-picker>
            </el-form-item>
          </template>
          <template v-if="item.type == 'select'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]" :style="formInputStyle" placeholder="请选择">
                <el-option
                  v-for="(item) in dict[item.fieldName]"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-if="item.type == 'textarea'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-input :type="item.type" v-model="form[item.fieldName]" style="width: 370px;"/>
            </el-form-item>
          </template>
        </template>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>
    <!--表格渲染-->
    <el-table ref="table" v-loading="crud.loading" :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              default-expand-all :data="crud.data" row-key="id" @select="crud.selectChange"
              @select-all="crud.selectAllChange" @selection-change="crud.selectionChangeHandler" border>
      <el-table-column :selectable="checkboxT" type="selection" width="55"/>
      <template v-for="(item, key) in tableListFields">
        <el-table-column :label="item.label" :prop="item.fieldName" :key="key">
          <template v-slot="scope">

            <template v-if="item.fieldName == 'templateFileName'">
              <el-link  @click="downLoad(scope.row)" :underline="false">{{scope.row['templateFileName']}}</el-link>
            </template>
            <template v-else-if="item.fieldName == 'templateType'">
              {{ showCurrentData(dict.templateType, scope.row.templateType) }}
            </template>
            <template v-else>
              {{ item.fieldName == 'createTime' ? parseTime(scope.row[ item.fieldName ]) : scope.row[ item.fieldName ] }}
            </template>
          </template>

        </el-table-column>
      </template>

      <el-table-column v-permission="['admin','system:fileTemplate:edit','system:fileTemplate:del']" label="操作"
                       width="180px" align="center" fixed="right">
        <template v-slot="scope">
          <div style="display:inline-block;">
            <udOperation
                :data="scope.row"
                :permission="permission"
                :disabled-dle="scope.row.id === 1"
                msg="确定删除吗,如果存在下级节点则一并删除，此操作不能撤销！"
              />
          </div>
          <div style="display:inline-block;">
              <el-button type="info" size='mini'  @click="downLoad(scope.row)">下载</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination/>
  </div>
</template>

<script>
  import crudRequest from './config/request'
  import '@riophae/vue-treeselect/dist/vue-treeselect.css'
  import CRUD, {presenter, header, form, crud} from '@crud/crud'
  import rrOperation from '@crud/RR.operation'
  import crudOperation from '@crud/CRUD.operation'
  import udOperation from '@crud/UD.operation'
  import pagination from '@crud/Pagination'
  import request from '@/utils/request'
  import {downloadFile} from '@/utils/index'
  import {upload} from '@/utils/upload'
  //注入配置文件
  import config from './config'

  const formFields = config.getFields('form');
  const tableListFields = config.getFields('tableList');
  const defaultForm = config.getDefaultForm(formFields);
  export default {
    name: 'simpleList',
    components: {crudOperation, rrOperation, udOperation, pagination},
    cruds() {
      return CRUD({title: '模板', url: config.requestUrl, crudMethod: {...crudRequest}})
    },
    mixins: [presenter(), header(), form(defaultForm), crud()],
    // 设置数据字典
    dicts: ['project_types','templateType'],
    data() {
      return {
        formFields: formFields,
        tableListFields: tableListFields,
        rules: {
          templateName: [{required: true, message: '请输入名称', trigger: 'blur'}],
          templateUrl: [{required: true, message: '请上传模板附件', trigger: 'blur'}],
          templateType: [{required: true, message: '请选择模板类型', trigger: 'blur'}]
        },
        permission: {
          add: ['admin', 'system:fileTemplate:add'],
          edit: ['admin', 'system:fileTemplate:edit'],
          del: ['admin', 'system:fileTemplate:del']
        },
        enabledTypeOptions: [
          {key: 'true', display_name: '正常'},
          {key: 'false', display_name: '禁用'}
        ],
        fileList: [],
      }
    },
    methods: {
      checkboxT(row, rowIndex) {
        return row.id !== 1
      },
      handleExceed(){
        this.$message.warning('超出文件上限');
      },
      handlePreview(file) {
        const ele = document.createElement('a');
        ele.target = "_blank";
        ele.setAttribute('href', this.filePath + file.url);
        ele.click();
      },
      httpRequest(file) {
        this.loading = true;
        upload('general/file/upload', file.file)
          .then(res => {
            this.fileList.push({name: file.file.name, url: res.data.data})
            this.form.templateFileName = file.file.name
            this.form.templateUrl = res.data.data
            this.loading = false;
          })
          .catch(e => {
            this.loading = false;
            this.crud.notify('上传失败！', 'error')
          })
      },
      handleRemove(file, fileList) {
        this.fileList = []
        this.form.templateFileName = ''
        this.form.templateUrl = ''
      },
      //下载附件
      downLoad(scope) {
        request({
          url: '/general/file/download?uri=' + scope.templateUrl,
          type: 'get',
          responseType: 'blob'
        })
          .then(res => {
            downloadFile(res,scope.templateFileName)
          })
      },
      [CRUD.HOOK.beforeToAdd](crud, record) {
        this.fileList = []
        crud.resetForm();
      },
      [CRUD.HOOK.beforeToEdit](crud, record) {
        this.fileList = []
        this.fileList.push({name: record.templateFileName, url: record.templateUrl})
      },
    }
  }
</script>

