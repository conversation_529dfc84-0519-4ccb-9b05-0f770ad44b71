export default {
  //获取某类型字段列表
  getFields(type) {
    return this.parames.filter(item => {
      return item.whereShow.some((citem) => citem == type)
    })
  },
  //加工页面formData
  getDefaultForm(data) {
    var json = {};
    data.map(item => {
      json[item.fieldName] = item.defaultVal;
    })
    return json;
  },
  //页面请求地址
  requestUrl: '/workflow/workflowTask/historyTask',

  //页面字段参数，可根据实际需求扩展
  parames: [
    {
      label: '项目名称',
      fieldName: 'projectName',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '报审编号',
      fieldName: 'auditCode',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '状态',
      fieldName: 'auditStatus',
      defaultVal: '',
      type: 'select',
      whereShow: ['tableList']
    },
    {
      label: '创建人',
      fieldName: 'createUserName',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '最后执行人',
      fieldName: 'executeUser',
      defaultVal: '',
      type: 'select',
      whereShow: ['tableList']
    },
    {
      label: '创建时间',
      fieldName: 'createDate',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '完成时间',
      fieldName: 'updateDate',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
  ]
}
