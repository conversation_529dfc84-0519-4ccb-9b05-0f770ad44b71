<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.name" clearable size="small" placeholder="输入名称搜索" style="width: 200px;"
                  class="filter-item" @keyup.enter.native="crud.toQuery"/>
        <el-date-picker
          @change="crud.toQuery"
          v-model="query.createTime"
          :default-time="['00:00:00','23:59:59']"
          type="daterange"
          range-separator=":"
          size="small"
          class="date-item"
          value-format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
        <rrOperation/>
      </div>
      <crudOperation :permission="permission"/>
    </div>

    <el-dialog :visible.sync="imgPreview" title="实时流程图" width="80%">
      <DrawingPreview :svgData="currentImgData" :imgPreview="imgPreview"/>
    </el-dialog>
    <!--表单组件-->
    <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU"
               :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
      <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
        <template v-for="(item, key) in formFields">
          <template v-if="item.type == 'radio'">
            <el-form-item label="状态" :prop="item.fieldName" :key="key">
              <el-radio v-for="item in dict.dept_status" :key="item.id" v-model="form.enabled" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-form-item>
          </template>
          <template v-if="item.type == 'input'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-input v-model="form[item.fieldName]" :style="formInputStyle"/>
            </el-form-item>
          </template>
          <template v-if="item.type == 'date'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-date-picker
                v-model="form[item.fieldName]"
                value-format="yyyy年MM月dd日 HH:mm:ss"
                type="datetime"
                :style="formInputStyle"
                placeholder="选择日期时间">
              </el-date-picker>
            </el-form-item>
          </template>
          <template v-if="item.type == 'select'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]" :style="formInputStyle" placeholder="请选择">
                <el-option
                  v-for="(item) in dict.project_types"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-if="item.type == 'textarea'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-input :type="item.type" v-model="form[item.fieldName]" style="width: 370px;"/>
            </el-form-item>
          </template>
        </template>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>

    <!--表格渲染-->
    <el-table ref="table" v-loading="crud.loading" :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              default-expand-all :data="crud.data" row-key="id" @select="crud.selectChange"
              @select-all="crud.selectAllChange" @selection-change="crud.selectionChangeHandler" border>
      <template v-for="(item, key) in tableListFields">
        <el-table-column :label="item.label" :prop="item.fieldName" :key="key">
          <template v-slot="scope">
            <template v-if="item.fieldName == 'auditStatus'">
              {{ showCurrentData(dict.audit_status, scope.row[item.fieldName]) }}
            </template>
            <template v-else-if="item.fieldName == 'createDate'">
              {{ scope.row[item.fieldName] ? parseTime(scope.row[item.fieldName]) : '' }}
            </template>
            <template v-else-if="item.fieldName == 'updateDate'">
              {{ scope.row[item.fieldName] ? parseTime(scope.row[item.fieldName]) : '' }}
            </template>
            <template v-else>
              {{ scope.row[item.fieldName] }}
            </template>
          </template>
        </el-table-column>
      </template>

      <el-table-column v-permission="['admin','taskManagement:historyTaskPool:lookProcess']" label="操作" width="130px"
                       align="center" fixed="right">
        <template v-slot="scope">
          <el-button v-permission="['admin','taskManagement:historyTaskPool:lookProcess']" size="mini"
                     style="margin-right: 3px;" type="text" @click="showFlowImg(scope.row)">查看实时流程图
          </el-button>
          <el-button v-permission="['admin','taskManagement:historyTaskPool:lookProcess']" size="mini"
                     style="margin-right: 3px;" type="text" @click="seeAuditRecords(scope.row)">查看审核记录
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog :visible.sync="auditRecordsVisible" title="审核记录" width="80%">
      <el-table
        :data="auditRecords.records"
        v-loading="auditLoading"
        style="width: 100%" border>

        <el-table-column type="expand">
          <template v-slot="props">
            <p v-if="!props.row.formContent">暂无数据</p>
            <PreviewForm
              v-if="props.row.formContent"
              :jsonData="props.row.formContent"
              :defaultValue="props.row.formData"
              :record="props.row"
              :isHideBtn="true"
              :disabled="true"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="任务名称"
          prop="name">
        </el-table-column>
        <el-table-column
          label="执行人"
          prop="assigneeName">
        </el-table-column>
        <el-table-column
          label="创建时间"
          prop="createdDate">
        </el-table-column>
        <el-table-column
          label="完成时间"
          prop="completedDate">
        </el-table-column>
        <el-table-column
          label="持续时间"
        >
          <template v-slot="scope">
            {{ scope.row.duration | timeConsuming }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!--分页组件-->
    <pagination/>
  </div>
</template>

<script>
import crudRequest from './config/request'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import PreviewForm from '@/components/PreviewForm'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import {timeConsuming} from '@/utils/datetime'
import DrawingPreview from '@/components/DrawingPreview/index'
//注入配置文件
import config from './config/index'

const formFields = config.getFields('form');
const tableListFields = config.getFields('tableList');
const defaultForm = config.getDefaultForm(formFields);
export default {
  name: 'simpleList',
  components: {crudOperation, rrOperation, udOperation, pagination, DrawingPreview, PreviewForm},
  cruds() {
    return CRUD({title: '模板', url: config.requestUrl, crudMethod: {...crudRequest}, sort: [], optShow: {reset: true}})
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 设置数据字典
  dicts: ['audit_status'],

  data() {
    return {
      imgPreview: false,
      currentImgData: '',
      formFields: formFields,
      tableListFields: tableListFields,
      rules: {
        name: [
          {required: true, message: '请输入名称', trigger: 'blur'}
        ]
      },
      permission: {},
      auditPageSize: 10,
      auditPage: 1,
      auditRecordsVisible: false,
      auditRecords: {},
      auditRecord: {},
      auditLoading: false,
      proActiveName: '0',
    }
  },
  methods: {
    //耗时转换
    timeConsuming(time) {
      return timeConsuming(time);
    },
    // 查看实时流程图
    showFlowImg(row) {
      crudRequest.getFlowImgByProcessInstanceId(row)
        .then(res => {
          this.currentImgData = res;
          this.imgPreview = true;
        })
    },
    checkboxT(row, rowIndex) {
      return row.id !== 1
    },
    seeAuditRecords(record) {
      this.auditRecordsVisible = true;
      this.auditPage = 1;
      this.getAuditPage(record);
    },
    getAuditPage(record) {
      this.auditLoading = true;
      crudRequest.getPageTask({
        current: this.auditPage,
        size: this.auditPageSize,
        processInstanceId: record.processInstanceId
      })
        .then(res => {
          res.records.map(item => {
            item.formContent = item.formContent && JSON.parse(item.formContent);
            item.formData = item.formData && JSON.parse(item.formData);
          })
          this.auditRecords = res;
          this.auditLoading = false;
        })
        .catch(err => {
          this.auditLoading = false;
        })
    },
  }
}
</script>

