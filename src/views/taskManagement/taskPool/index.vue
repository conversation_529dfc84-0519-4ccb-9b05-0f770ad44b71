<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.name" clearable size="small" placeholder="输入名称搜索" style="width: 200px;"
                  class="filter-item" @keyup.enter.native="crud.toQuery"/>
        <el-date-picker
          @change="crud.toQuery"
          v-model="query.createTime"
          :default-time="['00:00:00','23:59:59']"
          type="daterange"
          range-separator=":"
          size="small"
          class="date-item"
          value-format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
        <rrOperation/>
      </div>
      <crudOperation :permission="permission"/>
    </div>

    <el-dialog :visible.sync="imgPreview" title="实时流程图" width="80%">
      <DrawingPreview :svgData="currentImgData" :imgPreview="imgPreview"/>
    </el-dialog>
    <el-dialog :visible.sync="auditFormVisible" title="审核任务" width="80%">
      <auditForm
        :submit="auditFormSubmit"
        :visible="auditFormVisible"
        :record="currentRecordAuditForm"
        :addResultAuditFile="false"
      />
    </el-dialog>
    <!--表单组件-->
    <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU"
               :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
      <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
        <template v-for="(item, key) in formFields">
          <template v-if="item.type == 'radio'">
            <el-form-item label="状态" :prop="item.fieldName" :key="key">
              <el-radio v-for="item in dict.dept_status" :key="item.id" v-model="form.enabled" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-form-item>
          </template>
          <template v-if="item.type == 'input'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-input v-model="form[item.fieldName]" :style="formInputStyle"/>
            </el-form-item>
          </template>
          <template v-if="item.type == 'date'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-date-picker
                v-model="form[item.fieldName]"
                value-format="yyyy年MM月dd日 HH:mm:ss"
                type="datetime"
                :style="formInputStyle"
                placeholder="选择日期时间">
              </el-date-picker>
            </el-form-item>
          </template>
          <template v-if="item.type == 'select'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]" :style="formInputStyle" placeholder="请选择">
                <el-option
                  v-for="(item) in dict.project_types"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-if="item.type == 'textarea'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-input :type="item.type" v-model="form[item.fieldName]" style="width: 370px;"/>
            </el-form-item>
          </template>
        </template>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>
    <!--表格渲染-->
    <el-table ref="table" v-loading="crud.loading" :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              default-expand-all :data="crud.data" row-key="id" @select="crud.selectChange"
              @select-all="crud.selectAllChange" @selection-change="crud.selectionChangeHandler" border>
      <el-table-column :selectable="checkboxT" type="selection" width="55"/>
      <template v-for="(item, key) in tableListFields">
        <el-table-column :label="item.label" :prop="item.fieldName" :key="key">
          <template v-slot="scope">
            <template v-if="item.fieldName == 'statusCd'">
              {{ showCurrentData(dict.task_status, scope.row[item.fieldName]) }}
            </template>
            <template v-else-if="item.fieldName == 'ifDelegateTask'">
              {{ showCurrentData(dict.yes_no, scope.row[item.fieldName]) }}
            </template>
            <template v-else-if="item.fieldName == 'delegateTaskStatusCd'">
              {{ showCurrentData(dict.delegate_task_status, scope.row[item.fieldName]) }}
            </template>
            <template v-else>
              {{ scope.row[item.fieldName] }}
            </template>
          </template>
        </el-table-column>
      </template>

      <el-table-column v-permission="[
                'admin','taskManagement:taskPool:lookProcess',
                'taskManagement:taskPool:finishTask',
                'taskManagement:taskPool:rollbackTask',
                'taskManagement:taskPool:completionCommission',
                'taskManagement:taskPool:resolveDelegation',
                'taskManagement:taskPool:designatedExecutor',
                'taskManagement:taskPool:replacementExecutor'
                ]" label="操作" width="130px" align="center" fixed="right">
        <template v-slot="scope">
          <el-button v-permission="['admin','taskManagement:taskPool:lookProcess']" size="mini"
                     style="margin-right: 3px;" type="text" @click="showFlowImg(scope.row)">查看实时流程图
          </el-button>
          <div v-if="scope.row.statusCd == 3 && scope.row.delegateTaskStatusCd != 1">
            <el-button v-permission="['admin','taskManagement:taskPool:finishTask']" size="mini"
                       style="margin-right: 3px;" type="text" @click="auditTask(scope.row)">完成任务
            </el-button>
            <br/>
            <el-button v-permission="['admin','taskManagement:taskPool:rollbackTask']" size="mini"
                       style="margin-right: 3px;" type="text" @click="rollbackTask({taskId: scope.row.id})">驳回任务
            </el-button>
          </div>
          <div v-if="scope.row.statusCd == 3 && scope.row.delegateTaskStatusCd == 1">
            <el-button v-permission="['admin','taskManagement:taskPool:completionCommission']" size="mini"
                       style="margin-right: 3px;" type="text" @click="auditTask(scope.row, 1)">完成委托任务
            </el-button>
            <el-button v-permission="['admin','taskManagement:taskPool:resolveDelegation']" size="mini"
                       style="margin-right: 3px;" type="text" @click="auditTask(scope.row, 2)">解决委托任务
            </el-button>
          </div>

          <DesignatedPersonnel
            v-permission="['admin','taskManagement:taskPool:designatedExecutor']"
            v-if="!scope.row.assigneeName"
            :record="scope.row"
            btnText="指定执行人"
            @cascaderChange="crud.toQuery"
          />
          <DesignatedPersonnel
            v-permission="['admin','taskManagement:taskPool:replacementExecutor']"
            v-if="scope.row.statusCd == 3"
            :record="scope.row"
            btnText="更换执行人"
            @cascaderChange="crud.toQuery"
          />
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination/>
  </div>
</template>

<script>
import crudRequest from './config/request'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import auditForm from '@/components/auditForm'
import DesignatedPersonnel from '@/components/DesignatedPersonnel'
import DrawingPreview from '@/components/DrawingPreview/index'
import {getFormData} from '@/views/workflow/workflowForm/config/request'

//注入配置文件
import config from './config/index'

const formFields = config.getFields('form');
const tableListFields = config.getFields('tableList');
const defaultForm = config.getDefaultForm(formFields);
export default {
  name: 'simpleList',
  components: {crudOperation, DrawingPreview, rrOperation, udOperation, pagination, auditForm, DesignatedPersonnel},
  cruds() {
    return CRUD({title: '模板', url: config.requestUrl, crudMethod: {...crudRequest}, sort: [], optShow: {reset: true}})
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 设置数据字典
  dicts: ['task_status', 'yes_no', 'delegate_task_status'],
  data() {
    return {
      currentRecordAuditForm: {},
      auditFormVisible: false,
      imgPreview: false,
      currentImgData: '',
      formFields: formFields,
      tableListFields: tableListFields,
      rules: {
        name: [
          {required: true, message: '请输入名称', trigger: 'blur'}
        ]
      },
      permission: {},
    }
  },
  methods: {
    // 查看实时流程图
    showFlowImg(row) {
      crudRequest.getFlowImgByProcessInstanceId(row)
        .then(res => {
          this.currentImgData = res;
          this.imgPreview = true;
        })
    },
    // 指定执行人
    setAssignee(row) {
      // 弹出选择人员列表
      // 将选择的人员提交后台
      crudRequest.assignTask(row.id, "user_user_1").then(res => {
        this.crud.notify('操作成功', 'success');
        this.crud.toQuery();
      })

    },
    //审核任务
    auditTask(record, num) {
      record.operationType = num;
      if (record.formId) {
        getFormData({
          id: record.formId
        })
          .then(res => {
            this.currentRecordAuditForm = {
              ...record,
              jsonData: res.content ? JSON.parse(res.content) : {}
            };
            this.auditFormVisible = true;
          })
        return;
      }
      this.currentRecordAuditForm = record;
      this.auditFormVisible = true;
    },

    //提交表单弹出
    auditFormSubmit(data) {
      this.auditFormVisible = false;
      if (data.examineState == 1) {
        let currentData = {
          formData: data,
          formId: this.currentRecordAuditForm.formId,
          taskId: this.currentRecordAuditForm.id
        }

        if (this.currentRecordAuditForm.operationType == 1) {
          crudRequest.completeDelegateTask(currentData)
            .then(res => {
              this.crud.notify('操作成功', 'success');
              this.crud.toQuery();
            })

        } else if (this.currentRecordAuditForm.operationType == 2) {
          crudRequest.resolveDelegateTask(currentData)
            .then(res => {
              this.crud.notify('操作成功', 'success');
              this.crud.toQuery();
            })
        } else {
          this.completeTask(currentData)
        }
      } else {
        this.rollbackTask({
          taskId: this.currentRecordAuditForm.id
        })
      }
    },

    //回退任务
    rollbackTask(row) {
      this.$confirm('你确定驳回该任务吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudRequest.rollbackTask(row)
            .then(res => {
              this.crud.notify('操作成功', 'success');
              this.crud.toQuery();
            })
        })
    },
    // 完成任务
    completeTask(row) {
      crudRequest.completeTask(row).then(res => {
        this.crud.notify('操作成功', 'success');
        this.crud.toQuery();
      })
    },

    checkboxT(row, rowIndex) {
      return row.id !== 1
    }
  }
}
</script>

