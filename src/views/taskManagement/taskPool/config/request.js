import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;

export function getPage(params) {
    return request({
        url: requestUrl,
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: requestUrl,
        method: 'post',
        data
    })
}

export function del(ids) {
    return request({
        url: requestUrl,
        method: 'delete',
        data: ids
    })
}

export function edit(data) {
    return request({
        url: requestUrl,
        method: 'put',
        data
    })
}

export function getFlowImgByProcessInstanceId(data) {
    return request({
        url: `/workflow/workflowTask/getFlowImgByProcessInstanceId/${data.processInstanceId}`,
        method: 'get',
    })
}

// 分配任务
// export function assignTask(taskId, userId) {
//     return request({
//         url: `/workflow/workflowTask/assignTask`,
//         method: 'put',
//         data: {
//             taskId: taskId,
//             userId: userId
//         }
//     })
// }

// 更换执行人
export function assignTask(data) {
    return request({
        url: `/workflow/workflowTask/delegateTask`,
        method: 'put',
        data
    })
}

// 分配任务
export function assignTasks(data) {
    return request({
        url: `/workflow/workflowTask/assignTask`,
        method: 'put',
        data
    })
}


// 完成任务
export function completeTask(data) {
    return request({
        url: `/workflow/workflowTask/completeTask`,
        method: 'put',
        data
    })
}

// 回退任务
export function rollbackTask(data) {
    return request({
        url: `/workflow/workflowTask/rollbackTask`,
        method: 'put',
        data
    })
}


// 完成委托
export function completeDelegateTask(data) {
    return request({
        url: `/workflow/workflowTask/completeDelegateTask`,
        method: 'put',
        data
    })
}
// 解决委托
export function resolveDelegateTask(data) {
    return request({
        url: `/workflow/workflowTask/resolveDelegateTask`,
        method: 'put',
        data
    })
}





export default {completeDelegateTask,resolveDelegateTask ,  add, edit, del, getPage, getFlowImgByProcessInstanceId ,assignTask, completeTask, rollbackTask}
