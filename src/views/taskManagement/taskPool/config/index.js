export default {
    //获取某类型字段列表
    getFields(type){
        return  this.parames.filter(item=>{
            return item.whereShow.some((citem)=>citem == type)
    })
    },
    //加工页面formData
    getDefaultForm(data){
        var json = {};
        data.map(item=>{
            json[ item.fieldName ] = item.defaultVal;
    })
        return json;
    },
    //页面请求地址
    requestUrl: '/workflow/workflowTask/taskPool',

    //页面字段参数，可根据实际需求扩展
    parames: [
        {
            label: '流程名称',
            fieldName: 'processInstanceName',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '任务名称',
            fieldName: 'name',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '状态',
            fieldName: 'statusCd',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '执行人',
            fieldName: 'assigneeName',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '所有者',
            fieldName: 'ownerName',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '创建时间',
            fieldName: 'createdDate',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '是否委托任务',
            fieldName: 'ifDelegateTask',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '委托任务状态',
            fieldName: 'delegateTaskStatusCd',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        }
    ]
}