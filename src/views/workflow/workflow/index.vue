<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.name" clearable size="small" placeholder="输入部门名称搜索" style="width: 200px;"
                  class="filter-item" @keyup.enter.native="crud.toQuery"/>
        <el-date-picker
          @change="crud.toQuery"
          v-model="query.createTime"
          :default-time="['00:00:00','23:59:59']"
          type="daterange"
          range-separator=":"
          size="small"
          class="date-item"
          value-format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
        <rrOperation/>
      </div>
      <crudOperation :permission="permission"/>
    </div>
    <!--表单组件-->
    <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU"
               :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
      <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
        <template v-for="(item, key) in formFields">

          <template v-if="item.type == 'radio'">
            <el-form-item label="状态" :prop="item.fieldName" :key="key">
              <el-radio v-for="item in dict.dept_status" :key="item.id" v-model="form.enabled" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-form-item>
          </template>
          <template v-if="item.type == 'input'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-input v-model="form[item.fieldName]" style="width: 370px;"/>
            </el-form-item>
          </template>

          <template v-if="item.type == 'select' && item.fieldName == 'categoryId'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]" placeholder="请选择" style="width: 370px;">
                <el-option
                  v-for="item in categorys"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </template>

          <template v-if="item.type == 'select' && item.fieldName == 'copyContentId' && crud.status.add == 1">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]" placeholder="请选择" style="width: 370px;">
                <el-option
                  v-for="item in crud.data"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="imgPreview" title="实时流程图" width="80%">
      <DrawingPreview :svgData="currentImgData" :imgPreview="imgPreview"/>
    </el-dialog>

    <!--表格渲染-->
    <el-table ref="table" v-loading="crud.loading" :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              default-expand-all :data="crud.data" row-key="id" @select="crud.selectChange"
              @select-all="crud.selectAllChange" @selection-change="crud.selectionChangeHandler" border>
      <el-table-column :selectable="checkboxT" type="selection" width="55"/>
      <template v-for="(item, key) in tableListFields">
        <el-table-column :label="item.label" :prop="item.fieldName" :key="key">
          <template v-slot="scope">
            <template v-if="item.fieldName == 'statusCd'">
              {{ showCurrentData(dict.workflow_status, scope.row[item.fieldName]) }}
            </template>
            <template v-else-if="item.fieldName == 'categoryId'">
              {{ scope.row['categoryName'] }}
            </template>
            <template v-else>
              {{ scope.row[item.fieldName] }}
            </template>
          </template>
        </el-table-column>
      </template>

      <el-table-column v-permission="['admin',
            'workflow:workflow:edit',
            'workflow:workflow:del',
            'workflow:workflow:definition',
            'workflow:workflow:deploy',
            'workflow:workflow:lookProcess',
            'workflow:workflow:publish',
            'workflow:workflow:revoke'
            ]" label="操作" width="230px" align="center" fixed="right">
        <template v-slot="scope">
          <!-- <udOperation
                  :data="scope.row"
                  :permission="permission"
                  :disabledDle="scope.row.id === 1"
                  msg="确定删除吗,如果存在下级节点则一并删除，此操作不能撤销！"
          /> -->
          <div v-if="scope.row.statusCd == 0">
            <el-button v-permission="['admin','workflow:workflow:edit']" size="mini" style="margin-right: 3px;"
                       type="text" @click="crud.toEdit(scope.row)">编辑
            </el-button>
            <el-button v-permission="['admin','workflow:workflow:definition']" size="mini" style="margin-right: 3px;"
                       type="text">
              <router-link :to="`/tools/bpmn?id=${scope.row.id}`">
                定义流程
              </router-link>
            </el-button>
            <el-popover
              :ref="scope.row.id"
              v-permission="['admin', 'workflow:workflow:del']"
              placement="top"
              width="200"
            >
              <p>确定删除吗？</p>
              <div style="text-align: right; margin: 0">
                <el-button size="mini" type="text" @click="$refs[scope.row.id].doClose()">取消</el-button>
                <el-button :loading="delLoading" type="primary" size="mini" @click="crud.doDelete(scope.row)">确定
                </el-button>
              </div>
              <el-button slot="reference" type="text" size="mini">删除</el-button>

            </el-popover>
          </div>
          <div v-if="scope.row.statusCd == 1">
            <el-button v-permission="['admin','workflow:workflow:edit']" size="mini" style="margin-right: 3px;"
                       type="text" @click="crud.toEdit(scope.row)">编辑
            </el-button>
            <el-button v-permission="['admin','workflow:workflow:definition']" size="mini" style="margin-right: 3px;"
                       type="text">
              <router-link :to="`/tools/bpmn?id=${scope.row.id}`">
                定义流程
              </router-link>
            </el-button>
            <el-button v-permission="['admin','workflow:workflow:deploy']" size="mini" style="margin-right: 3px;"
                       type="text" @click="deploy(scope.row)">部署
            </el-button>

            <el-popover
              :ref="scope.row.id"
              v-permission="['admin', 'workflow:workflow:del']"
              placement="top"
              width="200"
            >
              <p>确定删除吗？</p>
              <div style="text-align: right; margin: 0">
                <el-button size="mini" type="text" @click="$refs[scope.row.id].doClose()">取消</el-button>
                <el-button :loading="delLoading" type="primary" size="mini" @click="crud.doDelete(scope.row)">确定
                </el-button>
              </div>
              <el-button slot="reference" type="text" size="mini">删除</el-button>

            </el-popover>

          </div>
          <div v-if="scope.row.statusCd == 2">
            <el-button v-permission="['admin','workflow:workflow:lookProcess']" size="mini" style="margin-right: 3px;"
                       type="text" @click="seeImg(scope.row)">查看流程图
            </el-button>
            <el-button v-permission="['admin','workflow:workflow:publish']" size="mini" style="margin-right: 3px;"
                       type="text" @click="publish(scope.row)">发布
            </el-button>
          </div>
          <div v-if="scope.row.statusCd == 3">
            <el-button v-permission="['admin','workflow:workflow:lookProcess']" size="mini" style="margin-right: 3px;"
                       type="text" @click="seeImg(scope.row)">查看流程图
            </el-button>
            <el-button v-permission="['admin','workflow:workflow:revoke']" size="mini" style="margin-right: 3px;"
                       type="text" @click="unPublish(scope.row)">撤销
            </el-button>
          </div>


        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination/>
  </div>
</template>

<script>
import crudDept from './config/request'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import DrawingPreview from '@/components/DrawingPreview/index'

//注入配置文件
import config from './config/index'

const formFields = config.getFields('form');
const tableListFields = config.getFields('tableList');
const defaultForm = config.getDefaultForm(formFields);

export default {
  name: 'simpleList',
  components: {Treeselect, crudOperation, rrOperation, udOperation, pagination, DrawingPreview},
  cruds() {
    return CRUD({title: '流程', url: config.requestUrl, crudMethod: {...crudDept}})
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 设置数据字典
  dicts: ['workflow_status'],
  data() {
    return {
      scale: 1,
      currentImgData: '',
      imgPreview: false,
      delLoading: false,
      categorys: [],
      formFields: formFields,
      tableListFields: tableListFields,
      rules: {
        name: [
          {required: true, message: '请输入名称', trigger: 'blur'}
        ]
      },
      permission: {
        add: ['admin', 'workflow:workflow:add'],
        edit: ['admin', 'workflow:workflow:edit'],
        del: ['admin', 'workflow:workflow:del']
      },
      enabledTypeOptions: [
        {key: 'true', display_name: '正常'},
        {key: 'false', display_name: '禁用'}
      ]
    }
  },
  methods: {
    //查看图片
    seeImg(row) {
      crudDept.getFlowImgByDefinitionId({
        id: row.processDefinitionId
      })
        .then(res => {
          this.currentImgData = res;
          this.imgPreview = true;
        })
      // window.open(row.picUri, '_blank');
    },
    //删除
    delMethod(row) {
      this.crud.doDelete(row)
    },
    deploy(data) {
      crudDept.deployment({
        id: data.id
      }).then(res => {
        this.crud.notify('部署成功', CRUD.NOTIFICATION_TYPE.SUCCESS);
        this.update();
      }).catch(err => {
      })

    },
    unPublish(data) {
      crudDept.unPublish({
        id: data.id
      }).then(res => {
        this.crud.notify('撤销成功', CRUD.NOTIFICATION_TYPE.SUCCESS);
        this.update();
      }).catch(err => {
      })
    },
    publish(data) {
      crudDept.publish({
        id: data.id
      }).then(res => {
        this.crud.notify('发布成功', CRUD.NOTIFICATION_TYPE.SUCCESS);
        this.update();
      }).catch(err => {
      })
    },
    update() {
      this.crud.toQuery();
    },
    // 新增与编辑前做的操作
    [CRUD.HOOK.afterToCU](crud, form) {
      crudDept.getWorkflowCategory()
        .then(res => {
          this.categorys = res;
        })
    },
    // 提交前的验证
    [CRUD.HOOK.afterValidateCU]() {
      return true
    },
    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('此操作将 "' + this.dict.label.dept_status[val] + '" ' + data.name + '部门, 是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        crudDept.edit(data).then(res => {
          this.crud.notify(this.dict.label.dept_status[val] + '成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
        }).catch(err => {
          data.enabled = !data.enabled
          console.log(err.response.data.message)
        })
      }).catch(() => {
        data.enabled = !data.enabled
      })
    },
    //定义流程
    openDefineProcess() {
      this.router.push('/tools/bpmn');
    },
    checkboxT(row, rowIndex) {
      return row.id !== 1
    },


  }
}
</script>

