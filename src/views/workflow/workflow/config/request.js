import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;

export function getPage(params) {
    return request({
        url: requestUrl,
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: requestUrl,
        method: 'post',
        data
    })
}

export function del(ids) {
    return request({
        url: requestUrl,
        method: 'delete',
        data: ids
    })
}

export function edit(data) {
    return request({
        url: requestUrl,
        method: 'put',
        data
    })
}

export function getWorkflowCategory(data){
    return request({
        url: '/workflow/workflowCategory/list',
        method: 'get',
        data
    })
}

export function deployment(data){
    let id = data.id;
    delete data.id;
    return request({
        url: '/workflow/workflow/deployment/' + id,
        method: 'put',

    })
}

export function publish(data){
    let id = data.id;
    delete data.id;
    return request({
        url: '/workflow/workflow/publish/' + id,
        method: 'put',
        data
    })
}

export function unPublish(data){
    let id = data.id;
    delete data.id;
    return request({
        url: '/workflow/workflow/unPublish/' + id,
        method: 'put',
        data
    })
}

export function getHistoryTaskPage(data) {
    return request({
        url: `/workflow/workflowTask/historyTask/historyTaskPage`,
        method: 'get',
        params: data
    })
}

export function getFlowImgByDefinitionId(data) {
    return request({
        url: `/workflow/workflow/getFlowImgByDefinitionId/${data.id}`,
        method: 'get',
        data
    })
}


export function getUserList(taskId) {
  return request({
    url: `/workflow/workflowTask/nextUserList/${taskId}`,
    method: 'get',
  })
}




export default {getFlowImgByDefinitionId, getHistoryTaskPage, add, edit, del, getPage, getWorkflowCategory, deployment, publish, unPublish }
