<template>
    <div>
       <k-form-build
            :value="jsonData"
            ref="KFB"
            @submit="handleSubmit"
        />
        <el-button @click="getData" type="success">提交</el-button>
    </div>
  
  
</template>
<script>
import KFormDesign from '../../../components/k-form-design/packages'
import '../../../components/k-form-design/styles/form-design.less'
import Vue from 'vue'
Vue.use( KFormDesign )
export default {
  name: 'Demo',
  props: {
      jsonData: {
        type: Object,
        default:  {"list":[{"type":"textarea","label":"文本框","options":{"width":"100%","minRows":4,"maxRows":6,"maxLength":null,"defaultValue":"","clearable":false,"hidden":false,"disabled":false,"placeholder":"请输入"},"model":"textarea_1594361276242","key":"textarea_1594361276242","rules":[{"required":false,"message":"必填项"}]},{"type":"radio","label":"单选框","options":{"disabled":false,"hidden":false,"defaultValue":"","dynamicKey":"","dynamic":false,"options":[{"value":"1","label":"选项1"},{"value":"2","label":"选项2"},{"value":"3","label":"选项3"}]},"model":"radio_1594361277189","key":"radio_1594361277189","rules":[{"required":false,"message":"必填项"}]},{"type":"textarea","label":"文本框","options":{"width":"100%","minRows":4,"maxRows":6,"maxLength":null,"defaultValue":"","clearable":false,"hidden":false,"disabled":false,"placeholder":"请输入"},"model":"textarea_1594361300378","key":"textarea_1594361300378","rules":[{"required":false,"message":"必填项"}]},{"type":"rate","label":"评分","options":{"defaultValue":0,"max":5,"disabled":false,"hidden":false,"allowHalf":false},"model":"rate_1594361303202","key":"rate_1594361303202","rules":[{"required":false,"message":"必填项"}]}],"config":{"layout":"horizontal","labelCol":{"span":4},"wrapperCol":{"span":18},"hideRequiredMark":false,"customStyle":""}}
      }
  },
  data () {
    return {
        defaultValue:  {}
    }
  },
  methods: {
    handleSubmit(p) {
       // 通过表单提交按钮触发，获取promise对象
       p().then(res => {
         // 获取数据成功
         alert(JSON.stringify(res))
       })
         .catch(err => {
           console.log(err, '校验失败')
         })
     },
     getData() {
       // 通过函数获取数据
       this.$refs.KFB.getData().then(res => {
         // 获取数据成功
         alert(JSON.stringify(res))
       })
         .catch(err => {
           console.log(err, '校验失败')
         })
     }
  },
  mounted(){
    
  }
}
</script>