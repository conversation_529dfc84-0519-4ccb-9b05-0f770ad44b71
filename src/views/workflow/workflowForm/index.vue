<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.name" clearable size="small" placeholder="输入部门名称搜索" style="width: 200px;"
                  class="filter-item" @keyup.enter.native="crud.toQuery"/>
        <el-date-picker
          @change="crud.toQuery"
          v-model="query.createTime"
          :default-time="['00:00:00','23:59:59']"
          type="daterange"
          range-separator=":"
          size="small"
          class="date-item"
          value-format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
        <rrOperation/>
      </div>
      <crudOperation :permission="permission"/>
    </div>
    <!--表单组件-->
    <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU"
               :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
      <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
        <template v-for="(item, key) in formFields">
          <template v-if="item.type == 'radio'">
            <el-form-item label="状态" :prop="item.fieldName" :key="key">
              <el-radio v-for="item in dict.dept_status" :key="item.id" v-model="form.enabled" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-form-item>
          </template>
          <template v-if="item.type == 'input'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-input v-model="form[item.fieldName]" style="width: 370px;"/>
            </el-form-item>
          </template>
          <template v-if="item.type == 'select' && item.fieldName == 'categoryId'">
            <el-form-item label="流程类目" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]" placeholder="请选择" style="width: 370px;">
                <el-option
                  v-for="item in categorys"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </template>
          <template v-if="item.type == 'select' && item.fieldName == 'copyContentId' && crud.status.add == 1">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]" placeholder="请选择" style="width: 370px;">
                <el-option
                  v-for="item in crud.data"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>
    <!--表格渲染-->
    <el-table ref="table" v-loading="crud.loading" :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              default-expand-all :data="crud.data" row-key="id" @select="crud.selectChange"
              @select-all="crud.selectAllChange" @selection-change="crud.selectionChangeHandler" border>
      <el-table-column :selectable="checkboxT" type="selection" width="55"/>
      <template v-for="(item, key) in tableListFields">
        <el-table-column :label="item.label" :prop="item.fieldName" :key="key">
          <template v-slot="scope">
            {{ item.fieldName == 'categoryId' ? scope.row['categoryName'] : scope.row[item.fieldName] }}
          </template>
        </el-table-column>
      </template>

      <el-table-column v-permission="[
                'admin',
                'workflow:workflowForm:edit',
                'workflow:workflowForm:del',
                'workflow:workflowForm:defineForm',
                'workflow:workflowForm:previewForm',
                'workflow:workflowForm:publish'

                ]" label="操作" width="280px" align="center" fixed="right">
        <template v-slot="scope">
          <div v-if="scope.row.statusCd == 0">
            <el-button v-permission="['admin','workflow:workflowForm:edit']" size="mini" style="margin-right: 3px;"
                       type="text" @click="crud.toEdit(scope.row)">修改
            </el-button>
            <el-button v-permission="['admin','workflow:workflowForm:defineForm']" size="mini"
                       style="margin-right: 3px;" type="text">
              <router-link :to="`/tools/kFormDesign?id=${scope.row.id}`">
                定义表单
              </router-link>
            </el-button>

            <el-popover
              :ref="scope.row.id"
              v-permission="['admin','workflow:workflowForm:del']"
              placement="top"
              width="200"
            >
              <p>确定删除该表单吗？</p>
              <div style="text-align: right; margin: 0">
                <el-button size="mini" type="text" @click="$refs[scope.row.id].doClose()">取消</el-button>
                <el-button :loading="delLoading" type="primary" size="mini" @click="delMethod(scope.row)">确定
                </el-button>
              </div>
              <el-button slot="reference" type="text" size="mini">删除</el-button>
            </el-popover>
          </div>
          <div v-if="scope.row.statusCd == 1">

            <el-button v-permission="['admin','workflow:workflowForm:edit']" size="mini" style="margin-right: 3px;"
                       type="text" @click="crud.toEdit(scope.row)">修改
            </el-button>
            <el-button v-permission="['admin','workflow:workflowForm:defineForm']" size="mini"
                       style="margin-right: 3px;" type="text">
              <router-link :to="`/tools/kFormDesign?id=${scope.row.id}`">
                定义表单
              </router-link>
            </el-button>
            <el-button v-permission="['admin','workflow:workflowForm:previewForm']" size="mini"
                       style="margin-right: 3px;" type="text">

              <router-link :to="`/tools/kFormDesign/preview?id=${scope.row.id}`">
                预览表单
              </router-link>
            </el-button>
            <el-button v-permission="['admin','workflow:workflowForm:publish']" size="mini" style="margin-right: 3px;"
                       type="text" @click="publish(scope.row)">发布
            </el-button>
            <el-popover
              :ref="scope.row.id"
              v-permission="['admin','workflow:workflowForm:del']"
              placement="top"
              width="200"
            >
              <p>确定删除该表单吗？</p>
              <div style="text-align: right; margin: 0">
                <el-button size="mini" type="text" @click="$refs[scope.row.id].doClose()">取消</el-button>
                <el-button :loading="delLoading" type="primary" size="mini" @click="delMethod(scope.row)">确定
                </el-button>
              </div>
              <el-button slot="reference" type="text" size="mini">删除</el-button>
            </el-popover>

          </div>
          <div v-if="scope.row.statusCd == 2">
            <el-button v-permission="['admin','workflow:workflowForm:previewForm']" size="mini"
                       style="margin-right: 3px;" type="text">
              <router-link :to="`/tools/kFormDesign/preview?id=${scope.row.id}`">
                预览表单
              </router-link>
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination/>

  </div>
</template>

<script>
import crudDept from './config/request'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

import preview from './preview'

//注入配置文件
import config from './config/index'

const formFields = config.getFields('form');
const tableListFields = config.getFields('tableList');
const defaultForm = config.getDefaultForm(formFields);
export default {
  name: 'simpleList',
  components: {Treeselect, crudOperation, rrOperation, udOperation, pagination, preview},
  cruds() {
    return CRUD({title: '表单', url: config.requestUrl, crudMethod: {...crudDept}})
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 设置数据字典
  dicts: ['dept_status'],
  data() {
    return {
      //弹窗jsondata
      jsonData: {},
      delLoading: false,
      categorys: [],
      formFields: formFields,
      tableListFields: tableListFields,
      rules: {
        name: [
          {required: true, message: '请输入名称', trigger: 'blur'}
        ]
      },
      permission: {
        add: ['admin', 'workflow:workflowForm:add'],
        edit: ['admin', 'workflow:workflowForm:edit'],
        del: ['admin', 'workflow:workflowForm:del']
      },
      enabledTypeOptions: [
        {key: 'true', display_name: '正常'},
        {key: 'false', display_name: '禁用'}
      ]
    }
  },
  methods: {
    //预览表单
    previewForm(data) {
      // this.jsonData = JSON.parse( data.content );
      // this.dialogVisible = true;
    },
    //删除
    delMethod(row) {
      this.crud.doDelete(row);
    },
    //发布
    publish(data) {
      crudDept.publish({
        id: data.id
      }).then(res => {
        this.crud.notify('发布成功', CRUD.NOTIFICATION_TYPE.SUCCESS);
        this.update();
      }).catch(err => {
      })
    },
    //更新分页
    update() {
      this.crud.toQuery();
    },
    // 新增与编辑前做的操作
    [CRUD.HOOK.afterToCU](crud, form) {
      crudDept.getWorkflowCategory()
        .then(res => {
          this.categorys = res;
        })
    },
    // 提交前的验证
    [CRUD.HOOK.afterValidateCU]() {
      return true
    },
    // 改变状态
    changeEnabled(data, val) {
    },
    //表格复选框状态
    checkboxT(row, rowIndex) {
      return row.id !== 1
    }
  }
}
</script>

<style scoped>
</style>
