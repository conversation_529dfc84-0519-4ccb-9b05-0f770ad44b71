export default {
    //获取某类型字段列表
    getFields(type){
        return  this.parames.filter(item=>{
            return item.whereShow.some((citem)=>citem == type)
    })
    },
    //加工页面formData
    getDefaultForm(data){
        var json = {};
        data.map(item=>{
            json[ item.fieldName ] = item.defaultVal;
    })
        return json;
    },
    //页面请求地址
    requestUrl: '/workflow/workflowForm',

    //页面字段参数，可根据实际需求扩展
    parames: [
       
        {
            label: '流程类目',
            fieldName: 'categoryId',
            defaultVal: '',
            type: 'select',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '复制表单',
            fieldName: 'copyContentId',
            defaultVal: '',
            type: 'select',
            whereShow: ['form']
        },
        {
            label: '名称',
            fieldName: 'name',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '编码',
            fieldName: 'code',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        
        {
            label: '备注',
            fieldName: 'remark',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit']
        },
        {
            label: '数据版本',
            fieldName: 'dataVersion',
            defaultVal: '',
            type: 'input',
            whereShow: []
        },
    ]
}