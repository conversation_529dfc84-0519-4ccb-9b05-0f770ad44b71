import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;

export function getPage(params) {
    return request({
        url: requestUrl + '/page',
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: requestUrl,
        method: 'post',
        data
    })
}

export function del(ids) {
    return request({
        url: requestUrl,
        method: 'delete',
        data: ids
    })
}

export function edit(data) {
    return request({
        url: requestUrl,
        method: 'put',
        data
    })
}
export function getWorkflowCategory(data){
    return request({
        url: '/workflow/workflowCategory/list',
        method: 'get',
        data
    })
}


export function publish(data){
    let id = data.id;
    delete data.id;
    return request({
        url: '/workflow/workflowForm/publish/' + id,
        method: 'put',
        data
    })
}

export function getFormData(data){
    let id = data.id;
    delete data.id;
    return request({
        url: '/workflow/workflowForm/' + id,
        method: 'get',
        
    })
}


export default { add, edit, del, getPage, getWorkflowCategory, publish, getFormData }
