<template>
    <div class="app-container">
        <!--工具栏-->
        <div class="head-container">
            <div v-if="crud.props.searchToggle">
                <!-- 搜索 -->
                <el-input v-model="query.name" clearable size="small" placeholder="输入名称搜索" style="width: 200px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
                <el-date-picker
                        v-model="query.createTime"
                        :default-time="['00:00:00','23:59:59']"
                        type="daterange"
                        range-separator=":"
                        size="small"
                        class="date-item"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        @change="crud.toQuery"
                />
                <rrOperation />
            </div>
            <crudOperation :permission="permission" />
        </div>

        <el-dialog :visible.sync="imgPreview" title="实时流程图" width="80%">
            <DrawingPreview :svgData="currentImgData" :imgPreview="imgPreview"/>
        </el-dialog>
        <!--表单组件-->
        <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
            <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
                <template v-for="(item, key) in formFields" >
                    <template v-if="item.type == 'radio'">
                        <el-form-item  label="状态" :prop="item.fieldName" :key="key">
                            <el-radio v-for="item in dict.dept_status" :key="item.id" v-model="form.enabled" :label="item.value">{{ item.label }}</el-radio>
                        </el-form-item>
                    </template>
                    <template v-if="item.type == 'input'">
                        <el-form-item :label="item.label" :prop="item.fieldName"  :key="key">
                            <el-input v-model="form[item.fieldName]" :style="formInputStyle"/>
                        </el-form-item>
                    </template>
                    <template v-if="item.type == 'date'">
                        <el-form-item :label="item.label" :prop="item.fieldName"  :key="key" >
                            <el-date-picker
                                    v-model="form[item.fieldName]"
                                    value-format="yyyy年MM月dd日 HH:mm:ss"
                                    type="datetime"
                                    :style="formInputStyle"
                                    placeholder="选择日期时间">
                            </el-date-picker>
                        </el-form-item>
                    </template>
                    <template v-if="item.type == 'select'">
                        <el-form-item :label="item.label" :prop="item.fieldName"  :key="key" >
                            <el-select v-model="form[item.fieldName]" :style="formInputStyle" placeholder="请选择">
                                <el-option
                                        v-for="(item) in dict.project_types"
                                        :key="item.code"
                                        :label="item.name"
                                        :value="item.code"
                                />
                            </el-select>
                        </el-form-item>
                    </template>
                    <template v-if="item.type == 'textarea'">
                        <el-form-item :label="item.label" :prop="item.fieldName"  :key="key">
                            <el-input :type="item.type" v-model="form[item.fieldName]" style="width: 370px;" />
                        </el-form-item>
                    </template>
                </template>

            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="text" @click="crud.cancelCU">取消</el-button>
                <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
            </div>
        </el-dialog>
        <!--表格渲染-->
        <el-table ref="table" v-loading="crud.loading" :tree-props="{children: 'children', hasChildren: 'hasChildren'}" default-expand-all :data="crud.data" row-key="id" @select="crud.selectChange" @select-all="crud.selectAllChange" @selection-change="crud.selectionChangeHandler" border>
            <template v-for="(item, key) in tableListFields">
              <template v-if="item.fieldName === 'processInstanceName'">
                <el-table-column :label="item.label" :prop="item.fieldName" :key="key">
                  <template v-slot="scope">
                    <el-link :underline="false" @click="projectClick(scope.row)">{{scope.row[ item.fieldName ]}}</el-link>
                  </template>
                </el-table-column>
              </template>
              <template v-else>
                <el-table-column :label="item.label" :prop="item.fieldName" :key="key">
                  <template v-slot="scope">
                    {{ item.fieldName == 'duration' ? timeConsuming( scope.row[ item.fieldName ] ) : scope.row[ item.fieldName ] }}
                  </template>
                </el-table-column>
              </template>

            </template>

            <el-table-column v-permission="['admin','myTask:myFinishTask:lookProcess', 'myTask:myFinishTask:details']" label="操作" width="130px" align="center" fixed="right">
                <template v-slot="scope">
                    <el-button v-permission="['admin','myTask:myFinishTask:lookProcess']" size="mini" style="margin-right: 3px;" type="text" @click="getAuditPage(scope.row.processInstanceId)">查看审核记录</el-button>
                    <el-button v-permission="['admin','myTask:myFinishTask:lookProcess']" size="mini" style="margin-right: 3px;" type="text" @click="showFlowImg(scope.row)">查看实时流程图</el-button>

                    <el-button v-permission="['admin','myTask:myFinishTask:details']" size="mini" style="margin-right: 3px;" type="text" @click="seeDetails(scope.row)">详情</el-button>
                    <!-- <el-button size="mini" style="margin-right: 3px;" type="text" @click="seeDetails(scope.row)">详情</el-button> -->
                </template>
            </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination />

        <el-dialog :visible.sync="detailsFormVisible" :close-on-click-modal='false' title="详情" width="80%">
            <detailsForm
                :submit="detailFormSubmit"
                :visible="detailsFormVisible"
                :record="currentRecordDetailsForm"
                :addResultAuditFile="false"
                :disabled="true"
            />
        </el-dialog>

        <el-dialog :visible.sync="auditRecordsVisible" title="审核记录" width="80%">
        <el-table
          :data="auditRecords.records"
          style="width: 100%">

          <el-table-column type="expand">
            <template v-slot="props">
              <p v-if="!props.row.formContent">暂无数据</p>
              <PreviewForm
                v-if="props.row.formContent"
                :jsonData="props.row.formContent"
                :defaultValue="props.row.formData"
                :record="props.row"
                :isHideBtn="true"
                :disabled="true"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="任务名称"
            prop="name">
          </el-table-column>
          <el-table-column
            label="执行人"
            prop="assigneeName">
          </el-table-column>
          <el-table-column
            label="创建时间"
            prop="createdDate">
          </el-table-column>
          <el-table-column
            label="完成时间"
            prop="completedDate">
          </el-table-column>
          <el-table-column
            label="持续时间"
          >
            <template v-slot="scope">
              {{ scope.row.duration | timeConsuming }}
            </template>
          </el-table-column>
        </el-table>
      </el-dialog>

      <!--项目表单组件-->
      <el-dialog :close-on-click-modal="false"
                 :visible.sync="projectInfoSync" :title="'项目详情'" width="1200px">
        <el-form ref="form" :disabled="true" :model="projectFormData" :inline="true"
                 size="small"
                 label-width="170px">
          <template v-for="(item, key) in proFormFirstFields">
            <template v-if="item.type == 'radio'">
              <el-form-item label="状态" :prop="item.fieldName" :key="key" :style="formInputStyle">
                <el-radio v-for="item in dict.dept_status" :key="item.id" v-model="projectFormData.enabled" :label="item.value">
                  {{
                    item.label }}
                </el-radio>
              </el-form-item>
            </template>
            <template v-if="item.type == 'column'">
              <el-form-item :prop="item.fieldName" :key="key" :style="item.style">
                <el-alert
                    style="width: 100%;"
                    :title="item.label"
                    type="info"
                    :closable="false">
                </el-alert>
              </el-form-item>
            </template>
            <template v-if="item.fieldName == 'projectPlannedInvestments'">
              <div v-for="(citem, index) in projectFormData[item.fieldName]" :key="index+100"
                   style="background: rgba(229,234,234,0.2);">
                <template>
                  <el-form-item label="投资计划下达年份" :prop=" 'projectPlannedInvestments.' + index + '.planApprovalDate'"
                                :rules="rules.planApprovalDate" :key="index+3" style="padding-top: 18px">
                    <el-date-picker
                        style="width: 171px"
                        v-model="citem['planApprovalDate']"
                        value-format="yyyy"
                        type="year"
                        placeholder="选择年份">
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item label="计划投资编号" :prop=" 'projectPlannedInvestments.' + index + '.fundPlanCode'"
                                :rules="rules.fundPlanCode" :key="index+1" style="padding-top: 18px">
                    <el-input v-model="citem['fundPlanCode']" style="width:171px"/>
                  </el-form-item>
                  <el-form-item label="计划投资额(万元)"
                                :prop=" 'projectPlannedInvestments.' + index + '.planApprovalAmount'"
                                :rules="rules.planApprovalAmount" :key="index+2" style="padding-top: 18px">
                    <el-input  v-model="citem['planApprovalAmount']"/>
                  </el-form-item>

                </template>
              </div>
            </template>
            <template v-if="item.type == 'input' && !item.other">
              <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                <el-input v-model="projectFormData[item.fieldName]" :disabled="item.disabled" :style="formInputStyle"/>
              </el-form-item>
            </template>

            <template v-if="item.type === 'input-number'">
              <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                <el-input  v-model="projectFormData[item.fieldName]" :style="formInputStyle" />

              </el-form-item>
            </template>

            <template v-if="item.type == 'textarea'">
              <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                <el-input type="textarea" v-model="projectFormData[item.fieldName]" :style="item.inputStyle"/>
              </el-form-item>
            </template>

            <template v-if="item.type == 'date'">
              <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                <el-date-picker
                    v-model="projectFormData[item.fieldName]"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    :style="formInputStyle"
                    placeholder="选择日期时间">
                </el-date-picker>
              </el-form-item>
            </template>

            <template v-if="item.type == 'select' && item.fieldName == 'projectCategory1Cd'">
              <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                <el-select v-model="projectFormData[item.fieldName]" placeholder="请选择" :style="item.inputStyle">
                  <el-option
                      v-for="(item) in dict.project_category_one"
                      :key="Number(item.code)"
                      :label="item.name"
                      :value="Number(item.code)"
                  />
                </el-select>
              </el-form-item>
            </template>

            <template v-if="item.type == 'select' && item.fieldName == 'projectCategory2Cd'">
              <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                <el-select v-model="projectFormData[item.fieldName]" placeholder="请选择" :style="item.inputStyle">
                  <el-option
                      v-for="(item) in dict.project_types"
                      :key="Number(item.code)"
                      :label="item.name"
                      :value="Number(item.code)"
                  />
                </el-select>
              </el-form-item>
            </template>

            <template v-if="item.type == 'select' && item.fieldName == 'buildThreeOrgName'">
              <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                <el-select
                    v-model="projectFormData[item.fieldName]"
                    :style="item.inputStyle"
                    placeholder="请选择">
                  <el-option
                      v-for="(item) in buildThreeLevelOrg"
                      :key="item.code"
                      :label="item.name"
                      :value="item"
                  />
                </el-select>

              </el-form-item>
            </template>

            <template v-if="item.type == 'select' && item.fieldName == 'progressCd'">
              <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                <el-select v-model="projectFormData[item.fieldName]" :style="formInputStyle" placeholder="请选择">
                  <el-option
                      v-for="(item) in dict.project_schedule"
                      :key="Number(item.code)"
                      :label="item.name"
                      :value="Number(item.code)"
                  />
                </el-select>
              </el-form-item>
            </template>
            <template v-if="item.type == 'select' && item.fieldName == 'projectTypeCd'">
              <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                <el-select v-model="projectFormData[item.fieldName]" :style="formInputStyle" placeholder="请选择">
                  <el-option
                      v-for="(item) in dict.project_type_new"
                      :key="Number(item.code)"
                      :label="item.name"
                      :value="Number(item.code)"
                  />
                </el-select>
              </el-form-item>
            </template>

            <template v-if="item.fieldName == 'contractor'">
              <div style="margin-bottom: 15px">
                <div v-for="(citem, index) in projectFormData[item.fieldName]" :key="index+100"
                     style="background: rgba(229,234,234,0.2)">
                  <template>
                    <el-form-item  label="设计单位" :prop=" 'contractor.' + index + '.designOrg'"
                                   :rules="rules.designOrg" :key="index+1" style="padding-top: 12px">
                      <el-input v-model="citem['designOrg']" style="width: 171px"/>
                    </el-form-item>
                    <el-form-item  label="监理单位" :prop=" 'contractor.' + index + '.supervisorOrg'"
                                   :rules="rules.supervisorOrg" :key="index+2" style="padding-top: 12px">
                      <el-input v-model="citem['supervisorOrg']" style="width: 171px"/>
                    </el-form-item>

                    <el-form-item  label="施工单位" :prop=" 'contractor.' + index + '.constructionOrg'"
                                   :rules="rules.constructionOrg" :key="index+3" style="padding-top: 12px">
                      <el-input v-model="citem['constructionOrg']" style="width: 171px"/>
                    </el-form-item>

                  </template>
                </div>
              </div>
            </template>

          </template>
        </el-form>
      </el-dialog>
    </div>
</template>

<script>
    import crudRequest from './config/request'
    import '@riophae/vue-treeselect/dist/vue-treeselect.css'
    import CRUD, { presenter, header, form, crud } from '@crud/crud'
    import rrOperation from '@crud/RR.operation'
    import crudOperation from '@crud/CRUD.operation'
    import udOperation from '@crud/UD.operation'
    import pagination from '@crud/Pagination'
    import { timeConsuming } from '@/utils/datetime'
    import DrawingPreview from '@/components/DrawingPreview/index'
    import { getFormData } from '@/views/workflow/workflowForm/config/request'
    import { getPageTask  } from '@/views/taskManagement/historyTaskPool/config/request'
    import {getDetails,getBuildThreeOrg} from '@/views/projectManagement/projectInfo/config/request'

    import PreviewForm from '@/components/PreviewForm'

    //注入配置文件
    import config from './config/index'
    import detailsForm from '@/components/auditForm'
    import proConfig from '@/views/projectManagement/projectInfo/config/index'
    const formFields = config.getFields('form');
    const tableListFields = config.getFields('tableList');
    const  defaultForm = config.getDefaultForm( formFields );
    const proFormFirstFields = proConfig.getFields('form');

    export default {
        name: 'simpleList',
        components: {  crudOperation, rrOperation, udOperation, pagination, DrawingPreview, detailsForm ,PreviewForm},
        cruds() {
            return CRUD({ title: '模板', url: config.requestUrl, crudMethod: { ...crudRequest }, sort:[], optShow: {reset: true }})
        },
        mixins: [presenter(), header(), form(defaultForm), crud()],
        // 设置数据字典
        dicts: ['project_category_one','project_types', 'project_type_new', 'project_schedule',],
        data() {
            return {
              buildThreeLevelOrg:[],
              projectInfoSync:false,
              projectFormData:{},
              auditRecords:{},
                auditRecordsVisible:false,
                currentRecordDetailsForm: {},
                detailsFormVisible: false,
                imgPreview: false,
                currentImgData: '',
                formFields: formFields,
                tableListFields: tableListFields,
                proFormFirstFields:proFormFirstFields,
              rules: {
                    name: [
                        { required: true, message: '请输入名称', trigger: 'blur' }
                    ]
                },
                permission: {},
            }
        },
      mounted() {
          this.getDictAll();
      },
      methods: {
          getDictAll(){
            getBuildThreeOrg({}).then(res => {
                  this.buildThreeLevelOrg = res;
                })

          },
          projectClick(row){
            getDetails({id:row.projectId}).then(res=>{
              this.projectFormData = res;
              this.projectInfoSync = true;
            })
          },
          getAuditPage(processInstanceId) {
            this.auditRecordsVisible = true;
            getPageTask({
              current: this.auditPage,
              size: this.auditPageSize,
              processInstanceId: processInstanceId
            })
              .then(res => {
                res.records.map(item => {
                  item.formContent = item.formContent && JSON.parse(item.formContent);
                  item.formData = item.formData && JSON.parse(item.formData);
                })
                this.auditRecords = res;
              })
              .catch(err => {
                console.log(err);
                this.auditRecordsVisible = false;
              })
          },
            detailFormSubmit(){},
            seeDetails(record, num){
                record.operationType = num;
                if( record.formId ){
                    getFormData({
                        id: record.formId
                    })
                    .then(res=>{
                        console.log( record )
                        this.currentRecordDetailsForm = {
                            ...record,
                            jsonData: res.content ? JSON.parse(res.content) : {}
                        };
                        this.detailsFormVisible = true;
                    })

                }else {
                  this.currentRecordDetailsForm = record;
                  this.detailsFormVisible = true;
                }
            },
            //耗时转换
            timeConsuming(time){
                return timeConsuming( time );
            },
            // 查看实时流程图
            showFlowImg(row) {
                crudRequest.getFlowImgByProcessInstanceId(row)
                .then(res=>{
                    this.currentImgData = res;
                    this.imgPreview = true;
                })
            },
            checkboxT(row, rowIndex) {
                return row.id !== 1
            }
        }
    }
</script>

