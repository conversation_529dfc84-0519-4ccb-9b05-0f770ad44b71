import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;

export function getPage(params) {
    return request({
        url: requestUrl,
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: requestUrl,
        method: 'post',
        data
    })
}

export function del(ids) {
    return request({
        url: requestUrl,
        method: 'delete',
        data: ids
    })
}

export function edit(data) {
    return request({
        url: requestUrl,
        method: 'put',
        data
    })
}

export function getFlowImgByProcessInstanceId(data) {
    return request({
        url: `/workflow/workflowTask/getFlowImgByProcessInstanceId/${data.processInstanceId}`,
        method: 'get',
    })
}

// 分配任务
export function assignTask(taskId, userId) {
    return request({
        url: `/workflow/workflowTask/assignTask`,
        method: 'put',
        data: {
            taskId: taskId,
            userId: userId
        }
    })
}

// 完成任务
export function completeTask(taskId) {
    return request({
        url: `/workflow/workflowTask/completeTask`,
        method: 'put',
        data: {
            taskId: taskId
        }
    })
}





export default { add, edit, del, getPage, getFlowImgByProcessInstanceId ,assignTask, completeTask}
