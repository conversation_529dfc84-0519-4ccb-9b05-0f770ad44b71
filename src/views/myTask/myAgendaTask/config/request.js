import request from '@/utils/request'
import config from './index'
import qs from "qs";
const requestUrl = config.requestUrl;

//代办和签收
export function getPage(params) {
    return request({
        url: requestUrl + '/page'+ '?' + qs.stringify(params, { indices: false }),
        method: 'get'
    })
}

export function add(data) {
    return request({
        url: requestUrl,
        method: 'post',
        data
    })
}

export function del(ids) {
    return request({
        url: requestUrl,
        method: 'delete',
        data: ids
    })
}

export function edit(data) {
    return request({
        url: requestUrl,
        method: 'put',
        data
    })
}

export function getFlowImgByProcessInstanceId(data) {
    return request({
        url: `/workflow/workflowTask/getFlowImgByProcessInstanceId/${data.processInstanceId}`,
        method: 'get',
    })
}

// 回退任务
export function rollbackTask(data) {
    return request({
        url: `/workflow/workflowTask/rollbackTask`,
        method: 'put',
        data
    })
}

// 分配任务
export function assignTask(taskId, userId) {
    return request({
        url: `/workflow/workflowTask/assignTask`,
        method: 'put',
        data: {
            taskId: taskId,
            userId: userId
        }
    })
}

// 完成任务
export function completeTask(data) {
    return request({
        url: `/workflow/workflowTask/completeTask`,
        method: 'put',
        data
    })
}


// 完成委托
export function completeDelegateTask(data) {
    return request({
        url: `/workflow/workflowTask/completeDelegateTask`,
        method: 'put',
        data
    })
}
// 解决委托
export function resolveDelegateTask(data) {
    return request({
        url: `/workflow/workflowTask/resolveDelegateTask`,
        method: 'put',
        data
    })
}

// 签收任务
export function claimTask(taskId) {
  return request({
    url: `/workflow/workflowTask/claimTask`,
    method: 'put',
    data: {
      taskId: taskId
    }
  })
}

// 代办任务
export function myTask() {
  return request({
    url: `/workflow/workflowTask/myTask/page`,
    method: 'get',
  })
}

// 代办任务
export function getProcessTrackingList(taskId) {
  return request({
    url: `/workflow/workflowTask/getProcessTrackingList/`+taskId,
    method: 'get',
  })
}

// 获取当前任务的历史任务节点
export function historicTaskInstanceDict(taskId) {
  return request({
    url: `/workflow/workflowTask/historicTaskInstanceDict/`+taskId,
    method: 'get',
  })
}


export default {add, edit, del, getPage, getFlowImgByProcessInstanceId ,assignTask, completeTask, rollbackTask, completeDelegateTask, resolveDelegateTask,claimTask,myTask,getProcessTrackingList,historicTaskInstanceDict}
