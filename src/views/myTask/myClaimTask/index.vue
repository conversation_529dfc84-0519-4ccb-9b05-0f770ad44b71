<template>
    <div class="app-container">
        <!--工具栏-->
        <div class="head-container">
            <div v-if="crud.props.searchToggle">
                <!-- 搜索 -->
                <el-input v-model="query.name" clearable size="small" placeholder="输入名称搜索" style="width: 200px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
                <el-date-picker
                        v-model="query.createTime"
                        :default-time="['00:00:00','23:59:59']"
                        type="daterange"
                        range-separator=":"
                        size="small"
                        class="date-item"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                />
                <rrOperation />
            </div>
            <crudOperation :permission="permission" />
        </div>

        <el-dialog :visible.sync="imgPreview" title="实时流程图" width="80%">
            <DrawingPreview :svgData="currentImgData" :imgPreview="imgPreview"/>
        </el-dialog>
        <!--表单组件-->
        <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
            <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
                <template v-for="(item, key) in formFields" >
                    <template v-if="item.type == 'radio'">
                        <el-form-item  label="状态" :prop="item.fieldName" :key="key">
                            <el-radio v-for="item in dict.dept_status" :key="item.id" v-model="form.enabled" :label="item.value">{{ item.label }}</el-radio>
                        </el-form-item>
                    </template>
                    <template v-if="item.type == 'input'">
                        <el-form-item :label="item.label" :prop="item.fieldName"  :key="key">
                            <el-input v-model="form[item.fieldName]" :style="formInputStyle"/>
                        </el-form-item>
                    </template>
                    <template v-if="item.type == 'date'">
                        <el-form-item :label="item.label" :prop="item.fieldName"  :key="key" >
                            <el-date-picker
                                    v-model="form[item.fieldName]"
                                    value-format="yyyy年MM月dd日 HH:mm:ss"
                                    type="datetime"
                                    :style="formInputStyle"
                                    placeholder="选择日期时间">
                            </el-date-picker>
                        </el-form-item>
                    </template>
                    <template v-if="item.type == 'select'">
                        <el-form-item :label="item.label" :prop="item.fieldName"  :key="key" >
                            <el-select v-model="form[item.fieldName]" :style="formInputStyle" placeholder="请选择">
                                <el-option
                                        v-for="(item) in dict.project_types"
                                        :key="item.code"
                                        :label="item.name"
                                        :value="item.code"
                                />
                            </el-select>
                        </el-form-item>
                    </template>
                    <template v-if="item.type == 'textarea'">
                        <el-form-item :label="item.label" :prop="item.fieldName"  :key="key">
                            <el-input :type="item.type" v-model="form[item.fieldName]" style="width: 370px;" />
                        </el-form-item>
                    </template>
                </template>

            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="text" @click="crud.cancelCU">取消</el-button>
                <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
            </div>
        </el-dialog>
        <!--表格渲染-->
        <el-table ref="table" v-loading="crud.loading" :tree-props="{children: 'children', hasChildren: 'hasChildren'}" default-expand-all :data="crud.data" row-key="id" @select="crud.selectChange" @select-all="crud.selectAllChange" @selection-change="crud.selectionChangeHandler" border>
            <el-table-column :selectable="checkboxT" type="selection" width="55" />
            <template v-for="(item, key) in tableListFields">
                <el-table-column :label="item.label" :prop="item.fieldName" :key="key">
                    <template v-slot="scope">
                        {{ item.fieldName == 'createTime' ? parseTime(scope.row[ item.fieldName ]) : scope.row[ item.fieldName ] }}
                    </template>
                </el-table-column>
            </template>

            <el-table-column v-permission="['admin',
            'myTask:myClaimTask:lookProcess',
            'myTask:myClaimTask:signTask']" label="操作" width="130px" align="center" fixed="right">
                <template v-slot="scope">
                    <el-button v-permission="['admin','myTask:myClaimTask:lookProcess']" size="mini" style="margin-right: 3px;" type="text" @click="showFlowImg(scope.row)">查看实时流程图</el-button>
                    <div v-if="scope.row.statusCd == 2">
                        <el-button v-permission="['admin','myTask:myClaimTask:signTask']" size="mini" style="margin-right: 3px;" type="text" @click="claimTask(scope.row)">签收任务</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination />
    </div>
</template>

<script>
    import crudRequest from './config/request'
    import '@riophae/vue-treeselect/dist/vue-treeselect.css'
    import CRUD, { presenter, header, form, crud } from '@crud/crud'
    import rrOperation from '@crud/RR.operation'
    import crudOperation from '@crud/CRUD.operation'
    import udOperation from '@crud/UD.operation'
    import pagination from '@crud/Pagination'
    import DrawingPreview from '@/components/DrawingPreview/index'
    //注入配置文件
    import config from './config/index'

    const formFields = config.getFields('form');
    const tableListFields = config.getFields('tableList');
    const  defaultForm = config.getDefaultForm( formFields );
    export default {
        name: 'simpleList',
        components: {  crudOperation, rrOperation, udOperation, pagination, DrawingPreview },
        cruds() {
            return CRUD({ title: '模板', url: config.requestUrl, crudMethod: { ...crudRequest }, sort:[], optShow: {reset: true }})
        },
        mixins: [presenter(), header(), form(defaultForm), crud()],
        // 设置数据字典
        dicts: [],
        data() {
            return {
                imgPreview: false,
                currentImgData: '',
                formFields: formFields,
                tableListFields: tableListFields,
                rules: {
                    name: [
                        { required: true, message: '请输入名称', trigger: 'blur' }
                    ]
                },
                permission: {},
            }
        },
        methods: {
            // 查看实时流程图
            showFlowImg(row) {
                crudRequest.getFlowImgByProcessInstanceId(row)
                .then(res=>{
                    this.currentImgData = res;
                    this.imgPreview = true;
                })
            },
            // 签收任务
            claimTask(row) {
                crudRequest.claimTask(row.id).then(res=>{
                    this.crud.notify('操作成功', 'success');
                    this.crud.toQuery();
                })
            },

            checkboxT(row, rowIndex) {
                return row.id !== 1
            }
        }
    }
</script>

