import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;

export function getPage(params) {
    return request({
        url: requestUrl + '/page',
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: requestUrl,
        method: 'post',
        data
    })
}

export function del(ids) {
    return request({
        url: requestUrl,
        method: 'delete',
        data: ids
    })
}

export function edit(data) {
    return request({
        url: requestUrl,
        method: 'put',
        data
    })
}

export function getFlowImgByProcessInstanceId(data) {
    return request({
        url: `/workflow/workflowTask/getFlowImgByProcessInstanceId/${data.processInstanceId}`,
        method: 'get',
    })
}

// 签收任务
export function claimTask(taskId) {
    return request({
        url: `/workflow/workflowTask/claimTask`,
        method: 'put',
        data: {
            taskId: taskId
        }
    })
}





export default { add, edit, del, getPage, getFlowImgByProcessInstanceId ,claimTask}
