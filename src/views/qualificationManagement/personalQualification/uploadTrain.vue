<template>
    <div class="filter-item">
        <el-upload
            v-permission="['admin','qualificationManagement:personalQualification:upload']"
            ref="logoUpload"
            accept=".gz"
            action=""
            :on-success="uploadTrainData"
            :show-file-list="false"
            :http-request="httpRequest"
        >  
            <el-button size="mini" type="primary">导入培训记录</el-button>
        </el-upload>
        
        <el-dialog
            title="导入成功"
            :visible.sync="dialogVisible"
            width="30%">
            <!-- 未关联人员列表 -->
            <div class="unmatched-list">
                <h4>未关联成功的人员名单</h4>
                <div class="names-container">
                    <!-- 动态插入未关联人名 -->
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {upload} from '@/utils/upload'
export default {
    name: "UploadTrain",
    data() {
        return {
            dialogVisible: false
        }
    },
    methods: {
        uploadTrainData() {
            this.msgSuccess('数据上传成功!')
        },
        httpRequest(data) {

            const loading = this.$loading({
                lock: true,
                text: '正在上传，请稍后',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            upload('qualification/personalQualification/uploadTraining', data.file).then(res => {
                if (res.data.status == 200) {
                    this.$message.success('上传成功!');
                    this.showUnmatchedNames(res.data.data);
                } else {
                    this.$message.error(res.data.message);
                }
                loading.close();
            }).catch(e => {
                loading.close();
                this.$message.error('上传失败！请稍后重试')
            })
        },
        showUnmatchedNames(names) {
            names.length > 0 && (this.dialogVisible = true)
            this.$nextTick(() => {
                const container = document.querySelector('.names-container');
                if (container) {
                    container.innerHTML = names
                    .map(name => `<div class="name-item">${name}</div>`)
                    .join('');
                } else {
                    console.error('.names-container 未找到');
                }
            });
        }
    }
}
</script>

<style scoped>
.names-container {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #eee;
    padding: 10px;
}
</style>