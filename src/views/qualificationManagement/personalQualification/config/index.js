export default {
  //获取某类型字段列表
  getFields(type) {
    return this.parames.filter(item => {
      return item.whereShow.some((citem) => citem == type)
    })
  },
  //加工页面formData
  getDefaultForm(data) {
    var json = {};
    data.map(item => {
      json[item.fieldName] = item.defaultVal;
    })
    return json;
  },
  //页面请求地址
  requestUrl: '/qualification/personalQualification',

  //页面字段参数，可根据实际需求扩展
  parames: [
	  {
		  label: '岗位认证等级',
		  fieldName: 'positionLevel',
		  defaultVal: '1',
		  type: 'select',
		  fixed: true,
		  whereShow: ['tableList', 'form', 'add', 'edit']
	  },
    {
      label: '资格等级',
      fieldName: 'qualificationLevelCd',
      defaultVal: '1',
      type: 'select',
      fixed: true,
      whereShow: ['tableList', 'form', 'add', 'edit']
    },{
      label: '主键ID',
      fieldName: 'id',
      defaultVal: '',
      type: 'input',
      whereShow: []
    },
    {
      label: '姓名',
      fieldName: 'name',
      defaultVal: '',
      type: 'input',
      fixed: true,
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '性别',
      fieldName: 'gender',
      defaultVal: '',
      type: 'select',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '年龄',
      fieldName: 'age',
      defaultVal: '',
      vShow: false,
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '出生日期',
      fieldName: 'birthday',
      defaultVal: '',
      type: 'date',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '项目公司',
      fieldName: 'companyName',
      defaultVal: '',
      vShow: false,
      type: 'select',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '身份证号',
      fieldName: 'cardCode',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '岗位认证专业',
      fieldName: 'costCertificateMajorCd',
      defaultVal: '',
      type: 'select',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '注册证号',
      fieldName: 'registerNumber',
      type: 'firstInput',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '印章编号',
      fieldName: 'sealNumber',
      type: 'firstInput',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '二级单位',
      fieldName: 'ownedSecondaryCompany',
      defaultVal: '',
      type: 'select',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },

    {
      label: '工作起始',
      fieldName: 'startYear',
      defaultVal: '',
      vShow: false,
      type: 'datepicker',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '通过年度',
      fieldName: 'examPassedYear',
      type: 'firstDateYear',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '注册日期',
      fieldName: 'initialRegistrationDate',
      type: 'firstDate',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '有效期',
      fieldName: 'certificateValidityPeriod',
      type: 'firstDate',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '发证部门',
      fieldName: 'issuingDepartment',
      defaultVal: '',
      vShow: false,
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },


    {
      label: '职务',
      fieldName: 'job',
      defaultVal: '',
      vShow: false,
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '从事岗位',
      fieldName: 'workPosition',
      defaultVal: '',
      vShow: false,
      type: 'select',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '职称',
      fieldName: 'jobTitleCd',
      defaultVal: '',
      vShow: false,
      type: 'select',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '学历',
      fieldName: 'educationCd',
      defaultVal: '',
      vShow: false,
      type: 'select',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '所学专业',
      fieldName: 'major',
      defaultVal: '',
      vShow: false,
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '毕业院校',
      fieldName: 'graduatedSchool',
      defaultVal: '',
      vShow: false,
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '毕业时间',
      fieldName: 'graduationDate',
      defaultVal: '',
      vShow: false,
      type: 'date',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '移动电话',
      fieldName: 'mobile',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },


    {
      label: '民族',
      fieldName: 'nation',
      defaultVal: '',
      vShow: false,
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '政治面貌',
      fieldName: 'politicsStatus',
      defaultVal: '',
      vShow: false,
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '员工编码',
      fieldName: 'workerNo',
      defaultVal: '',
      vShow: false,
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },


    {
      label: '岗位持证情况',
      fieldName: 'jobsHolder',
      defaultVal: '',
      type: 'select',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '通讯地址',
      fieldName: 'address',
      defaultVal: '',
      vShow: false,
      type: 'textarea',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
  ]
}
