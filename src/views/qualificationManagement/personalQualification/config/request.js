import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;

export function getPage(params) {
    return request({
        url: requestUrl,
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: requestUrl,
        method: 'post',
        data
    })
}

export function del(ids) {
    return request({
        url: requestUrl,
        method: 'delete',
        data: ids
    })
}

export function edit(data) {
    return request({
        url: requestUrl,
        method: 'put',
        data
    })
}

export function getTrainingList(qualPersonalId) {
    return request({
        url: requestUrl + '/getTrainingList',
        method: 'get',
        params: {
            qualPersonalId
        }
    })
}

export function relationTraining(qualPersonalId,trainingUserId) {
    return request({
        url: requestUrl + '/relationTraining',
        method: 'put',
        params: {
            qualPersonalId,
            trainingUserId
        }
    })
}

export function getTrainingUserList() {
    return request({
        url: requestUrl + '/getTrainingUserList',
        method: 'get'
    })
}

export function getSecondUnitList(){
    return request({
        url: requestUrl + '/getSecondUnitList',
        method: 'get'
    })
}
export default { add, edit, del, getPage, getTrainingList, relationTraining, getTrainingUserList, getSecondUnitList}
