<template>
  <div>
    <el-button
      v-permission="['admin','qualificationManagement:personalQualification:upload']"
      type="primary"
      size="mini"
      style="margin-bottom: 10px"
      @click="openDialog()"
      >关联培训</el-button
    >
    <el-table :data="tableData">
      <el-table-column prop="nickName" label="培训人员"> </el-table-column>
      <el-table-column prop="formName" label="培训名称"> </el-table-column>
      <el-table-column prop="sumScore" label="成绩"> </el-table-column>
      <el-table-column prop="onlineTime" label="上线时间"> </el-table-column>
    </el-table>

    <el-dialog title="关联培训人员" :visible.sync="dialogVisible" append-to-body>
      <el-table height="400" :data="trainingUserTableData.filter(data => !search || data.nickname.toLowerCase().includes(search.toLowerCase()))" style="width: 100%">
        <el-table-column prop="nickname" label="昵称"> </el-table-column>
        <el-table-column prop="orgName" label="公司名称"> </el-table-column>
        <el-table-column prop="phone" label="手机号"> </el-table-column>
        <el-table-column prop="memberNo" label="工号"> </el-table-column>
        <el-table-column label="操作" align="center">
            <template slot="header" slot-scope="scope">
                <el-input
                v-model="search"
                size="mini"
                placeholder="输入昵称搜索"/>
            </template>
            <template slot-scope="scope">
                <el-button type="text" size='mini' @click="relationTraining(scope.row)">关联</el-button>
            </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>
<script>
import crudRequest from "./config/request";
export default {
  components: { crudRequest },
  name: "TrainingTable",
  props: {
    qualPersonalId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      isShow: false,
      tableData: [],
      trainingUserTableData: [],
      dialogVisible: false,
      search: '',
    };
  },
  watch: {
    qualPersonalId: {
        handler(newValue) {
            this.getTrainingList();
        },
        immediate: true // 如果需要在组件创建时立即调用
    }
  },
  methods: {
    getTrainingList() {
      crudRequest.getTrainingList(this.qualPersonalId).then(res => {
        this.tableData = res;
      });
    },
    // 关联培训
    relationTraining(row) {
        // 确认关联
        this.$confirm('确定关联吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            console.log(row.id);
            
            crudRequest.relationTraining(this.qualPersonalId, row.id).then(res => {
                this.$message.success('关联成功');
                this.dialogVisible = false;
                this.getTrainingList();
            })
        })
    },
    openDialog() {
        crudRequest.getTrainingUserList().then(res => {                        
            this.trainingUserTableData = res;
        })
        this.dialogVisible = true;
    }
  },
};
</script>
