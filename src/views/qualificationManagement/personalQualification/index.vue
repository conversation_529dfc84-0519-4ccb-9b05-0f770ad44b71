<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.name" clearable size="small" placeholder="输入姓名搜索" style="width: 200px;"
                  class="filter-item" @keyup.enter.native="crud.toQuery"/>
        <el-input v-model="query.mobile" clearable size="small" placeholder="输入电话搜索" style="width: 200px;"
                  class="filter-item" @keyup.enter.native="crud.toQuery"/>
        <el-date-picker
          v-model="query.queryTime"
          :default-time="['00:00:00','23:59:59']"
          type="daterange"
          range-separator=":"
          size="small"
          class="date-item"
          value-format="yyyy-MM-dd"
          start-placeholder="毕业开始日期"
          end-placeholder="毕业结束日期"
          @change="crud.toQuery"
        />
        <el-select v-model="query.qualificationLevelCd" placeholder="资格证等级" class="filter-item"
                   @change="queryQualification">
          <el-option
            v-for="(item) in dict.grade_qualification_certificate"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
        <rrOperation/>
      </div>
      <crudOperation ref="crudOperation" :disabledDle="disabledCrudDel" :disabledEdit="disabledCrudEdit" :permission="permission">
        <template slot="right">
          <UploadTrain/>
          <el-upload
            v-permission="['admin','qualificationManagement:personalQualification:upload']"
            action=""
            :http-request="httpRequest"
            :show-file-list="false"
            class="filter-item"
          >
            <el-button size="mini" type="primary">导入人员资质</el-button>
            <span type="text" class="el-upload__tip" @click.stop="downTemplate">模板<i
              class="el-icon-download el-icon--right"></i>
            </span>
          </el-upload>
        </template>
      </crudOperation>



    </div>

    <!--表单组件-->
    <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU"
               :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="1140px">
      <el-form ref="form" :disabled="crud.status.details > 0" :model="form" :inline="true" :rules="rules" label-position="top" label-width="120px" style="padding: 20px 0 0 20px">
        <template v-for="(item, key) in formFields">
          <template v-if="item.type == 'radio'">
            <el-form-item label="状态" :prop="item.fieldName" :key="key">
              <el-radio v-for="item in dict.dept_status" :key="item.id" v-model="form.enabled" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-form-item>
          </template>
          <template v-if="item.type == 'input' && (item.vShow === undefined?true:item.vShow)">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-input v-model="form[item.fieldName]" />
            </el-form-item>
          </template>
          <template v-else-if="item.type == 'date' && (item.vShow === undefined?true:item.vShow)">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-date-picker
                v-model="form[item.fieldName]"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                type="date"
                placeholder="选择日期时间">
              </el-date-picker>
            </el-form-item>
          </template>
          <template v-else-if="item.fieldName == 'startYear'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key" v-if="item.vShow">
              <el-date-picker
                :picker-options="pickerOption"
                v-model="form[item.fieldName]"
                format="yyyy"
                type="year"

                placeholder="选择日期时间">
              </el-date-picker>
              <span class="congye">
                <el-tag size="small">从业{{ difference(form[item.fieldName]) }}年</el-tag>
              </span>
            </el-form-item>
          </template>
          <template v-else-if="item.fieldName == 'gender'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]"  placeholder="请选择">
                <el-option
                  v-for="(item) in dict.sex"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-else-if="item.fieldName == 'jobTitleCd'  && (item.vShow === undefined?true:item.vShow)">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]"  placeholder="请选择">
                <el-option
                  v-for="(item) in dict.title"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-else-if="item.fieldName == 'educationCd'  && (item.vShow === undefined?true:item.vShow)">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]"  placeholder="请选择">
                <el-option
                  v-for="(item) in dict.education"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-else-if="item.fieldName == 'ownedSecondaryCompany'  && (item.vShow === undefined?true:item.vShow)">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]"  placeholder="请选择" clearable
                         filterable>
                <el-option
                  v-for="(item) in sonOrgList"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-else-if=" item.fieldName == 'workPosition'  && (item.vShow === undefined?true:item.vShow)">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]"  placeholder="请选择">
                <el-option
                  v-for="(item) in dict.post"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-else-if="item.fieldName == 'qualificationLevelCd'  && (item.vShow === undefined?true:item.vShow)">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]"  placeholder="请选择"
                         @change="qualification">
                <el-option
                  v-for="(item) in dict.grade_qualification_certificate"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-else-if="item.fieldName == 'costCertificateMajorCd'  && (item.vShow === undefined?true:item.vShow)">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]" multiple  placeholder="请选择"
                         filterable clearable>
                <el-option
                  v-for="(item) in dict.cost_certificate_major"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-else-if="item.fieldName == 'companyName'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]"  placeholder="请选择" filterable>
                <el-option
                  v-for="(item) in childOrgList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-else-if="item.type == 'firstInput' && isShow">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-input v-model="form[item.fieldName]" />
            </el-form-item>
          </template>
          <template v-else-if="item.type == 'firstDateYear' && isShow">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-date-picker  v-model="form[item.fieldName]" type="year"
                              placeholder="选择年"></el-date-picker>
            </el-form-item>
          </template>
          <template v-else-if="item.type == 'firstDate' && isShow">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-date-picker  v-model="form[item.fieldName]" type="date"
                              placeholder="选择日期"></el-date-picker>
            </el-form-item>
          </template>
          <template v-else-if="item.type == 'firstDatePicker' && isShow">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-date-picker  v-model="form[item.fieldName]" type="daterange"
                              range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </template>
          <template v-else-if="item.type == 'textarea'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key" style="width: 100% !important;">
              <el-input :type="item.type" v-model="form[item.fieldName]" style="width: 1030px;"/>
            </el-form-item>
          </template>
          <template v-else-if="item.fieldName == 'jobsHolder'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]"  placeholder="请选择" filterable>
                <el-option
                  v-for="(item) in dict.jobsHolder"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-else-if="item.fieldName == 'positionLevel'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]"  placeholder="请选择" filterable>
                <el-option
                    v-for="(item) in dict['position_level']"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                />
              </el-select>
            </el-form-item>
          </template>
        </template>
        <el-divider>岗位证书</el-divider>
        <uploadImage
          :file-list="form.imgList"
          :remove-file="removeFile">
        </uploadImage>
        <el-divider>培训记录</el-divider>
        <TrainingTable :qualPersonalId="form.id"></TrainingTable>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>
    <!--表格渲染-->
    <el-table ref="table" v-loading="crud.loading" :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              default-expand-all :data="crud.data" row-key="id" @select="selectChange"
              @select-all="crud.selectAllChange" @selection-change="crud.selectionChangeHandler" border>
      <el-table-column :selectable="checkboxT" type="selection" width="55"/>
      <template v-for="(item, key) in tableListFields">
        <el-table-column :label="item.label"
                         :prop="item.fieldName" :key="key"
                         width="200px" :fixed="item.fixed">
          <template v-slot="scope">
            <template v-if="item.fieldName == 'gender'">
              {{ showCurrentData(dict.sex, scope.row[item.fieldName]) }}
            </template>
            <template v-else-if="item.fieldName === 'name'">
              <el-button type="text" size="small" @click="crud.toDetails(scope.row)">
                {{ scope.row.name }}
              </el-button>
            </template>
            <template v-else-if="item.fieldName == 'costCertificateMajorCd'">
              {{ showArrayCurrentData(dict.cost_certificate_major, scope.row[item.fieldName]) }}
            </template>
            <template v-else-if="item.fieldName == 'qualificationLevelCd'">
              {{ showCurrentData(dict.grade_qualification_certificate, scope.row[item.fieldName]) }}
            </template>
            <template v-else-if="item.fieldName == 'workPosition'">
              {{ showCurrentData(dict.post, scope.row[item.fieldName]) }}
            </template>
            <template v-else-if="item.fieldName == 'educationCd'">
              {{ showCurrentData(dict.education, scope.row[item.fieldName]) }}
            </template>
            <template v-else-if="item.fieldName == 'ownedSecondaryCompany'">
              {{ showCurrentData(sonOrgList, scope.row[item.fieldName]) }}
            </template>
            <template v-else-if="item.fieldName == 'companyName'">
              {{ showCurrentData(childOrgList, scope.row[item.fieldName], 'value', 'label') }}
            </template>
            <template v-else-if="item.fieldName == 'startYear'">
              {{ dateFormatVal(scope.row[item.fieldName], "YYYY") }}
            </template>
            <template v-else-if="item.type == 'date'">
              {{ dateFormatVal(scope.row[item.fieldName], "YYYY-MM-DD") }}
            </template>
            <template v-else-if="item.fieldName == 'jobsHolder'">
              {{ showCurrentData(dict.jobsHolder, scope.row[item.fieldName]) }}
            </template>
            <template v-else-if="item.fieldName == 'positionLevel'">
              {{ showCurrentData(dict['position_level'], scope.row[item.fieldName]) }}
            </template>
            <template v-else>
              {{ scope.row[item.fieldName] }}
            </template>
          </template>
        </el-table-column>
      </template>

      <el-table-column
        v-permission="['admin','qualificationManagement:personalQualification:edit','qualificationManagement:personalQualification:del','qualificationManagement:personalQualification:details']"
        label="操作" width="160px" align="center" fixed="right">
        <template v-slot="scope">
          <udOperation
            :data="scope.row"
            :permission="permission"
            :disabledDle="(scope.row.createUserName !==user.nickname && !user.ifOrgAdmin) && user.orgTypeCd !== 1"
            :disabledEdit="(scope.row.createUserName !==user.nickname && !user.ifOrgAdmin) && user.orgTypeCd !== 1"
            msg="确定删除吗,如果存在下级节点则一并删除，此操作不能撤销！"
          />
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination/>
  </div>
</template>

<script>
import crudRequest from './config/request'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import moment from "moment";
import {getOrgCodeDict} from '@/views/user/org/config/request'

//注入配置文件
import config from './config/index'
import {mapGetters} from "vuex";
import {upload} from '@/utils/upload'
import uploadImage from '@/components/AttachmentsUploadFile/uploadImage'
import {getOneByType} from "../../fileTemplate/config/request";
import request from '@/utils/request'
import {downloadFile} from '@/utils/index'
import UploadTrain from './uploadTrain'
import TrainingTable from './trainingTable'

const formFields = config.getFields('form');
const tableListFields = config.getFields('tableList');
const defaultForm = config.getDefaultForm(formFields);
export default {
  name: 'simpleList',
  watch: {
    'query.qualificationLevelCd': {
      handler(val) {
        if (val === undefined) {
          this.queryQualification(0);
        }
      }
    }
  },
  components: {crudOperation, rrOperation, udOperation, pagination, uploadImage, UploadTrain, TrainingTable},
  cruds() {
    return CRUD({title: '个人资质', url: config.requestUrl, crudMethod: {...crudRequest}, columnDropIndex: 1})
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 设置数据字典
  dicts: ['sex', 'cost_certificate_major', 'grade_qualification_certificate', 'post', 'title', 'education', 'jobsHolder', 'position_level'],
  computed: {
    ...mapGetters([
      'user',
    ])
  },
  data() {
    return {
      disabledCrudDel: false,
      disabledCrudEdit: false,
      pickerOption: {
        disabledDate: (time) => {
          return time.getTime() > Date.now() - 1 * 24 * 3600 * 1000
        }
      },
      isShow: true,
      sonOrgList: [],
      childOrgList: [],
      formFields: formFields,
      tableListFields: tableListFields,
      rules: {
        name: [
          {required: true, message: '请输入名称', trigger: 'blur'},
        ],
      },
      permission: {
        add: ['admin', 'qualificationManagement:personalQualification:add'],
        edit: ['admin', 'qualificationManagement:personalQualification:edit'],
        del: ['admin', 'qualificationManagement:personalQualification:del'],
        details: ['admin', 'qualificationManagement:personalQualification:details']
      },
      dialogVisible: false,
    }
  },
  methods: {
    getSecondUnitList(){
      crudRequest.getSecondUnitList().then(res=>{
        console.log(res)
        this.sonOrgList = res
      })
    },
    selectChange(selection, row) {
      for (let item of selection) {
        if ((item.createUserName !== this.user.nickname && !this.user.ifOrgAdmin) && this.user.orgTypeCd !== 1) {
          this.disabledCrudDel = true;
          this.disabledCrudEdit = true;
          return;
        } else {
          this.disabledCrudDel = false;
          this.disabledCrudEdit = false;
        }
      }
    },
    queryQualification(val) {
      if (1 == val) {
        this.crud.props.tableColumns.forEach((item, index) => {
          switch (item['property']) {
            case 'age':
            case 'startYear':
            case 'costCertificateMajorCd':
            case 'qualificationLevelCd':
            case 'issuingDepartment':
            case 'ownedSecondaryCompany':
            case 'job':
            case 'workPosition':
            case 'jobTitleCd':
            case 'educationCd':
            case 'major':
            case 'graduatedSchool':
            case 'graduationDate':
            case 'address':
              item.visible = false
              this.$refs.crudOperation.updateColumnVisible(item);
              break;
          }
        })
      } else {
        this.crud.props.tableColumns.forEach(item => {
          if (!item.visible) {
            item.visible = true
            this.$refs.crudOperation.updateColumnVisible(item);
          }
        })
      }
      this.crud.toQuery();
    },
    difference(date) {
      if (date === null || date === undefined) {
        return 0;
      }
      let number = new Date().getFullYear() - new Date(date).getFullYear();
      return isNaN(number) || number < 0 ? 0 : number;
    },

    [CRUD.HOOK.beforeToEdit](crud) {
      this.qualification(crud.form['qualificationLevelCd'])
      crud.form.gender = crud.form.gender ? crud.form.gender.toString() : ''
      crud.form.costCertificateMajorCd = crud.form.costCertificateMajorCd ? crud.form.costCertificateMajorCd.split(';') : []
      crud.form.qualificationLevelCd = crud.form.qualificationLevelCd ? crud.form.qualificationLevelCd.toString() : ''
      crud.form.jobsHolder = crud.form.jobsHolder ? crud.form.jobsHolder.toString() : ''
      crud.form.ownedSecondaryCompany = crud.form.ownedSecondaryCompany ? crud.form.ownedSecondaryCompany.toString() : ''
      crud.form.workPosition = crud.form.workPosition ? crud.form.workPosition.toString() : ''
      crud.form.jobTitleCd = crud.form.jobTitleCd ? crud.form.jobTitleCd.toString() : ''
      crud.form.educationCd = crud.form.educationCd ? crud.form.educationCd.toString() : ''
      crud.form.positionLevel = crud.form.positionLevel ? crud.form.positionLevel.toString() : ''
    },
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.costCertificateMajorCd = crud.form.costCertificateMajorCd ? crud.form.costCertificateMajorCd.join(";") : ''
    },
    [CRUD.HOOK.beforeToAdd](crud) {
      crud.form.imgList = []
      this.qualification(1);
      if (this.childOrgList.length === 1) {
        this.form.companyName = this.childOrgList[0].value;
      }
      if (this.sonOrgList.length === 1) {
        this.form.ownedSecondaryCompany = this.sonOrgList[0].code;
      }
    },
    qualification(val) {
      if (val == 1) {
        this.isShow = true
        this.formFields.forEach(item => {
          if (item.vShow !== undefined && item.vShow) {
            item.vShow = false;
          }
        })
      } else {
        this.isShow = false
        this.formFields.forEach(item => {
          if (item.vShow !== undefined && !item.vShow) {
            item.vShow = true;
          }
        })
      }
    },
    dateFormatVal(val, formatValue) {
      if (val !== null) {
        return moment(val).format(formatValue)
      }
    },
    checkboxT(row, rowIndex) {
      return row.id !== 1
    },
    httpRequest(data) {
      const loading = this.$loading({
        lock: true,
        text: '正在上传，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      upload('qualification/personalQualification/upload', data.file)
        .then(res => {
          if (res.data.status === 200) {
            this.crud.notify('导入成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
            this.crud.refresh()
          } else {
            this.$message.error(res.data.message);
          }
          loading.close();
        })
        .catch(e => {
          loading.close();
          this.$message.error('上传失败！请稍后重试！');
        })
    },
    removeFile(file) {
      this.form.imgList = this.form.imgList.filter(p => p.id !== file.id)
    },
    downTemplate() {
      //模板类型（1：结算，2：概算,3 人员资质）
      getOneByType("3").then(item => {
        if (item) {
          request({
            url: '/general/file/download?uri=' + item.templateUrl,
            type: 'get',
            responseType: 'blob'
          })
            .then(res => {
              downloadFile(res, item.templateFileName)
            })
        } else {
          this.$message.error('请在模板菜单维护对应模板！')
        }
      })
    },
  },
  mounted() {
    this.getSecondUnitList();
    getOrgCodeDict().then(res => {
      this.childOrgList = res;
    })
  },

}
</script>

<style type="css" scoped>
.congye {
  position: absolute;
  margin: 0px 5px;
}

.congye .el-tag--small {
  padding: 0 4px;
}

.personal-upload-button {
  position: absolute;
  margin-top: -42px;
  margin-left: 253px;
}

.form-style-class {
  background: #F9FCFF;
  padding: 10px 0 0 18px;
}

.form-top-line {
  background: #8D969E;
  height: 2px;
  width: 100%;
}

.form-child-line {
  background: #8D969E;
  height: 2px;
  width: 100%;
  margin-top: 20px;
}

.el-form-item {
  width: 200px !important;
  .el-input {
    width: 100% !important;
  }
}

>>> .el-form-item--mini.el-form-item {
  margin-bottom: 20px;
}

>>> .el-form-item--mini .el-form-item__label {
  line-height: 10px;
}

.el-upload__tip {
  font-size: 10px;
  color: #1890ff;
  margin-top: 0;
}
</style>
