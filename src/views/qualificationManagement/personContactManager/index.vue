<template>
    <div class="app-container">
        <!--工具栏-->
        <div class="head-container">
            <div v-if="crud.props.searchToggle">
                <!-- 搜索 -->
                <el-input 
                    v-model="query.name" 
                    clearable 
                    size="small" 
                    placeholder="输入姓名搜索" 
                    style="width: 200px;" 
                    class="filter-item" 
                    @keyup.enter.native="crud.toQuery" 
                />
                <el-select 
                    v-model="query.projectCompanyId" 
                    clearable 
                    size="small" 
                    placeholder="项目公司" 
                    class="filter-item" 
                    style="width: 200px" 
                    @change="crud.toQuery"
                >
                    <el-option 
                        v-for="item in projectCompanyOptions" 
                        :key="item.value" 
                        :label="item.label" 
                        :value="item.value" 
                    />
                </el-select>
                <el-select 
                    v-model="query.roleCode" 
                    clearable 
                    size="small" 
                    placeholder="角色" 
                    class="filter-item" 
                    style="width: 120px" 
                    @change="crud.toQuery"
                >
                    <el-option 
                        v-for="item in dict.person_contact_role" 
                        :key="item.code" 
                        :label="item.name" 
                        :value="item.code" 
                    />
                </el-select>
                <rrOperation />
            </div>
            <crudOperation :permission="permission" />
        </div>
        
        <!--表单组件-->
        <el-dialog 
            append-to-body 
            :close-on-click-modal="false" 
            :before-close="crud.cancelCU" 
            :visible.sync="crud.status.cu > 0" 
            :title="crud.status.title" 
            width="600px"
        >
            <el-form ref="form" :model="form" :rules="rules" size="small" label-width="100px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="项目公司" prop="projectCompanyId">
                            <el-select 
                                v-model="form.projectCompanyId" 
                                placeholder="请选择项目公司" 
                                style="width: 100%"
                                @change="handleProjectCompanyChange"
                                filterable
                            >
                                <el-option 
                                    v-for="item in projectCompanyOptions" 
                                    :key="item.value" 
                                    :label="item.label" 
                                    :value="item.value" 
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="角色" prop="roleCode">
                            <el-select 
                                v-model="form.roleCode" 
                                placeholder="请选择角色" 
                                style="width: 100%"
                                @change="handleRoleChange"
                            >
                                <el-option 
                                    v-for="item in dict.person_contact_role" 
                                    :key="item.code" 
                                    :label="item.name" 
                                    :value="item.code" 
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="姓名" prop="name">
                            <el-input v-model="form.name" placeholder="请输入姓名" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="岗位" prop="positionCode">
                            <el-input v-model="form.positionCode" placeholder="请输入岗位" />
                            <!-- <el-select 
                                v-model="form.positionCode" 
                                placeholder="请选择岗位" 
                                style="width: 100%"
                                @change="handlePositionChange"
                            >
                                <el-option 
                                    v-for="item in dict.person_position" 
                                    :key="item.code" 
                                    :label="item.name" 
                                    :value="item.code" 
                                />
                            </el-select> -->
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="座机" prop="landline">
                            <el-input v-model="form.landline" placeholder="请输入座机号码" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="手机" prop="mobile">
                            <el-input v-model="form.mobile" placeholder="请输入手机号码" />
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="邮箱" prop="email">
                            <el-input v-model="form.email" placeholder="请输入邮箱地址" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="传真" prop="fax">
                            <el-input v-model="form.fax" placeholder="请输入传真号码" />
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-form-item label="备注" prop="remark">
                    <el-input 
                        type="textarea" 
                        v-model="form.remark" 
                        placeholder="请输入备注信息" 
                        :rows="3"
                    />
                </el-form-item>
            </el-form>
            
            <div slot="footer" class="dialog-footer">
                <el-button type="text" @click="crud.cancelCU">取消</el-button>
                <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
            </div>
        </el-dialog>
        
        <!--表格渲染-->
        <el-table 
            ref="table" 
            v-loading="crud.loading" 
            :data="crud.data" 
            @select="crud.selectChange" 
            @select-all="crud.selectAllChange" 
            @selection-change="crud.selectionChangeHandler" 
            border
            max-height="600"
            style="width: 100%"
        >
            <el-table-column type="selection" width="55" />
            <el-table-column label="项目公司" prop="projectCompanyName" :show-overflow-tooltip="true" min-width="120" class-name="no-wrap-column">
                <template v-slot="scope">
                     {{ showCurrentData(projectCompanyOptions, scope.row.projectCompanyId, 'value', 'label') }}
                </template>
            </el-table-column>
            <el-table-column label="角色" prop="roleName" :show-overflow-tooltip="true" min-width="100" class-name="no-wrap-column">
                <template v-slot="scope">
                     {{ showCurrentData(dict.person_contact_role, scope.row.roleCode) }}
                </template>
            </el-table-column>
            <el-table-column label="姓名" prop="name" class-name="no-wrap-column" min-width="80" />
            <el-table-column label="岗位" prop="positionCode" class-name="no-wrap-column" min-width="100" />
            <el-table-column label="座机" prop="landline" class-name="no-wrap-column" min-width="120" />
            <el-table-column label="手机" prop="mobile" class-name="no-wrap-column" min-width="120" />
            <el-table-column label="邮箱" prop="email" class-name="no-wrap-column" min-width="120" />
            <!-- <el-table-column label="传真" prop="fax" class-name="no-wrap-column" min-width="120" />
            <el-table-column label="备注" prop="remark" :show-overflow-tooltip="true" min-width="120" class-name="no-wrap-column" /> -->
            <el-table-column label="创建时间" prop="createDate" :show-overflow-tooltip="true" min-width="120" class-name="no-wrap-column">
                <template v-slot="scope">
                    {{ parseTime(scope.row.createDate) }}
                </template>
            </el-table-column>
            
            <el-table-column 
                v-permission="['admin','qualificationManagement:personContact:edit','qualificationManagement:personContact:del']" 
                label="操作" 
                width="130px" 
                align="center" 
                fixed="right"
            >
                <template v-slot="scope">
                    <udOperation
                        :data="scope.row"
                        :permission="permission"
                        msg="确定删除吗？此操作不能撤销！"
                    />
                </template>
            </el-table-column>
        </el-table>
        
        <!--分页组件-->
        <pagination />
    </div>
</template>

<script>
import crudRequest from './config/request'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import { parseTime } from '@/utils'
import {getOrgCodeDict} from '@/views/user/org/config/request'

// 注入配置文件
import config from './config/index'

const formFields = config.getFields('form');
const tableListFields = config.getFields('tableList');
const defaultForm = config.getDefaultForm(formFields);

export default {
    name: 'PersonContactManager',
    components: { crudOperation, rrOperation, udOperation, pagination },
    cruds() {
        return CRUD({ 
            title: '人员通讯录', 
            url: config.requestUrl, 
            crudMethod: { ...crudRequest }
        })
    },
    mixins: [presenter(), header(), form(defaultForm), crud()],
    // 设置数据字典
    dicts: ['person_contact_role'],
    data() {
        return {
            formFields: formFields,
            tableListFields: tableListFields,
            projectCompanyOptions: [], // 项目公司选项
            rules: config.getValidationRules(), // 表单验证规则
            permission: {
                add: ['admin', 'qualificationManagement:personContact:add'],
                edit: ['admin', 'qualificationManagement:personContact:edit'],
                del: ['admin', 'qualificationManagement:personContact:del']
            }
        }
    },
    created() {
        // 加载项目公司列表
        this.loadProjectCompanyList();
    },
    methods: {
        // 加载项目公司列表
        async loadProjectCompanyList() {
            try {
                const loading = this.$loading({
                    lock: true,
                    text: '加载项目公司列表...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
              
                const response = await getOrgCodeDict();
                this.projectCompanyOptions = response || [];
                
                loading.close();
            } catch (error) {
                console.error('加载项目公司列表失败:', error);
                this.$message.error('加载项目公司列表失败');
            }
        },
        
        // 项目公司选择变化处理
        handleProjectCompanyChange(value) {
            const selectedCompany = this.projectCompanyOptions.find(item => item.value === value);
            if (selectedCompany) {
                this.form.projectCompanyName = selectedCompany.label;
            }
        },
        
        // 角色选择变化处理
        handleRoleChange(value) {
            const selectedRole = this.dict.person_contact_role.find(item => item.code === value);
            if (selectedRole) {
                this.form.roleName = selectedRole.name;
            }
        },
        
        // 重写CRUD提交前的钩子，添加额外验证
        [CRUD.HOOK.beforeSubmit]() {
            // 如果填写了邮箱，验证邮箱格式
            if (this.form.email && this.form.email.trim()) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(this.form.email)) {
                    this.$message.error('请输入正确的邮箱格式');
                    return false;
                }
            }
            
            // 如果填写了手机号，验证手机号格式
            if (this.form.mobile && this.form.mobile.trim()) {
                const mobileRegex = /^1[3-9]\d{9}$/;
                if (!mobileRegex.test(this.form.mobile)) {
                    this.$message.error('请输入正确的手机号格式');
                    return false;
                }
            }
            
            return true;
        },
        
        // 重写CRUD提交成功后的钩子
        [CRUD.HOOK.afterSubmit]() {
            
        },
        
        // 重写CRUD删除成功后的钩子
        [CRUD.HOOK.afterDelete]() {
            
        },
        
        // 测试方法：验证所有功能是否正常
        testFunctionality() {
            console.log('=== 人员通讯录功能测试 ===');
            console.log('1. 配置文件:', config);
            console.log('2. 表单字段:', this.formFields);
            console.log('3. 表格字段:', this.tableListFields);
            console.log('4. 验证规则:', this.rules);
            console.log('5. 项目公司选项:', this.projectCompanyOptions);
            console.log('6. 字典数据:', this.dict);
            console.log('7. 权限配置:', this.permission);
            console.log('8. 查询参数:', this.query);
            console.log('=== 测试完成 ===');
        }
    }
}
</script>

<style scoped>
.app-container {
    padding: 20px;
}

.head-container {
    margin-bottom: 20px;
}

.filter-item {
    margin-right: 10px;
    margin-bottom: 10px;
}

.dialog-footer {
    text-align: right;
}

/* 表格样式优化 */
.el-table {
    margin-top: 15px;
    table-layout: fixed;
}

/* 针对特定列的样式优化，防止换行 */
.el-table .no-wrap-column .cell {
    white-space: nowrap !important;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 确保表格单元格不换行 */
.el-table .cell {
    word-break: keep-all;
    white-space: nowrap;
}

/* 表单样式优化 */
.el-form-item {
    margin-bottom: 18px;
}

.el-dialog__body {
    padding: 20px 20px 10px 20px;
}

/* 搜索区域样式 */
.head-container .filter-item:last-child {
    margin-right: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .filter-item {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .el-dialog {
        width: 95% !important;
    }
}
</style>