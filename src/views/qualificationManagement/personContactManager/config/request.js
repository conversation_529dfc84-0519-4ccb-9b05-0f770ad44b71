import request from '@/utils/request'
import config from './index'

const requestUrl = config.requestUrl;

// 获取人员通讯录分页列表
export function getPage(params) {
    return request({
        url: requestUrl,
        method: 'get',
        params
    })
}

// 新增人员信息
export function add(data) {
    return request({
        url: requestUrl,
        method: 'post',
        data
    })
}

// 删除人员信息
export function del(ids) {
    return request({
        url: requestUrl,
        method: 'delete',
        data: ids
    })
}

// 编辑人员信息
export function edit(data) {
    return request({
        url: requestUrl,
        method: 'put',
        data
    })
}

// 获取项目公司列表
export function getProjectCompanyList() {
    return request({
        url: '/general/getProjectCompanyList',
        method: 'get'
    })
}

export default { add, edit, del, getPage, getProjectCompanyList }