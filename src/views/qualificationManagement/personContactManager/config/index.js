export default {
    // 获取某类型字段列表
    getFields(type) {
        return this.parames.filter(item => {
            return item.whereShow.some((citem) => citem == type)
        })
    },
    
    // 加工页面formData
    getDefaultForm(data) {
        var json = {};
        data.map(item => {
            json[item.fieldName] = item.defaultVal;
        })
        return json;
    },
    
    // 获取表单验证规则
    getValidationRules() {
        const rules = {};
        
        // 姓名必填验证
        rules.name = [
            { required: true, message: '请输入姓名', trigger: 'blur' },
            { min: 1, max: 50, message: '姓名长度在1到50个字符', trigger: 'blur' }
        ];
        
        // 邮箱格式验证（非必填）
        rules.email = [
            { type: 'email', message: '请输入正确的邮箱格式', trigger: ['blur', 'change'] }
        ];
        
        // 手机号格式验证（非必填）
        rules.mobile = [
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: ['blur', 'change'] }
        ];
        
        // 座机号格式验证（非必填，支持区号-号码格式）
        rules.landline = [
            { pattern: /^(\d{3,4}-?)?\d{7,8}$/, message: '请输入正确的座机号格式', trigger: ['blur', 'change'] }
        ];
        
        
        // 备注长度限制
        rules.remark = [
            { max: 500, message: '备注长度不能超过500个字符', trigger: 'blur' }
        ];
        
        // 其他字段长度限制
        ['projectCompanyName', 'roleName', 'positionName'].forEach(field => {
            rules[field] = [
                { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
            ];
        });
        
        return rules;
    },
    
    // 页面请求地址
    requestUrl: '/qualification/personContact',

    // 页面字段参数，可根据实际需求扩展
    parames: [
        {
            label: '主键ID',
            fieldName: 'id',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'edit']
        },
        {
            label: '项目公司',
            fieldName: 'projectCompanyId',
            defaultVal: '',
            type: 'select',
            whereShow: ['tableList', 'form', 'add', 'edit'],
            required: false
        },
        {
            label: '项目公司名称',
            fieldName: 'projectCompanyName',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '角色',
            fieldName: 'roleCode',
            defaultVal: '',
            type: 'dict_select',
            dictCode: 'person_role',
            whereShow: ['tableList', 'form', 'add', 'edit'],
            required: false
        },
        {
            label: '角色名称',
            fieldName: 'roleName',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '姓名',
            fieldName: 'name',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit'],
            required: true
        },
        {
            label: '岗位',
            fieldName: 'positionCode',
            defaultVal: '',
            type: 'dict_select',
            dictCode: 'person_position',
            whereShow: ['tableList', 'form', 'add', 'edit'],
            required: false
        },
        {
            label: '岗位名称',
            fieldName: 'positionName',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '座机',
            fieldName: 'landline',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit'],
            required: false
        },
        {
            label: '手机',
            fieldName: 'mobile',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit'],
            required: false
        },
        {
            label: '邮箱',
            fieldName: 'email',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit'],
            required: false
        },
        {
            label: '传真',
            fieldName: 'fax',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'add', 'edit'],
            required: false
        },
        {
            label: '备注',
            fieldName: 'remark',
            defaultVal: '',
            type: 'textarea',
            whereShow: ['tableList', 'form', 'add', 'edit'],
            required: false
        },
        {
            label: '创建用户姓名',
            fieldName: 'createUserName',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '创建时间',
            fieldName: 'createDate',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '更新用户姓名',
            fieldName: 'updateUserName',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '更新时间',
            fieldName: 'updateDate',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList']
        },
        {
            label: '数据版本',
            fieldName: 'dataVersion',
            defaultVal: '',
            type: 'input',
            whereShow: ['tableList', 'form', 'edit']
        }
    ]
}