<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.name" clearable size="small" placeholder="输入企业名称搜索" style="width: 200px;"
                  class="filter-item" @keyup.enter.native="crud.toQuery"/>
        <el-date-picker
          v-model="query.date"
          type="year"
          size="small"
          class="filter-item"
          value-format="yyyy"
          format="yyyy"
          placeholder="年度"
          disabled
          @change="crud.toQuery"
        />
        <rrOperation/>
      </div>
      <crudOperation :permission="{edit: ['*&%^&^&$%%$^$%'],del: ['*&%^&^&$%%$^$%'],add: ['admin', 'qualificationManagement:enterpriseQualification:add']}">
        <template v-slot:right>
          <el-button type="primary" size="mini" @click="exportExcel"
               v-permission="['admin', 'qualificationManagement:enterpriseQualification:exportExcel']" class="filter-item">导出
          </el-button>
          <!-- 发布 -->
          <el-button v-if="!publishStatus" type="primary" size="mini" @click="publish"
            v-permission="['admin', 'qualificationManagement:enterpriseQualification:publish']" class="filter-item">发布
          </el-button>
          <!-- 取消发布 -->
          <el-button v-else type="warning" size="mini" @click="cancelPublish"
            v-permission="['admin', 'qualificationManagement:enterpriseQualification:publish']" class="filter-item">取消发布
          </el-button>

          <!-- 重新计算 -->
          <el-popover
            ref="popoverName"
            placement="right"
            v-permission="['admin','qualificationManagement:enterpriseQualification:reUpdate']"
            width="200"
            trigger="click">
              <span>
                <el-date-picker
                  :editable="false"
                  :clearable="false"
                  style="width: 100px"
                  v-model="recalculateYear"
                  type="year"
                  placeholder="选择年">
                </el-date-picker>
              </span>
              <span>
                  <el-button style="margin-left: 6px;" type="primary" size="mini"
                            @click="recalculateScore">确认</el-button>
              </span>
              <el-button slot="reference" type="warning" size="mini" class="filter-item">重新计算</el-button>
            </el-popover>
        </template>
      </crudOperation>
    </div>


    <!--表格渲染-->
    <el-table ref="table" v-loading="crud.loading"
              :key="randomKey"
              :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              :data="crud.data"
              row-key="id"
              @select="crud.selectChange" @select-all="crud.selectAllChange"
              @selection-change="crud.selectionChangeHandler"
              :cell-style="cellStyle"
              @sort-change="sortChangeHandler"
              height="470"
              border>
      <template v-for="(item, key) in tableListFields">
        <el-table-column 
        :label="item.label" 
        :prop="item.fieldName" 
        :key="key" 
        align="center" 
        :width="item.size" 
        :sortable="item.fieldName === 'evaluationResult' ? 'custom' : false"
        :fixed="item.fixed"
        >
          <template v-slot="scope">
            <template v-if="item.fieldName == 'shortlistedProperties'">
              {{ showCurrentData(dict.shortlisted_attributes, scope.row[item.fieldName]) }}
            </template>
            <template v-if="item.fieldName == 'evaluationResult'">
              <el-rate
                disabled-void-color="#e1dfdd"
                :max="5.0"
                :value="scope.row['evaluateScore']"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value}">
              </el-rate>
            </template>

            <template v-else>
              {{ scope.row[item.fieldName] }}
            </template>
          </template>
        </el-table-column>
      </template>
      <el-table-column label="操作" width="120px" align="center" fixed="right">
        <template v-slot="scope">
          <el-button type="text" @click="editSorce(scope.row)" v-if="scope.row.children.length > 0">调整分值</el-button>
          <el-button type="text" @click="downloadZip(scope.row)" v-if="!scope.row.ifFather">下载附件</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
<!--    <pagination/>-->
  </div>
</template>

<script>
import crudRequest, {addUpdateScore} from './config/request'
import {updateTotalScore} from '@/views/qualificationManagement/enterpriseQualification/config/request'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import AttachmentsUploadFile from '@/components/AttachmentsUploadFile/index'
import {compareNow} from '@/utils/datetime'
import {downloadFile} from '@/utils/index'
//注入配置文件
import config from './config/index'
import {mapGetters} from "vuex";
import companyScore from '@/views/tools/companyScore'
import moment from "moment";
import request from '@/utils/request'
import {list as listConfig} from '@/views/system/config/config/request'

const formFields = config.getFields('form');
const tableListFields = config.getFields('tableList');
const defaultForm = config.getDefaultForm(formFields);
export default {
  directives: {
    //注册一个局部的自定义指令v-focus
    focus: {
      //指令的定义
      inserted: function (el) {
        //聚焦元素
        el.querySelector('input').focus()
      }
    }
  },
  name: 'simpleList',
  components: {crudOperation, rrOperation, udOperation, pagination, AttachmentsUploadFile, companyScore},
  cruds() {
    return CRUD({
      sort: ['total_score,desc', 'create_date,desc'],
      title: '企业资质',
      url: config.requestUrl,
      urlSuffix: 'list',
      crudMethod: {...crudRequest},
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: true
      },
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 设置数据字典
  dicts: ['shortlisted_attributes', 'unit_status', 'certificate_level', 'territory', 'enterprise_risk'],

  computed: {
    ...mapGetters([
      'user',
    ])
  },
  watch: {},
  mounted() {
    this.getPublish()
  },
  created() {
    listConfig({configLabelLikeQuery:'score_pro_'}).then(res=>{
        this.scoreProMap = new Map();
        res.forEach(resKey=>{
          this.scoreProMap.set(resKey.configLabel,resKey.configValue)
        })
      })
  },
  data() {
    return {
      recalculateYear: new Date(),
      // 发布状态
      publishStatus:false,
      // 咨询服务及时性满分
      serverFullScore: 10,
      // 咨询服务及时性配合度满分
      serverTimelyFullScore: 10,
      // 咨询服务质量满分
      serverQualityFullScore: 10,
      // 处理疑难问题能力满分
      serverRequestFullScore: 10,
      // 其他满分
      serverOtherScore: 5,
      scoreProMap:{},
      query: {
        date: '2024',
        name: ''
      },
      randomKey: Math.random(),
      // 显示编辑框
      showEdit: [],
      formInputStyle: "width: 320px",
      formFields: formFields,
      tableListFields: tableListFields,
      rules: {
        name: [
          {required: true, message: '请输入名称', trigger: 'blur'}
        ]
      },
      permission: {
        add: ['admin', 'qualificationManagement:enterpriseQualification:add'],
        edit: ['admin', 'qualificationManagement:enterpriseQualification:edit'],
        del: ['admin', 'qualificationManagement:enterpriseQualification:del'],
        details: ['admin', 'qualificationManagement:enterpriseQualification:details'],
        addRelation: ['admin', 'qualificationManagement:enterpriseQualification:addRelation'],
      },
    }
  },
  methods: {
    recalculateScore() {
      updateTotalScore({year: moment(this.recalculateYear).year()}).then(res => {
        this.$message.success('操作成功')
        this.$refs['popoverName'].doClose()
        this.crud.refresh()
      });
    },
    getPublish(){
      crudRequest.getPublish(this.query.date).then(res=>{
        this.publishStatus = res
      })
    },
    publish(){
      crudRequest.publish(this.query.date).then(res=>{
        this.$message.success('发布成功')
        this.getPublish()
      })
    },
    cancelPublish(){
      crudRequest.cancelPublish(this.query.date).then(res=>{
        this.$message.success('取消发布成功')
        this.getPublish()
      })
    },
    [CRUD.HOOK.beforeRefresh](crud) {
      crud.query.year = this.query.date;
      crud.query.name = this.query.name;
    },
    computeScore(rowTotalScore){
      let moneyMultiplier = Number(this.scoreProMap.get('score_pro_money_multiplier'));//咨询额满分
        let countMultiplier = Number(this.scoreProMap.get('score_pro_count_multiplier'));//咨询项目数量满分
        let fullScore = this.serverFullScore + this.serverTimelyFullScore + this.serverQualityFullScore + this.serverRequestFullScore + this.serverOtherScore + moneyMultiplier + countMultiplier
        // 计算得分
        let score = ((rowTotalScore / fullScore) * 5).toFixed(1);
        return Number(score);
    },
    refreshTable() {
      this.randomKey = Math.random()
    },
    // 表体字体颜色设置
    cellStyle({row, column, rowIndex, columnIndex}) {
      return "";
    },
    changeSwitch(event, row) {
      if (event) {
        crudRequest.addRelation(row.id)
      } else {
        crudRequest.deleteRelation(row.id)
      }
    },
    exportExcel() {
      let title = "外委单位" + this.crud.query.year + "年终考核表" + ".xlsx"
      crudRequest.downloadScore(this.crud.query).then(res => {
        downloadFile(res, title);
      })
    },
    downloadZip(row) {
      let title = row.orgName + row.orgChildrenName + "其他评分附件" + ".zip"
      crudRequest.checkZip(row.id).then(res => {
        crudRequest.downloadZip({filePath: res}).then(res => {
          downloadFile(res, title);
        })
      })
    },
    editSorce(row) {
      this.$prompt('请输入分值', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^[+-]?(?:\d+\.\d+|\.\d+|\d+)$/,
        inputErrorMessage: '请输入数字'
      }).then(({ value }) => {
        request({url: '/qualification/enterpriseQualificationScore/editScore', method: 'post', data: { enterpriseId: row.enterpriseId, year: this.query.date, score: value }}).then(res => {
          console.log(res)
          this.$message.success('操作成功')
          this.crud.refresh()
        })
      })
    },
    sortChangeHandler(column) {
      if ('evaluationResult' == column.prop) {
        if (column.order == 'descending') {
          this.crud.data.sort((a, b) => {
            return b.totalScore - a.totalScore
          })
          this.crud.data.forEach((item, index) => {
            item.index = index + 1
            if (item.children && item.children.length) {
              item.children.forEach((item, idx) => {
                item.index = index + 1 + '.' + (idx + 1)
              })
            }
          })
        } else if (column.order == 'ascending') {
          this.crud.data.sort((a, b) => {
            return a.totalScore - b.totalScore
          })
          this.crud.data.forEach((item, index) => {
            item.index = index + 1
            if (item.children && item.children.length) {
              item.children.forEach((item, idx) => {
                item.index = index + 1 + '.' + (idx + 1)
              })
            }
          })
        } else {
          this.crud.refresh()
        }
      }
    },
  },
}
</script>
<style>
.el-table .warning-row {
  background: #f80303;
}

.el-table .success-row {
  background: #8b658b;
}

.el-table .black-row {
  background: #0c0c0c;
}
</style>
