import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;

export function getPage(params) {
    return request({
        url: requestUrl+'/page',
        method: 'get',
        params
    })
}

export function getUnitList() {
  return request({
    url: requestUrl+'/list',
    method: 'get',
  })
}

export function add(data) {
    return request({
        url: requestUrl,
        method: 'post',
        data
    })
}

export function del(ids) {
    return request({
        url: requestUrl,
        method: 'delete',
        data: ids
    })
}

export function edit(data) {
    return request({
        url: requestUrl,
        method: 'put',
        data
    })
}

export function getEnterpriseQualificationAccessory(data) {
    return request({
        url: '/qualification/enterpriseQualification/getEnterpriseQualificationAccessory/'+data.id,
        method: 'get',
        data
    })
}

export function addRelation(id) {
  return request({
    url: requestUrl+'/addRelation/'+id,
    method: 'put',
  })
}

export function deleteRelation(id) {
  return request({
    url: requestUrl+'/deleteRelation/'+id,
    method: 'put',
  })
}


export function addUpdateScore(data) {
  return request({
    url: '/qualification/enterpriseQualificationScore/addUpdate',
    method: 'post',
    data
  })
}

export function getScoreByParams(params) {
  return request({
    url: '/qualification/enterpriseQualificationScore/scoreByParams',
    method: 'get',
    params
  })
}

export function downloadScore(params) {
  return request({
    url: '/qualification/enterpriseQualificationScore/downloadScore',
    method: 'get',
    responseType: 'blob',
    params
  })
}

export function downloadZip(params) {
  return request({
    url: '/qualification/enterpriseQualificationScore/downloadZip',
    method: 'get',
    responseType: 'blob',
    params
  })
}

export function checkZip(id) {
  return request({
    url: '/qualification/enterpriseQualificationScore/checkOther/'+id,
    method: 'get',
  })
}

export function publish(params) {
  return request({
    url: '/qualification/enterpriseQualificationScore/publish/',
    method: 'put',
    params:{
      year:params
    }
  })
}

export function cancelPublish(params) {
  return request({
    url: '/qualification/enterpriseQualificationScore/cancelPublish',
    method: 'put',
    params:{
      year:params
    }
  })
}

export function getPublish(params) {
  return request({
    url: '/qualification/enterpriseQualificationScore/getPublish',
    method: 'get',
    params:{
      year:params
    }
  })
}
export default { add, edit, del, getPage, getEnterpriseQualificationAccessory,deleteRelation,addRelation,addUpdateScore,getScoreByParams,downloadScore,downloadZip,checkZip,publish,getPublish,cancelPublish}
