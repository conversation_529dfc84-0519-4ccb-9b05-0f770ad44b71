export default {
  //获取某类型字段列表
  getFields(type) {
    return this.parames.filter(item => {
      return item.whereShow.some((citem) => citem == type)
    })
  },
  //加工页面formData
  getDefaultForm(data) {
    var json = {};
    data.map(item => {
      json[item.fieldName] = item.defaultVal;
    })
    return json;
  },
  //页面请求地址
  requestUrl: '/qualification/enterpriseQualificationScore',

  //页面字段参数，可根据实际需求扩展
  parames: [
    {
      label: '序号',
      fieldName: 'index',
      defaultVal: '',
      size:100,
      type: 'input',
      fixed: 'left',
      whereShow: ['tableList']
    },
    {
      label: '咨询单位名称',
      fieldName: 'name',
      defaultVal: '',
      size:250,
      type: 'input',
      fixed: 'left',
      whereShow: ['tableList']
    },
    {
      label: '机关',
      fieldName: 'orgName',
      defaultVal: '',
      size:120,
      type: 'short',
      whereShow: ['tableList']
    },
    {
      label: '下属单位',
      fieldName: 'orgChildrenName',
      defaultVal: '',
      size:120,
      type: 'input',
      whereShow: ['tableList']
    },
	  {
		  label: '项目公司',
		  fieldName: 'orgChildrenChildrenName',
		  defaultVal: '',
		  size:120,
		  type: 'input',
		  whereShow: ['tableList']
	  },
    {
      label: '评价结果',
      fieldName: 'evaluationResult',
      defaultVal: '',
      size:190,
      type: 'rate',
      whereShow: ['tableList']
    },
    {
      label: '得分',
      fieldName: 'totalScore',
      defaultVal: '',
      edit:true,
      size:120,
      type: 'totalScore',
      whereShow: ['tableList']
    },
    {
      label: '咨询项目数量',
      fieldName: 'projectCount',
      defaultVal: '',
      size:120,
      type: 'short',
      whereShow: ['tableList']
    },
    {
      label: '咨询审定总金额(万元)',
      fieldName: 'projectAmount',
      defaultVal: '',
      size:150,
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '咨询服务及时性',
      fieldName: 'serverScore',
      defaultVal: '',
      size:120,
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '咨询服务配合度',
      fieldName: 'serverTimelyScore',
      defaultVal: '',
      size:120,
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '咨询服务质量',
      fieldName: 'serverQualityScore',
      defaultVal: '',
      size:120,
      type: 'date',
      whereShow: ['tableList']
    },
    {
      label: '处理疑难问题能力',
      fieldName: 'serverRequestScore',
      defaultVal: '',
      size:120,
      type: 'effectiveStartDate',
      whereShow: ['tableList']
    },
    {
      label: '其他',
      fieldName: 'serverOtherScore',
      defaultVal: '',
      size:120,
      type: 'date',
      whereShow: ['tableList']
    },
    {
      label: '备注',
      fieldName: 'remark',
      defaultVal: '',
      size:200,
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '编辑人',
      fieldName: 'updateUserName',
      size:90,
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '时间',
      fieldName: 'updateDate',
      size:90,
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },
  ]
}
