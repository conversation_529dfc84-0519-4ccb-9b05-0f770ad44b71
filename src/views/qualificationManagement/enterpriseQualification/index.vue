<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <el-input v-model="query.name" clearable size="small" placeholder="输入咨询单位名称搜索" style="width: 200px;"
                  class="filter-item" @keyup.enter.native="crud.toQuery"/>
        <!-- 所属地 -->
        <el-select v-model="query.placeOrigin" placeholder="所属地" class="filter-item"
                  @change="crud.toQuery" clearable>
          <el-option
            v-for="(item) in dict.territory"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
        <!-- 企业状态 -->
        <el-select v-model="query.statusCd" class="filter-item" @change="crud.toQuery" placeholder="状态" clearable>
          <el-option
            v-for="(item) in dict.unit_status"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
        <!-- 评分区间查询-->
        <input-number-range
          :min="0"
          :max="5"
          style="width: 200px;"
          class="filter-item"
          :disabled="false"
          :precision="1"
          v-model="query.rangeScore"
          placeForm="考核评价最小值"
          placeTo="考核评价最大值"
        >
      </input-number-range>
        <rrOperation/>
      </div>

      <crudOperation :permission="{
                    edit: ['*&%^&^&$%%$^$%'],
                    del: ['*&%^&^&$%%$^$%'],
                    add: ['admin', 'qualificationManagement:enterpriseQualification:add']
                }">
        <template v-slot:right>
          <el-upload
            v-permission="['admin','qualificationManagement:enterpriseQualification:upload']"
            action=""
            :http-request="importUnitHttpRequest"
            :show-file-list="false"
            class="filter-item"
          >
            <el-button type="primary" size="mini">导入单位</el-button>
          </el-upload>

          <el-upload
            v-permission="['admin','qualificationManagement:enterpriseQualification:upload']"
            action=""
            :http-request="updateUnitStatusHttpRequest"
            :show-file-list="false"
            class="filter-item"
          >
            <el-button type="primary" size="mini">更新单位状态</el-button>
          </el-upload>
          <el-button type="primary" class="filter-item" size="mini" @click="showFiledDialog">导出资质</el-button>
        </template>
      </crudOperation>

    </div>
    <!--表单组件-->
    <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU"
               :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="1140px">
      <el-tabs v-model="proActiveName" type="card">
        <el-tab-pane label="项目信息" name="0">
          <el-form :disabled="crud.status.details > 0" ref="form" :model="form" :inline="true" :rules="rules"
                   size="small" label-width="190px">
            <template v-for="(item, key) in formFields">
              <template v-if="item.type == 'radio'">
                <el-form-item label="状态" :prop="item.fieldName" :key="key">
                  <el-radio v-for="item in dict.dept_status" :key="item.id" v-model="form.enabled" :label="item.value">
                    {{ item.label }}
                  </el-radio>
                </el-form-item>
              </template>
              <template v-if="item.type == 'input'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-input v-model="form[item.fieldName]" :style="formInputStyle"/>
                </el-form-item>
              </template>
              <template v-if="item.type == 'date'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-date-picker
                    v-model="form[item.fieldName]"
                    value-format="yyyy-MM-dd"
                    type="date"
                    :style="formInputStyle"
                    placeholder="选择日期时间">
                  </el-date-picker>
                </el-form-item>
              </template>
              <template v-if="item.type == 'effectiveStartDate'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-date-picker
                    v-model="form[item.fieldName]"
                    value-format="yyyy-MM-dd"
                    type="date"
                    style="width:142px"
                    placeholder="选择开始日期">
                  </el-date-picker>
                </el-form-item>
              </template>

              <template v-if="item.type === 'input-number'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-input-number v-model="form[item.fieldName]" :style="formInputStyle" :disabled="item.disabled"
                                   :min=2000 :max=2099 :controls="false"/>
                </el-form-item>
              </template>
              <template v-if="item.type == 'effectiveEndDate'">
                <el-form-item style="margin-left: 23px" label-width="23px" :prop="item.fieldName" :key="key">
                  <el-date-picker
                    v-model="form[item.fieldName]"
                    value-format="yyyy-MM-dd"
                    type="date"
                    style="width:142px"
                    placeholder="选择结束日期">
                  </el-date-picker>
                </el-form-item>
              </template>

              <template v-if="item.type == 'short' && item.fieldName != 'shortlistedCode'">
                <el-form-item :label="item.label" label-width="90px" :prop="item.fieldName" :key="key">
                  <el-input v-model="form[item.fieldName]" style="width:100px"/>
                </el-form-item>
              </template>

              <template v-if="item.fieldName == 'shortlistedCode' && user.orgTypeCd == 1">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-input v-model="form[item.fieldName]" :style="formInputStyle"/>
                </el-form-item>
              </template>

              <template v-if="item.type == 'select' && item.fieldName == 'certificateLevelCd'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-select v-model="form[item.fieldName]" style="width:100px" placeholder="请选择">
                    <el-option
                      v-for="(item) in dict.certificate_level"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </template>

              <template v-if="item.type == 'select' && item.fieldName == 'placeOrigin'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-select v-model="form[item.fieldName]" :style="formInputStyle" placeholder="请选择" filterable>
                    <el-option
                      v-for="(item) in dict.territory"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </template>

              <template v-if="item.type == 'select' && item.fieldName == 'enterprisesInvolveLegalRisks'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-select v-model="form[item.fieldName]" :style="formInputStyle" placeholder="请选择">
                    <el-option
                      v-for="(item) in dict.enterprise_risk"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </template>

              <template
                v-if="item.type == 'select' && item.fieldName == 'shortlistedProperties' && user.orgTypeCd == 1">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-select v-model="form[item.fieldName]" style="width:100px" placeholder="请选择">
                    <el-option
                      v-for="(item) in dict.shortlisted_attributes"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </template>
              <template v-if="item.type == 'select' && item.fieldName == 'statusCd'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-select v-model="form[item.fieldName]" :style="formInputStyle" placeholder="请选择">
                    <el-option
                      v-for="(item) in dict.unit_status"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    />
                  </el-select>
                </el-form-item>
              </template>

              <template v-if="item.fieldName == 'involveLawsuit'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-input type="textarea" v-model="form[item.fieldName]" :style="formInputStyle"/>
                </el-form-item>
              </template>

              <template v-if="item.fieldName == 'enterpriseProfile'">
                <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
                  <el-input type="textarea" :rows="4" v-model="form[item.fieldName]" style="width: 840px"/>
                </el-form-item>
              </template>
            </template>
            <el-divider>岗位证书</el-divider>
            <uploadImage
              :file-list="form.imgList"
              :remove-file="removeFile">
            </uploadImage>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="附件" name="1">
          <AttachmentsUploadFile
            :uploadFileData="crud.form.accessories"
            :isHiddenColumn="true"
            :fileChage="uploadList"
            ref="upload"
          />
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>

    <span class="annotationFont annotation" style="margin-top: 12px;right: 60px;">有诉讼</span>
    <div class="annotationIcon annotation" style="margin-top: 13px;background-color:#f80303;right: 100px;"></div>

    <!--表格渲染-->
    <el-tabs type="border-card">
      <el-tab-pane label="单位资质信息">
        <el-table ref="table" v-loading="crud.loading"
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                  default-expand-all
                  :data="normal.data.records"
                  row-key="id"
                  @select="crud.selectChange" @select-all="crud.selectAllChange"
                  @selection-change="crud.selectionChangeHandler"
                  :cell-style="cellStyle"
                  border
                  @sort-change="sortChangeHandler"
        >
          <template v-for="(item, key) in tableListFields">
            <el-table-column :label="item.label" :prop="item.fieldName" :key="key" :width="item.width" :fixed="item.fixed"
                             show-overflow-tooltip :sortable="item.fieldName === 'evaluationResult' ? 'custom' : false">
              <template slot-scope="scope">
                <template v-if="item.fieldName == 'evaluationResult'">
                  <el-rate
                    disabled-void-color="#e1dfdd"
                    :max="5.0"
                    :value="scope.row['score']"
                    disabled
                    show-score
                    text-color="#ff9900"
                    score-template="{value}">
                  </el-rate>
                </template>

                <template v-if="item.fieldName == 'shortlistedProperties'">
                  {{ showCurrentData(dict.shortlisted_attributes, scope.row[item.fieldName]) }}
                </template>
                <template v-else-if="item.fieldName == 'certificateLevelCd'">
                  {{ showCurrentData(dict.certificate_level, scope.row[item.fieldName]) }}
                </template>
                <template v-else-if="item.fieldName == 'enterprisesInvolveLegalRisks'">
                  {{ showCurrentData(dict.enterprise_risk, scope.row[item.fieldName]) }}
                </template>
                <template v-else-if="item.fieldName == 'statusCd'">
                  {{ showCurrentData(dict.unit_status, scope.row[item.fieldName]) }}
                </template>
                <template v-else-if="item.fieldName == 'name'">
                  <!-- <el-tag v-if="scope.row['involveLawsuit'] !== ''&&scope.row['involveLawsuit'] != null" size="mini"
                          hit>有诉讼
                  </el-tag> -->
                  <!-- <br  v-if="scope.row['effectiveEndDate'] != null && isCompareNow(scope.row['effectiveEndDate']) || (scope.row['involveLawsuit'] !== '' && scope.row['involveLawsuit'] != null)"/> -->
                  <el-button type="text" size="small" @click="crud.toDetails(scope.row)">
                    {{ scope.row.name }}
                  </el-button>
                </template>
                <template
                  v-else-if="item.type == 'date' || item.type == 'effectiveStartDate' || item.type == 'effectiveEndDate'">
                  {{ dateFormatVal(scope.row[item.fieldName], "YYYY-MM-DD") }}
                </template>
                <template v-else>
                  {{ scope.row[item.fieldName] }}
                </template>
              </template>
            </el-table-column>
          </template>

          <el-table-column label="常用企业资质" width="100px" align="center" fixed="right">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.ifRelation"
                @change=changeSwitch($event,scope.row)
              >
              </el-switch>
            </template>
          </el-table-column>

          <el-table-column
            v-permission="['admin','qualificationManagement:enterpriseQualification:edit','qualificationManagement:enterpriseQualification:del','qualificationManagement:enterpriseQualification:details']"
            label="操作" width="220px" align="center" fixed="right">
            <template slot-scope="scope">
              <div style="display:inline-block;">
                <udOperation
                  :data="{...scope.row}"
                  :permission="permission"
                  :disabledDle="scope.row.createUserName !==user.nickname && !user.ifOrgAdmin"
                  :disabledEdit="scope.row.createUserName !==user.nickname && !user.ifOrgAdmin"
                  msg="确定删除吗,如果存在下级节点则一并删除，此操作不能撤销！"
                />
              </div>
              <div style="display:inline-block;">
                <el-tooltip class="item" effect="dark" content="评分" placement="top-start">
                  <!--                             v-permission="['admin','qualificationManagement:enterpriseQualification:score']"-->
                  <el-button type="danger"
                              :disabled="publishStatus"
                             size="mini" icon="el-icon-warning" @click="resetScore(scope.row)"></el-button>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <el-pagination
          :page-size.sync="normal.data.size"
          :total="normal.data.total"
          :current-page.sync="current"
          style="margin-top: 8px;"
          layout="total, prev, pager, next, sizes"
          @size-change="normalSizeChangeHandler($event)"
          @current-change="normalPageChangeHandler"
        />
      </el-tab-pane>
      <!--表格渲染-->

    </el-tabs>
    <el-dialog
      title="评分"
      :visible.sync="scoreDialogVisible"
      width="1000px">
      <companyScore
        :scoreEntity="scoreEntity"
        :commitData="commitData"
        :changeYear="changeYear">
      </companyScore>
    </el-dialog>

    <el-dialog
      title="请选择导出列:"
      :visible.sync="fieldCheckDialog"
      width="800px">
      <el-checkbox-group v-model="checkedField">
        <el-checkbox v-for="(field,index) in allFiled" :label="field" :key="index">
          {{ field.label }}
        </el-checkbox>
      </el-checkbox-group>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="fieldCheckDialog=false">取消</el-button>
        <el-button type="primary" @click="downloadExcel">确认</el-button>
      </div>
    </el-dialog>

    <!-- 更新完成的单位名单 -->
    <el-dialog
      :visible.sync="updateCompletedDialog"
      :show-close="false"
      :modal-append-to-body="false"
      width="500px"
      custom-class="update-unit-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="update-result-container">
        <div class="update-result-header">
          <i class="el-icon-success"></i>
          <span>单位状态更新完成</span>
          <el-button
            type="text"
            icon="el-icon-close"
            class="close-btn"
            @click="updateCompletedDialog = false">
          </el-button>
        </div>
        <div class="update-result-content">
          <div class="update-title">更新完成的单位名单</div>
          <div class="names-container">
            <!-- 动态插入更新完成的单位名单 -->
          </div>
        </div>
      </div>
    </el-dialog>



    <p style="    text-align: center;color: rgb(153, 153, 153);font-size: 12px;position: absolute;bottom: 40px;right: 0;">注：以上查询结果仅供参考，最终造价咨询服务商名单以“中国石油工程建设管理平台”为准。 </p>
  </div>

</template>

<script>
import crudRequest from './config/request'
import {getPublish} from '@/views/qualificationManagement/companyQualificationReport/config/request'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import AttachmentsUploadFile from '@/components/AttachmentsUploadFile/index'
import {compareNow} from '@/utils/datetime'
import {downloadFile} from "@/utils";
import uploadImage from '@/components/AttachmentsUploadFile/uploadImage'
//注入配置文件
import config from './config/index'
import {mapGetters} from "vuex";
import companyScore from '@/views/tools/companyScore'
import moment from "moment";
import {upload} from '@/utils/upload'

import InputNumberRange from '@/components/InputNumberRange/index.vue'

const formFields = config.getFields('form');
const tableListFields = config.getFields('tableList');
const defaultForm = config.getDefaultForm(formFields);
export default {
  name: 'simpleList',
  components: {crudOperation, rrOperation, udOperation, pagination, AttachmentsUploadFile, companyScore, uploadImage, InputNumberRange},
  cruds() {
    return CRUD({
      title: '企业资质',
      url: config.requestUrl,
      crudMethod: {...crudRequest},
      sort: ['create_date,asc']
  })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 设置数据字典
  dicts: ['shortlisted_attributes', 'unit_status', 'certificate_level', 'territory', 'enterprise_risk'],

  computed: {
    ...mapGetters([
      'user',
    ])
  },
  data() {
    return {
      publishStatus: false,
      normal: {
        size: 10,
        data: {},
      },
      other: {
        size: 10,
        data: {},
      },
      current: 1,
      sort: 'a.update_date,desc',
      proActiveName: '0',
      formInputStyle: "width: 320px",
      formFields: formFields,
      tableListFields: tableListFields,
      rules: {
        name: [{required: true, message: '咨询单位名称', trigger: 'blur'}],
        statusCd: [{required: true, message: '单位状态', trigger: 'blur'}],
      },
      permission: {
        add: ['admin', 'qualificationManagement:enterpriseQualification:add'],
        edit: ['admin', 'qualificationManagement:enterpriseQualification:edit'],
        del: ['admin', 'qualificationManagement:enterpriseQualification:del'],
        details: ['admin', 'qualificationManagement:enterpriseQualification:details'],
        addRelation: ['admin', 'qualificationManagement:enterpriseQualification:addRelation'],
      },
      enabledTypeOptions: [
        {key: 'true', display_name: '正常'},
        {key: 'false', display_name: '禁用'}
      ],
      scoreDialogVisible: false,
      row: {},
      scoreEntity: {
        serverOtherScore: 0,
        serverRequestScore: 0,
        serverQualityScore: 0,
        serverTimelyScore: 0,
        serverScore: 0,
        projectAmountScore: 0,
        projectCountScore: 0
      },
      fieldCheckDialog: false,
      checkedField: [],
      allFiled: config.parames,
      updateCompletedDialog: false,
    }
  },
  mounted() {
    this.getNormalPage();
    getPublish("2024").then(res=>{
      this.publishStatus = res
    })
  },
  methods: {
    removeFile(file) {
      this.form.imgList = this.form.imgList.filter(p => p.id !== file.id)
    },
    dateFormatVal(val, formatValue) {
      if (val !== null) {
        return moment(val).format(formatValue)
      }
    },
    [CRUD.HOOK.beforeValidateCU]() {
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          this.proActiveName = "0"
        }
      })
    },
    [CRUD.HOOK.beforeToAdd](crud) {
      this.crud.form.accessories = []
      this.crud.form.imgList = []
    },
    //编辑
    [CRUD.HOOK.beforeToEdit](crud) {
      crud.form.statusCd = crud.form.statusCd != null?crud.form.statusCd.toString():'';
      crud.form.enterprisesInvolveLegalRisks = crud.form.enterprisesInvolveLegalRisks != null?crud.form.enterprisesInvolveLegalRisks.toString():'';
      setTimeout(() => {
        crudRequest.getEnterpriseQualificationAccessory({
          id: this.form.id
        })
          .then(res => {
            this.$refs.upload.clearFiles();
            this.$refs.upload.setDefaultFiles(res.map(item => ({
              ...item,
              affiliationCd: 'test',
              directoriesCd: 'test'
            })))
          })
      }, 0)
    },

    [CRUD.HOOK.beforeRefresh]() {
      this.getNormalPage();
      this.hideFromCertainUnits();
      return false;
    },
    sortChangeHandler(column) {
      if ('evaluationResult' == column.prop) {
        if (column.order == 'descending') {
          this.sort = `b.rating,desc`
        }else if (column.order == 'ascending') {
          this.sort = `b.rating,asc`
        }else{
          this.sort = `a.update_date,desc`
        }
      }
      this.getNormalPage();
    },
    //二级单位和项目公司隐藏入围
    hideFromCertainUnits() {
      if (this.user.orgTypeCd != 1) {
        this.tableListFields = this.tableListFields.filter(function (key) {
          return !(['shortlistedCode', 'shortlistedProperties'].includes(key.fieldName))
        })
      }
    },
    getOtherPage(page) {
      let data = {
        current: page ? this.current : page,
        size: this.otherSize,
        sort: this.sort,
        statusCdIn: '3,4'
      }
      crudRequest.getPage(data)
        .then(res => {
          res.records.map(item => {
            this.crud.crudoOperationData[item.id] = {
              delete: 0,
              edit: 0
            }
          })
          this.other.data = res;
        })
    },
    getQueryData() {
      let data = {
        current: this.current,
        size: this.normal.size,
        sort: this.sort,
        name: this.query.name,
        placeOrigin: this.query.placeOrigin,
        statusCd: this.query.statusCd,
        rangeScore: this.query.rangeScore
      }
      return data;
    },
    getNormalPage(page) {
      if (this.query.rangeScore && this.query.rangeScore.length > 0) {
        if (!this.query.rangeScore[0] && !this.query.rangeScore[1]) {
          this.query.rangeScore = null;
        } else {
          this.query.rangeScore[0] = this.query.rangeScore[0] || 0;
          this.query.rangeScore[1] = this.query.rangeScore[1] || 999;
        }
      }
      let data = this.getQueryData();
      crudRequest.getPage(data)
        .then(res => {
          res.records.map(item => {
            this.crud.crudoOperationData[item.id] = {
              delete: 0,
              edit: 0
            }
          })
          this.normal.data = res;
        })
    },
    //上传图片change
    uploadList(current, list) {
      this.form.accessories = list.map(item => {
        return {
          name: item.name,
          uri: item.uri
        }
      });
    },
    otherSizeChangeHandler(ev) {
      this.other.size = ev;
      this.getOtherData();
    },
    otherPageChangeHandler(page) {
      this.getOtherPage(page);
    },
    normalSizeChangeHandler(ev) {
      this.normal.size = ev;
      this.getNormalPage();
    },
    normalPageChangeHandler(page) {
      this.current = page;
      this.getNormalPage(page);
    },
    checkboxT(row, rowIndex) {
      return row.id !== 1
    },
    tableRowClassName({row, rowIndex}) {
      if (row.statusCd === 4) {
        return 'black-row';
      }
      return '';
    },
    isCompareNow(date) {
      return !compareNow(date);
    },
    // 表体字体颜色设置
    cellStyle({row, column, rowIndex, columnIndex}) {
      // 状态列字体颜色
      // if (row.statusCd === 4) {
      //   return 'color: #f80303'
      // }

      //有诉讼是红色
      if (row.involveLawsuit != '' && row.involveLawsuit != null) {
        return 'color: #f80303'
      }

      // //过期是紫色
      // if (row.effectiveEndDate!=null && !compareNow(row.effectiveEndDate)) {
      //   return 'color: #AB1ABF'
      // }

    },
    changeSwitch(event, row) {
      if (event) {
        crudRequest.addRelation(row.id)
      } else {
        crudRequest.deleteRelation(row.id)
      }
    },
    //获取报表信息
    resetScore(row) {
      this.row = row
      crudRequest.getScoreByParams({
        enterpriseId: row.id,
        year: row.year ? row.year : 2024
      }).then(res => {
        this.scoreDialogVisible = true;
        this.scoreEntity = res;
        this.scoreEntity.row = row;
      })
    },
    commitData(data) {
      crudRequest.addUpdateScore(data).then(res => {
        this.scoreDialogVisible = false;
        this.crud.notify('操作成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
      })
    },
    changeYear(row) {
      crudRequest.getScoreByParams({
        enterpriseId: row.id,
        year: row.year
      }).then(res => {
        this.scoreEntity = res;
        this.scoreEntity.year = row.year;
        this.scoreEntity.row = this.row;
      })
    },
    showFiledDialog() {
      this.fieldCheckDialog = true;
      this.checkedField = this.tableListFields
    },
    downloadExcel() {
      let queryData = this.getQueryData();
      crudRequest.downLoadByFiled(this.checkedField, queryData).then(res => {
        downloadFile(res, "企业资质.xlsx")
        this.fieldCheckDialog = false;
      })
    },
    importUnitHttpRequest(file) {
      const loading = this.$loading({
        lock: true,
        text: '正在上传，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      upload('qualification/enterpriseQualification/importEnterprise', file.file)
        .then(res => {
          loading.close();
          if (res.data.status === 200) {
            this.$message.success('导入成功！');
            this.getNormalPage();
          } else {
            this.$message.error('导入失败！请稍后重试！');
          }
        })
        .catch(e => {
          loading.close();
          this.$message.error('上传失败！请稍后重试！');
        })
    },
    updateUnitStatusHttpRequest(file) {
      const loading = this.$loading({
        lock: true,
        text: '正在上传，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      upload('qualification/enterpriseQualification/importUpdateEnterpriseStatus', file.file)
        .then(res => {
          loading.close();
          if (res.data.status === 200) {
            this.showUnmatchedNames(res.data.data);
            this.getNormalPage();
          } else {
            this.$message.error('企业状态更新失败，请稍后重试！');
          }
        })
        .catch(e => {
          loading.close();
          this.$message.error('上传失败！请稍后重试！');
        })
    },
    showUnmatchedNames(names) {
      if (names && Object.keys(names).length > 0) {
        this.updateCompletedDialog = true;
        let unit_status = this.dict['unit_status'];
        this.$nextTick(() => {
          const container = document.querySelector('.names-container');
          if (container) {
            container.innerHTML = names
              .map((data) => {
                const statusText = this.showCurrentData(unit_status, data.status);
                const statusClass = data.status == 1 ? 'status-normal' : 'status-warning';
                return `<div class="name-item">
                          <span class="name">${data.name}</span>
                          <span class="status ${statusClass}">${statusText}</span>
                        </div>`;
              })
              .join('');
          } else {
            console.error('.names-container 未找到');
          }
        });
      }
    }
  },

}
</script>
<style lang="scss">
.annotationFont {
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  font-size: 12px;
}

.annotationIcon {
  width: 9px;
  height: 9px;
  border-radius: 50%;
}

.annotation {
  position: absolute;
  z-index: 8;

}

.el-table .warning-row {
  background: #f80303;
}

.el-table .success-row {
  background: #8b658b;
}

.el-table .black-row {
  background: #0c0c0c;
}

.downloadButton {
  position: absolute;
  margin-top: -42px;
  margin-left: 172px;
}

// 自定义对话框样式
:deep(.update-unit-dialog) {
  .el-dialog__header {
    display: none; // 隐藏对话框头部
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__headerbtn {
    display: none;
  }
}

.update-result-container {
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  overflow: hidden;

  .update-result-header {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e6ebf5;
    position: relative;

    i {
      font-size: 22px;
      color: #67C23A;
      margin-right: 10px;
    }

    span {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      flex: 1;
    }

    .close-btn {
      position: absolute;
      right: 15px;
      top: 15px;
      font-size: 16px;
      color: #909399;

      &:hover {
        color: #409EFF;
      }
    }
  }

  .update-result-content {
    padding: 20px;

    .update-title {
      font-size: 14px;
      font-weight: 500;
      color: #606266;
      margin-bottom: 15px;
    }

    .names-container {
      max-height: 300px;
      overflow-y: auto;
      background-color: #f9f9f9;
      border-radius: 4px;
      padding: 5px;

      .name-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        margin: 5px 0;
        background-color: #fff;
        border-radius: 4px;
        border-left: 3px solid #409EFF;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        font-size: 14px;
        color: #606266;
        transition: all 0.3s;

        &:hover {
          transform: translateX(2px);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        .name {
          font-weight: 500;
          flex: 1;
        }

        .status {
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;

          &.status-normal {
            background-color: rgba(103, 194, 58, 0.1);
            color: #67C23A;
          }

          &.status-warning {
            background-color: rgba(245, 108, 108, 0.1);
            color: #F56C6C;
          }
        }
      }
    }
  }

  .update-result-footer {
    padding: 10px 20px 20px;
    text-align: right;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
