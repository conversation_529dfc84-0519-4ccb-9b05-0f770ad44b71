export default {
  //获取某类型字段列表
  getFields(type) {
    return this.parames.filter(item => {
      return item.whereShow.some((citem) => citem == type)
    })
  },
  //加工页面formData
  getDefaultForm(data) {
    var json = {};
    data.map(item => {
      json[item.fieldName] = item.defaultVal;
    })
    return json;
  },
  //页面请求地址
  requestUrl: '/qualification/enterpriseQualification',

  //页面字段参数，可根据实际需求扩展
  parames: [
    {
      label: '序号',
      fieldName: 'pageIndex',
      defaultVal: '',
      index: 0,
      width: 60,
      fixed: true,
      whereShow: ['tableList']
    },
    {
      label: '咨询单位名称',
      fieldName: 'name',
      defaultVal: '',
      type: 'input',
      index: 1,
      width: 200,
      fixed: true,
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '2024年评价结果',
      fieldName: 'evaluationResult',
      defaultVal: '',
      index: 2,
      size: 190,
      width: 200,
      type: 'rate',
      whereShow: ['tableList']
    },
    {
      label: '单位状态',
      fieldName: 'statusCd',
      defaultVal: '',
      index: 3,
      width: 100,
      type: 'select',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '企业资质等级',
      fieldName: 'certificateLevelCd',
      defaultVal: '',
      type: 'select',
      index: 21,
      width: 200,
      fixed: true,
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '证书编号',
      fieldName: 'certificateCode',
      defaultVal: '',
      index: 20,
      width: 200,
      type: 'short',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '曾用名',
      fieldName: 'oldName',
      defaultVal: '',
      index: 4,
      width: 200,
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '入围编号',
      fieldName: 'shortlistedCode',
      defaultVal: '',
      index: 19,
      width: 200,
      type: 'short',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '企业地址',
      fieldName: 'address',
      defaultVal: '',
      index: 5,
      width: 200,
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '注册资本（万元）',
      fieldName: 'registeredCapital',
      defaultVal: '',
      index: 6,
      width: 200,
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '企业统一社会信用代码',
      fieldName: 'enterpriseUnifiedSocialCreditCode',
      defaultVal: '',
      index: 7,
      width: 200,
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '企业成立日期',
      fieldName: 'establishmentDate',
      defaultVal: '',
      index: 8,
      width: 200,
      type: 'date',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '资质有效开始日期',
      fieldName: 'effectiveStartDate',
      defaultVal: '',
      index: 9,
      width: 200,
      type: 'effectiveStartDate',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '资质有效结束日期',
      fieldName: 'effectiveEndDate',
      defaultVal: '',
      index: 10,
      width: 200,
      type: 'effectiveEndDate',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '入围时间',
      fieldName: 'inclusionDate',
      defaultVal: '',
      index: 11,
      width: 200,
      type: 'date',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '联系人名字',
      fieldName: 'contactName',
      defaultVal: '',
      index: 12,
      width: 200,
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '联系电话',
      fieldName: 'contactTel',
      defaultVal: '',
      index: 13,
      width: 200,
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },

    {
      label: '法人代表',
      fieldName: 'legalRepresentative',
      defaultVal: '',
      index: 14,
      width: 200,
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '所属地',
      fieldName: 'placeOrigin',
      defaultVal: '',
      index: 15,
      width: 200,
      type: 'select',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '企业涉法风险',
      fieldName: 'enterprisesInvolveLegalRisks',
      defaultVal: '',
      index: 16,
      width: 200,
      type: 'select',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '涉及法律诉讼',
      fieldName: 'involveLawsuit',
      defaultVal: '',
      index: 17,
      width: 200,
      type: 'textarea',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '企业简介',
      fieldName: 'enterpriseProfile',
      defaultVal: '',
      index: 18,
      width: 200,
      type: 'textarea',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },


  ]
}
