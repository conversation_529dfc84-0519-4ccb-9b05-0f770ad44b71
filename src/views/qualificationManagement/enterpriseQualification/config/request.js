import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;
import qs from 'qs'

export function getPage(params) {
    return request({
        url: requestUrl+'/page' + "?" + qs.stringify(params, { indices: false }),
        method: 'get'
    })
}

export function getUnitDict() {
  return request({
    url: requestUrl+'/getUnitDict',
    method: 'get',
  })
}

export function getUnitList() {
  return request({
    url: requestUrl+'/list',
    method: 'get',
  })
}

export function add(data) {
    return request({
        url: requestUrl,
        method: 'post',
        data
    })
}

export function del(ids) {
    return request({
        url: requestUrl,
        method: 'delete',
        data: ids
    })
}

export function edit(data) {
    return request({
        url: requestUrl,
        method: 'put',
        data
    })
}

export function getEnterpriseQualificationAccessory(data) {
    return request({
        url: '/qualification/enterpriseQualification/getEnterpriseQualificationAccessory/'+data.id,
        method: 'get',
        data
    })
}

export function addRelation(id) {
  return request({
    url: requestUrl+'/addRelation/'+id,
    method: 'put',
  })
}

export function deleteRelation(id) {
  return request({
    url: requestUrl+'/deleteRelation/'+id,
    method: 'put',
  })
}


export function addUpdateScore(data) {
  return request({
    url: '/qualification/enterpriseQualificationScore/addUpdate',
    method: 'post',
    data
  })
}

export function getScoreByParams(params) {
  return request({
    url: '/qualification/enterpriseQualificationScore/scoreByParams',
    method: 'get',
    params
  })
}

export function updateTotalScore(params) {
    return request({
        url: requestUrl + '/updateTotalScore',
        method: 'get',
        params
    })
}


export function downLoadByFiled(data, params) {
  return request({
    url: requestUrl + '/downLoadByFiled',
    method: 'post',
    data,
    params,
    responseType: 'blob'
  })
}

export default { getUnitList,getUnitDict, add, edit, del, getPage, getEnterpriseQualificationAccessory,deleteRelation,addRelation,addUpdateScore,getScoreByParams, updateTotalScore,downLoadByFiled}
