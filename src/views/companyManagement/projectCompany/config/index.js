export default {
  //获取某类型字段列表
  getFields(type) {
    return this.parames.filter(item => {
      return item.whereShow.some((citem) => citem == type)
    })
  },
  //加工页面formData
  getDefaultForm(data) {
    var json = {};
    data.map(item => {
      json[item.fieldName] = item.defaultVal;
    })
    return json;
  },
  //页面请求地址
  requestUrl: '/user/org',

  //页面字段参数，可根据实际需求扩展
  parames: [
    {
      label: '主键ID',
      fieldName: 'id',
      defaultVal: '',
      type: 'input',
      whereShow: []
    },
    {
      label: '管理员用户名',
      fieldName: 'adminUsername',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '管理员密码',
      fieldName: 'adminPassword',
      defaultVal: '',
      type: 'password',
      whereShow: ['form', 'add', 'edit']
    },
    {
      label: '名称',
      fieldName: 'name',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },


    {
      label: '联系人姓名',
      fieldName: 'contactName',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '联系人电话',
      fieldName: 'contactTel',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },

    {
      label: '公司分类',
      fieldName: 'companyType',
      defaultVal: '',
      type: 'select',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '上报上级金额(万元)',
      fieldName: 'limitAmount',
      defaultVal: '',
      type: 'input-number',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },

    {
      label: '省市区',
      fieldName: 'region',
      defaultVal: '',
      type: 'region',
      whereShow: ['form']
    },
    {
      label: '详细地址',
      fieldName: 'detailAddress',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '备注',
      size:50,
      fieldName: 'remark',
      defaultVal: '',
      type: 'textarea',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '是否启用',
      size:70,
      fieldName: 'ifEnabledCd',
      defaultVal: '1',
      type: 'radio',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '创建时间',
      fieldName: 'createDate',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },

    {
      label: '数据版本',
      fieldName: 'dataVersion',
      defaultVal: '',
      type: 'input',
      whereShow: []
    },
  ]
}
