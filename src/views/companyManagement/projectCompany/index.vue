<template>
  <div class="app-container">
    <div id="box">
      <div id="left" v-if="$router.currentRoute.fullPath === '/companyManagement/projectCompany'">
        <el-input
          size="small"
          placeholder="输入关键字进行过滤"
          v-model="filterText"
          clearable
        >
        </el-input>
        <el-scrollbar style="height:95%;margin-top: 20px">
          <el-tree
            ref="tree"
            :data="orgTreeData"
            :props="defaultProps"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
            :filter-node-method="filterNode"
            highlight-current
          />
        </el-scrollbar>
      </div>
      <div id="resize"></div>
      <div id="right" v-bind:style="{ width: detailWidth + 'px' }">
        <!--工具栏-->
        <div class="head-container">
          <div v-if="crud.props.searchToggle">
            <!-- 搜索 -->
            <el-input v-model="query.name" clearable size="small" placeholder="输入公司名称搜索" style="width: 200px;"
                      class="filter-item" @keyup.enter.native="crud.toQuery"/>
            <el-date-picker
              @change="crud.toQuery()"
              v-model="query.createTime"
              :default-time="['00:00:00','23:59:59']"
              type="daterange"
              range-separator=":"
              size="small"
              class="date-item"
              value-format="yyyy-MM-dd HH:mm:ss"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
            <rrOperation/>
          </div>
          <crudOperation :permission="permission"/>
        </div>
        <!--表格渲染-->
        <el-table ref="table" v-loading="crud.loading" :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                  default-expand-all :data="crud.data" row-key="id" @select="crud.selectChange"
                  @select-all="crud.selectAllChange" @selection-change="crud.selectionChangeHandler" border>
          <el-table-column :selectable="checkboxT" type="selection" width="55"/>
          <template v-for="(item, key) in tableListFields">
            <template v-if="showCompanyType && item.fieldName==='companyType' ">
              <el-table-column label="公司分类" prop="companyType" :key="key">
                <template v-slot="scope">
                  {{ showCurrentData(dict.companyType, scope.row.companyType) }}
                </template>
              </el-table-column>
            </template>
            <template v-else>
              <el-table-column :label="item.label" :prop="item.fieldName" :key="key" :width="item.size"
                               show-overflow-tooltip>
                <template v-slot="scope">
                  {{
                    item.fieldName === 'ifEnabledCd' ? showCurrentData(dict.disable_enable, scope.row.ifEnabledCd)
                      : scope.row[item.fieldName]
                  }}
                </template>
              </el-table-column>
            </template>
          </template>
          <el-table-column v-permission="permissionList" label="操作" width="220px" align="center" fixed="right">
            <template v-slot="scope">
              <div style="display:inline-block;">
                <udOperation
                  :data="{...scope.row,
                    region: [scope.row.provinceCd, scope.row.cityCd, scope.row.districtCd],
                    adminPassword: '******' }"
                  :permission="permission"
                  :disabledDle="scope.row.id === 1"
                  msg="确定删除吗,如果存在下级节点则一并删除，此操作不能撤销！"
                />
              </div>
              <div style="display:inline-block;">
                <el-tooltip class="item" effect="dark" content="重置密码" placement="top-start">
                  <el-button type="danger" size="mini" icon="el-icon-warning"
                             @click="resetDialog(scope.row)"></el-button>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination/>
      </div>
    </div>

    <!--表单组件-->
    <el-dialog append-to-body :close-on-click-modal="false" :before-close="crud.cancelCU"
               :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="580px">
      <el-form ref="form" :disabled="crud.status.details > 0" :model="form" :rules="rules" size="small"
               label-width="160px">
        <template v-for="(item, key) in formFields">
          <template v-if="item.type == 'radio'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-radio v-for="citem in dict.disable_enable" :key="citem.code" v-model="form[item.fieldName]"
                        :label="citem.code">{{ citem.name }}
              </el-radio>
            </el-form-item>
          </template>
          <template v-if="item.type == 'region'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-cascader
                style="width: 300px;"
                size="large"
                :options="regionData"
                v-model="form.region"
                @change="regionChange"
              >
              </el-cascader>
            </el-form-item>
          </template>
          <template v-if="item.type == 'input'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-input :disabled="item.fieldName == 'adminUsername' && crud.status.edit == 1"
                        v-model="form[item.fieldName]" style="width: 300px;"/>
            </el-form-item>
          </template>
          <template v-if="item.type === 'input-number' && showCompanyType">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-input-number v-model="form[item.fieldName]" style="width: 300px;" :disabled="item.disabled"
                               :controls="false"/>
            </el-form-item>
          </template>
          <template v-if="item.type == 'textarea'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-input type="textarea" v-model="form[item.fieldName]" style="width: 300px;"/>
            </el-form-item>
          </template>
          <template v-if="item.type === 'select'&& showCompanyType ">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-select v-model="form[item.fieldName]" style="width:300px" placeholder="请选择">
                <el-option
                  v-for="(item) in dict[item.fieldName]"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-if="item.type == 'password'">
            <el-form-item :label="item.label" :prop="item.fieldName" :key="key">
              <el-input :disabled="crud.status.edit == 1" type="password" v-model="form[item.fieldName]"
                        style="width: 300px;"/>
            </el-form-item>
          </template>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="重置密码"
      :visible.sync="dialogVisible"
      width="30%">
      <span>是否重置密码为Gczjglxt@2.0</span>
      <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="resetPassword">确 定</el-button>
          </span>
    </el-dialog>
  </div>
</template>

<script>
import crudDept, {getOrg2Tree} from './config/request'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import CRUD, {presenter, header, form, crud} from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import {validatePhoneTwo} from '@/utils/validate'

import {regionData} from 'element-china-area-data'
//注入配置文件
import config from './config/index'
import {mapGetters} from "vuex";
import crudUser from "../../user/user/config/request";
import {decrypt, encrypt} from '@/utils/rsaEncrypt'

const formFields = config.getFields('form');
const tableListFields = config.getFields('tableList');
const defaultForm = config.getDefaultForm(formFields);
export default {
  name: 'simpleList',
  components: {Treeselect, crudOperation, rrOperation, udOperation, pagination},
  cruds() {
    return CRUD({
      query: {
        typeCd: {
          '/companyManagement/projectCompany': 3,
          '/companyManagement/costCenter': 1,
          '/companyManagement/secondaryUnits': 2
        }[document.location.pathname],
      },
      title: '公司', sort: ['create_date,asc'], url: config.requestUrl, crudMethod: {...crudDept}
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 设置数据字典
  dicts: ['disable_enable', 'companyType'],
  computed: {
    ...mapGetters([
      'user'
    ])
  },
  data() {
    // 自定义验证
    const validPhone = (rule, value, callback) => {
      if (!value) {
        callback && callback(new Error('请输入正确的电话号码或者固话号码（格式：如010-1234567/13111111111）'))
      } else if (!validatePhoneTwo(rule, value, callback)) {
        callback && callback(new Error('请输入正确的电话号码或者固话号码（格式：如010-1234567/13111111111）'))
      } else {
        callback && callback()
      }
    }
    return {
      orgTreeData: [],
      defaultProps: {children: 'children', label: 'name'},
      filterText: '',
      detailWidth: document.documentElement.clientWidth,
      permissionList: {
        '/companyManagement/projectCompany': ['admin', 'companyManagement:projectCompany:edit', 'companyManagement:projectCompany:del', 'companyManagement:projectCompany:details'],
        '/companyManagement/costCenter': ['admin', 'companyManagement:costCenter:edit', 'companyManagement:costCenter:del', 'companyManagement:costCenter:details'],
        '/companyManagement/secondaryUnits': ['admin', 'companyMgement:secondaryanaUnits:edit', 'companyManagement:secondaryUnits:del', 'companyManagement:secondaryUnits:details']
      }[document.location.pathname],
      regionData: regionData,
      depts: [],
      formFields: formFields,
      tableListFields: tableListFields,
      dialogVisible: false,
      selectedRow: {},
      rules: {
        adminUsername: [
          {required: true, message: '请输入管理员用户名', trigger: 'blur'}
        ],
        adminPassword: [
          {required: true, message: '请输入管理员密码', trigger: 'blur'}
        ],
        name: [
          {required: true, message: '请输入名称', trigger: 'blur'}
        ],
        contactName: [
          {required: true, message: '请输入联系人姓名', trigger: 'blur'}
        ],
        contactTel: [
          {
            required: true,
            message: '请输入正确的电话号码（格式：如010-1234567、13111111111）',
            trigger: 'blur',
            validator: validPhone
          }
        ],
        companyType: [
          {required: true, message: '请选择公司分类', trigger: 'blur'}
        ],
      },
      permission: {
        '/companyManagement/projectCompany': {
          add: ['admin', 'companyManagement:projectCompany:add'],
          edit: ['admin', 'companyManagement:projectCompany:edit'],
          del: ['admin', 'companyManagement:projectCompany:del'],
          details: ['admin', 'companyManagement:projectCompany:details'],
        },
        '/companyManagement/costCenter': {
          add: ['admin', 'companyManagement:costCenter:add'],
          edit: ['admin', 'companyManagement:costCenter:edit'],
          del: ['admin', 'companyManagement:costCenter:del'],
          details: ['admin', 'companyManagement:costCenter:details'],
        },
        '/companyManagement/secondaryUnits': {
          add: ['admin', 'companyManagement:secondaryUnits:add'],
          edit: ['admin', 'companyManagement:secondaryUnits:edit'],
          del: ['admin', 'companyManagement:secondaryUnits:del'],
          details: ['admin', 'companyManagement:secondaryUnits:details'],
        }
      }[document.location.pathname],
      enabledTypeOptions: [
        {key: 1, display_name: '启用'},
        {key: 0, display_name: '禁用'}
      ],
      showCompanyType: false,
    }
  },
  created() {
    if (!this.showCompanyType) {
      this.tableListFields = this.tableListFields.filter(item => item.fieldName !== 'companyType');
    }
    if (this.$router.currentRoute.fullPath === '/companyManagement/projectCompany') {
      this.detailWidth /= 1.45;
    }
  },
  mounted() {
    this.getLeftOrg2Tree();
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
    $route: {
      handler: function (route) {
        if (route.path.indexOf('secondaryUnits') > 0) {
          this.showCompanyType = true
        } else {
          this.showCompanyType = false
        }
      },
      immediate: true
    }
  },
  methods: {
    // 获取左侧单位树
    getLeftOrg2Tree() {
      const sort = 'id,desc'
      const params = {sort: sort}
      getOrg2Tree(params).then(res => {
        this.orgTreeData = res
      })
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    // 单位
    handleNodeClick(data) {
      this.query.orgCodeRLike = data.code;
      this.crud.toQuery()
    },
    regionChange(data) {
    },
    [CRUD.HOOK.beforeToEdit](crud) {
      if (crud.form.companyType !== null) {
        crud.form.companyType = crud.form.companyType.toString();
      }
    },
    [CRUD.HOOK.afterRefresh](crud) {
      this.crud.data.map(item => {
        item.ifEnabledCd = item.ifEnabledCd.toString();
      })
      return true;
    },
    // 提交前的验证
    [CRUD.HOOK.afterValidateCU]() {
      let newData = {
        ...this.crud.form
      };
      newData['provinceCd'] = this.crud.form.region[0];
      newData['cityCd'] = this.crud.form.region[1];
      newData['districtCd'] = this.crud.form.region[2];
      delete newData.region;
      newData.typeCd = {
        '/companyManagement/projectCompany': 3,
        '/companyManagement/costCenter': 1,
        '/companyManagement/secondaryUnits': 2
      }[this.$router.currentRoute.fullPath]
      if (this.crud.status.edit === 1) {
        delete newData.adminPassword;
        delete newData.typeCd;
      }
      this.crud.formData = newData;
      return true
    },
    // 改变状态
    changeEnabled(data, val) {
      this.$confirm('此操作将 "' + this.dict.label.dept_status[val] + '" ' + data.name + '部门, 是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        crudDept.edit(data).then(res => {
          this.crud.notify(this.dict.label.dept_status[val] + '成功', CRUD.NOTIFICATION_TYPE.SUCCESS)
        }).catch(err => {
          data.enabled = !data.enabled
        })
      }).catch(() => {
        data.enabled = !data.enabled
      })
    },
    checkboxT(row, rowIndex) {
      return row.id !== 1
    },
    //重置密码
    resetPassword() {
      let data = {
        id: this.selectedRow.userId,
        passwordCode: encrypt('Gczjglxt@2.0'),
        password: encrypt('Gczjglxt@2.0')
      }
      crudUser.resetPassword(data).then(res => {
        this.$message.success("操作成功")
        this.dialogVisible = false;
      })
    },
    //重置密码
    resetDialog(row) {
      this.dialogVisible = true;
      this.selectedRow = row;
    }
  }

}
</script>
<style scoped>
#box {
  width: 100%;
  height: 700px;
  position: relative;
  overflow: hidden;
  display: flex;
}

#left {
  height: 100%;
  flex: 1;
}

#resize {
  width: 8px;
  height: 100%;
  cursor: w-resize;
}

#right {
  height: 100%;
}

.el-scrollbar >>> .el-scrollbar__wrap {
  overflow-x: hidden;
}

>>> .el-tree-node__label {
  font-size: 12px;
}

.el-select-dropdown__item.hover, .el-select-dropdown__item:hover {
  background-color: #ffffff;
}
>>> .el-input--small .el-input__inner {
  height: 30.5px;
  line-height: 32px;
}

</style>
