<template>
  <div style="margin-left: 40px">
    <el-form ref="form" :model="scoreEntity" label-width="100px" inline>
        <el-form-item label="外委单位名称">
          <el-input v-model="scoreEntity.outerCompanyName" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="项目经理">
          <el-input v-model="scoreEntity.projectLeader"></el-input>
        </el-form-item>
        <el-form-item label="考核年度">
          <el-date-picker
            disabled
            v-model="scoreEntity.year"
            type="year"
            value-format="yyyy"
            :style="formInputStyle"
            @change="changeYearPicker"
            placeholder="选择日期时间">
          </el-date-picker>
        </el-form-item>
      <el-form-item label="考核单位名称">
        <el-input v-model="scoreEntity.currentCompanyName" :disabled="true"></el-input>
      </el-form-item>
    </el-form>
    <table style="margin-left: 30px">
      <thead>
      <tr>
        <td class="label-header">序号</td>
        <td class="label-header">考核项目及内容</td>
        <td class="label-header">评分标准</td>
        <td class="label-header">考核数据得分</td>
      </tr>
      </thead>
      <tbody>
      <tr>
        <td class="label-width-no">1</td>
        <td class="label-width">造价咨询项目数量(个)</td>
        <td class="label-width">
          <el-input v-model="scoreEntity.projectCount" placeholder="请输入数量"  />
        </td>
        <!-- <td class="label-width-right">
          <el-input-number ref="refNumber1" :controls="false" v-model="scoreEntity.projectCountScore"
                           placeholder="请评分" :precision="2"  disabled/>
        </td> -->
      </tr>
      <tr>
        <td class="label-width-no">2</td>
        <td class="label-width">咨询项目审定总金额(万元)</td>
        <td class="label-width">
          <el-input v-model="scoreEntity.projectAmount" placeholder="请输入金额"  />
        </td>
        <!-- <td class="label-width-right">
          <el-input-number ref="refNumber2" :controls="false" v-model="scoreEntity.projectAmountScore"
                           placeholder="请评分" :precision="2" disabled/>
        </td> -->
      </tr>

      <tr>
        <td class="label-width-no">3</td>
        <td class="label-width">咨询服务及时性</td>
        <td class="label-width" rowspan="4">
          10-8分，优秀; 7-5分，良好; 4-2分，及格; 1-0分，不及格;
        </td>
        <td class="label-width-right">
          <el-input-number ref="refNumber3" :controls="false" v-model="scoreEntity.serverScore"
                           placeholder="请评分" :precision="2" @blur="handleChange()" :max="10"/>
        </td>
      </tr>

      <tr >
        <td class="label-width-no">4</td>
        <td class="label-width">咨询服务及时性配合度</td>
        <td class="label-width-right">
          <el-input-number  ref="refNumber4" :controls="false" v-model="scoreEntity.serverTimelyScore"
                           placeholder="请评分" :precision="2" @blur="handleChange()" :max="10"/>
        </td>
      </tr>
      <tr>
        <td class="label-width-no">5</td>
        <td class="label-width">咨询服务质量</td>
        <td class="label-width-right">
          <el-input-number ref="refNumber5" :controls="false" v-model="scoreEntity.serverQualityScore"
                           placeholder="请评分" :precision="2" @blur="handleChange()" :max="10"/>
        </td>
      </tr>
      <tr>
        <td class="label-width-no">6</td>
        <td class="label-width">处理疑难问题能力</td>
        <td class="label-width-right">
          <el-input-number ref="refNumber6" :controls="false" v-model="scoreEntity.serverRequestScore"
                           placeholder="请评分" :precision="2" @blur="handleChange()" :max="10"/>
        </td>
      </tr>
      <tr>
        <td class="label-width-no">7</td>
        <td class="label-width">其他</td>
        <td class="label-width">扣分项，如果有证明材料请提交附件
          <el-upload
            ref="upload"
            action=""
            :http-request="httpRequest"
            :on-remove="handleRemove"
            :file-list="scoreEntity.fileList">
            <el-button type="text" size="mini" plain>文件上传</el-button>
          </el-upload>
        </td>
        <td class="label-width-right">
          <el-input-number ref="refNumber7" :controls="false" v-model="scoreEntity.serverOtherScore"
                           placeholder="请评分" :precision="2" @blur="handleChange()" :max="5" :min="-5"/>
        </td>
      </tr>
      <tr>
        <td class="label-width-no">8</td>
        <td class="label-width">备注</td>
        <td class="label-width" >
          <el-input v-model="scoreEntity.remark" placeholder="备注"  type="textarea"
                    :rows="2"/>
        </td>
        <td class="label-width-right"></td>
      </tr>
      <!-- <tr>
        <td class="label-width-no"></td>
        <td class="label-width" >合计</td>
        <td class="label-width">
        </td>
        <td class="label-width-right">
          <el-input-number v-model="scoreEntity.totalScore" :disabled="true" :precision="2" :controls="false"/>
        </td>

      </tr> -->
      </tbody>
    </table>
    <div style="margin-top: 10px;margin-left: 5px">
      填表须知：<br/>
      1.造价咨询项目数量以一年度自然年位单位计算;<br/>
      2.咨询服务及时性、咨询服务配合度、咨询服务质量、处理疑难问题能力，每项满分10分，根据实际情况进行评分<br/>
      10-8分，优秀; 7-5分，良好; 4-2分，及格; 1-0分，不及格;<br/>
      3.其他项内填报的是表中未列二十级涉及到的奖扣得分，评分可增可减，分值范围为-5~5分，需在备注中对奖扣事项进行详尽描述。没有则不填。<br/>
    </div>
    <div style="text-align: right;margin-top: 20px;margin-right: 20px">
      <el-button type="primary" @click="commit">提 交</el-button>
    </div>
  </div>
</template>

<script>
  import {upload} from '@/utils/upload'
  import {list} from '@/views/system/config/config/request'
  import {mapGetters} from "vuex";

  export default {
    props: {
      commitData: {
        type: Function,
        default: () => {
        }
      },
      scoreEntity: {
        type: Object,
        default: () => {
        }
      },
      changeYear: {
        type: Function,
        default: () => {
        }
      },
    },
    computed: {
      ...mapGetters([
        'user'
      ]),
      dataRange() {
        const {ranDomValue} = this;
        return {ranDomValue}
      }
    },
    watch: {
      scoreEntity:{
        handler(){
          this.scoreEntity.outerCompanyName = this.scoreEntity.row.name
          this.scoreEntity.year = this.scoreEntity.year ? this.scoreEntity.year : '2024';
          this.scoreEntity.enterpriseId = this.scoreEntity.row.id
          this.scoreEntity.currentCompanyName = this.user.orgName
        },
        deep: true
      },
      dataRange:{
        handler(newValue,oldValue){
        },
        deep: true
      },
      'scoreEntity.projectCount':{
        handler(){
          if (this.scoreEntity.projectCount) {
            if (parseFloat(this.scoreEntity.projectCount) > parseFloat(this.scoreProMap.get('score_pro_count_divisor'))) {
              this.scoreEntity.projectCountScore = parseFloat(this.scoreProMap.get('score_pro_count_multiplier'));
            }else {
              this.scoreEntity.projectCountScore = parseFloat(this.scoreEntity.projectCount)/parseFloat(this.scoreProMap.get('score_pro_count_divisor'))*parseFloat(this.scoreProMap.get('score_pro_count_multiplier'))
            }
          }else {
            this.scoreEntity.projectCountScore = 0;
            this.scoreEntity.projectCount = 0
          }
          this.handleChange();
        }
      },
      'scoreEntity.projectAmount':{
        handler(){
          if (this.scoreEntity.projectAmount) {
            if ((parseFloat(this.scoreEntity.projectAmount) * 10000) > parseFloat(this.scoreProMap.get('score_pro_money_divisor'))) {
              this.scoreEntity.projectAmountScore = parseFloat(this.scoreProMap.get('score_pro_money_multiplier'));
            }else {
              this.scoreEntity.projectAmountScore = parseFloat(this.scoreEntity.projectAmount) * 10000/parseFloat(this.scoreProMap.get('score_pro_money_divisor'))*parseFloat(this.scoreProMap.get('score_pro_money_multiplier'))
            }
          }else {
            this.scoreEntity.projectAmountScore = 0;
            this.scoreEntity.projectAmount = 0
          }
          this.handleChange();
        }
      }
    },
    data() {
      return {
        ranDomValue:0,
        scoreProMap:{}
      }
    },
    created() {
      list({configLabelLikeQuery:'score_pro_'}).then(res=>{
        this.scoreProMap = new Map();
        res.forEach(resKey=>{
          this.scoreProMap.set(resKey.configLabel,resKey.configValue)
        })
      })
    },
    dicts: [],
    methods: {
      handleChange() {
        this.$nextTick(res=>{
          let parseFloat7 = parseFloat(this.$refs.refNumber7.displayValue);
          let parseFloat6 = parseFloat(this.$refs.refNumber6.displayValue);
          let parseFloat5 = parseFloat(this.$refs.refNumber5.displayValue);
          let parseFloat4 = parseFloat(this.$refs.refNumber4.displayValue);
          let parseFloat3 = parseFloat(this.$refs.refNumber3.displayValue);
          this.scoreEntity.serverOtherScore = this.isNumber(parseFloat7) ? parseFloat7 : 0;
          this.scoreEntity.serverRequestScore = this.isNumber(parseFloat6) ? parseFloat6 : 0;
          this.scoreEntity.serverQualityScore = this.isNumber(parseFloat5) ? parseFloat5 : 0;
          this.scoreEntity.serverTimelyScore = this.isNumber(parseFloat4) ? parseFloat4 : 0;
          this.scoreEntity.serverScore = this.isNumber(parseFloat3) ? parseFloat3 : 0;
          this.scoreEntity.totalScore = this.scoreEntity.serverOtherScore
              + this.scoreEntity.serverRequestScore
              + this.scoreEntity.serverQualityScore
              + this.scoreEntity.serverTimelyScore
              + this.scoreEntity.serverScore
              + this.scoreEntity.projectCountScore
              + this.scoreEntity.projectAmountScore;
              
        })
      },
      isNumber(val){
        const regExp = /^[1-9]\d*\,\d*|[1-9]\d*$/;
        return regExp.test(val);
      },
      httpRequest(data) {
        const loading = this.$loading({
          lock: true,
          text: '正在上传，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        upload('general/file/upload', data.file)
          .then(res => {
            if (res.data.status === 200) {
              let result = {
                uri: res.data.data,
                name: data.file.name,
                uid: data.file.uid
              }
              this.scoreEntity.fileList.push(result);
            } else {
              this.$message.error(res.data.message);
            }
            loading.close();
          })
          .catch(e => {
            loading.close();
            this.$message.error('上传失败！请稍后重试！');
          })
      },
      handleRemove(file, fileList) {
        this.scoreEntity.fileList = fileList;
      },
      commit() {
        if (this.scoreEntity.totalScore > 0) {
          this.commitData(this.scoreEntity);
        }else {
          this.$message.warning("合计不能为0，请评分后再提交！")
        }
      },
      changeYearPicker() {
        if (this.scoreEntity.year) {
          this.changeYear({id: this.scoreEntity.row.id, year: this.scoreEntity.year})
        }
      },
      validateInput() {
        if (this.scoreEntity.projectCount > 10.00) {
          this.scoreEntity.projectCount = 10.00;
        }
      }
    },

  }
</script>
<style>
  .label-width-no {
    width: 50px;
    text-align: center;
    border-bottom: 1px solid #dfe6ec;
    border-right: 1px solid #dfe6ec;
  }
  .label-width {
    width: 300px;
    text-align: left;
    border-bottom: 1px solid #dfe6ec;
    border-right: 1px solid #dfe6ec;
  }
  .label-width-right {
    width: 100px;
    text-align: left;
    border-bottom: 1px solid #dfe6ec;
  }
  .label-header{
    color: white;
    text-align: center;
    height: 50px;
    border-bottom: 1px solid #dfe6ec;
    border-right: 1px solid #dfe6ec;
    background: #a0a6ac;
  }
  table  {
    border: 1px solid #dfe6ec;
  }
</style>
