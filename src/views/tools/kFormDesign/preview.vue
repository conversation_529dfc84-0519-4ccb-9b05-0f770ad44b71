<template>
  <div style="padding-top:20px">
    <k-form-build
      :value="jsonData"
      ref="KFB"
      @submit="handleSubmit"
      :dynamicData="dynamicData"
    />
    <el-button style="width:200px;margin:0 auto;display:block" @click="getData" type="danger">收审修改</el-button>
    <el-button style="width:200px;margin:0 auto;display:block" @click="getData" type="success">提交</el-button>
  </div>
</template>
<script>
  import KFormDesign from '../../../components/k-form-design/packages'
  import '../../../components/k-form-design/styles/form-design.less'
  import Vue from 'vue'
  Vue.use(KFormDesign)
  import {getFormData} from '@/views/workflow/workflowForm/config/request'

  export default {
    name: 'Demo',
    // 设置数据字典
    dicts: ['auto_form_type'],

    data() {
      return {
        jsonData: {},
        dynamicData: {
          selectData: []
        }
      }
    },

    methods: {
      handleSubmit(p) {
        // 通过表单提交按钮触发，获取promise对象
        p().then(res => {
          // 获取数据成功
          alert(JSON.stringify(res))
        })
          .catch(err => {
            console.log(err, '校验失败')
          })
      },
      getData() {
        // 通过函数获取数据
        this.$refs.KFB.getData().then(res => {
          // 获取数据成功
          alert(JSON.stringify(res))
        })
          .catch(err => {
            console.log(err, '校验失败')
          })
      },
    },
    mounted() {
      getFormData({
        id: this.$route.query.id
      }).then(res => {
          this.jsonData = JSON.parse(res.content);
        })
    },
  }
</script>
