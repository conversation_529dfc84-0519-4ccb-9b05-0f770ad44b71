<template>
<div>
  <KFormDesign
    @save="handleSave"
    title="表单设计"
    toolbarsTop
    ref="kfd"
  ></KFormDesign>
</div>
</template>

<script>
//const  KFormDesign = r => require.ensure( [], () => r (require('../../../components/k-form-design/packages')))
import KFormDesign from '../../../components/k-form-design/packages'
import '../../../components/k-form-design/styles/form-design.less'
import Vue from 'vue'
Vue.use( KFormDesign )

import { editContent, getWorkflowForm } from '@/api/workflow/workflowForm'


export default {
  name: "k",

  data(){
    return {
      jsonData: {}
    }
  },
  created() {

  },
  methods: {
    importData () {
      getWorkflowForm({
        id: this.$route.query.id
      })
      .then(res=>{
        this.jsonData = JSON.parse( res.content );
        this.$refs.kfd.handleSetData(this.jsonData)
      })

    },
    handleSave(data){
       const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
      editContent({
        id: this.$route.query.id,
        content: data
      })
      .then(res=>{
        loading.close();
        this.$message.success('操作成功！');
        this.$router.back();
      })
      .catch(err=>{
        loading.close();
      })


    }
  },
  mounted () {
    this.importData()
  }
}
</script>
<style>
  .content aside{
    width: 300px !important;
    background:#fff;
  }
</style>
