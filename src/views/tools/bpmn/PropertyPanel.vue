﻿<template>
  <div class="property-panel" ref="propertyPanel">
    <el-form :inline="true" :model="form" label-width="100px" size="small">
      <el-form-item label="节点ID">
        <el-input v-model="form.id" disabled></el-input>
      </el-form-item>
      <el-form-item label="节点名称">
        <el-input v-model="form.name" @input="nameChange"></el-input>
      </el-form-item>
      <el-form-item label="节点颜色">
        <el-color-picker v-model="form.color" @active-change="colorChange"></el-color-picker>
      </el-form-item>
      <el-form-item label="表单" v-if="userTask && form.$type == 'bpmn:UserTask'">
        <el-select v-model="form.associatedForm" placeholder="请选择" @change="associatedFormChange">
          <el-option :value="item.id" :label="item.name" v-for="(item, key) in associatedForm" :key="key"></el-option>
        </el-select>
      </el-form-item>
      <!-- 任务节点允许选择人员 -->
      <el-form-item label="数据源" v-if="userTask && form.$type == 'bpmn:UserTask'">
        <el-select v-model="form.userType" placeholder="请选择" @change="typeChange">
          <el-option :value="item.key" :label="item.name" v-for="(item, key) in userTaskList" :key="key"></el-option>
        </el-select>
      </el-form-item>
      <!-- 候选人员 -->
      <el-form-item label="人员" v-if="userTask && form.$type == 'bpmn:UserTask'">
        <el-cascader
         @change="(value) => addUser({[form.userType]: value || value})"
        v-model="form[form.userType]"
        :options="users"
        :props="{
          multiple: (form.userType != 'assignee' && form.userType != 'relativeUser'),
          value: 'id',
          label: 'name',
          children: 'children',
          disabled: 'ifOrg',
          checkStrictly: true,
          emitPath: false
        }"
        :show-all-levels="false"
        clearable></el-cascader>
      </el-form-item>

      <!-- 分支允许添加条件 -->
      <el-form-item label="分支条件" v-if="sequenceFlow">
        <el-input v-model="form.user" @input="sequenceFlowChange" type="text"/>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>

import {
  getUserTypeList,
  getOrgDeptList,
  getOrgPositionList,
  getOrgRoleList,
  getOrgUserList,
  getRelativeUserList
} from '@/api/workflow/workflowUser'
import { getPage } from '@/views/workflow/workflowForm/config/request'

export default {
  name: "PropertyPanel",
  props: {
    modeler: {
      type: Object,
      required: true
    }
  },
  watch:{

  },
  computed: {
    userTask() {
      if (!this.element) {
        return;
      }
      return this.element.type != 'bpmn:SequenceFlow';
      //return this.element.type === "bpmn:UserTask";
    },
    sequenceFlow() {
      if (!this.element) {
        return;
      }
      return this.element.type === "bpmn:SequenceFlow";
    }
  },
  data() {
    return {
      //类目
      associatedForm:[],
      form: {
        id: "",
        name: "",
        color: null
      },
      dataLists: {},
      element: {},
      userTaskList: [],
      users: []
    };
  },
  mounted() {
    this.handleModeler();
    this.initPageData();
  },
  methods: {
    //获取页面所需要的数据
    initPageData(){
      getUserTypeList({})
      .then(res=>{
        this.userTaskList = res;
        console.log(res)
      })

      //获取表单列表
      getPage({
        page: 1,
        size: 1000
      })
      .then(res=>{
        this.associatedForm =  res.records;
      })

    },
    //关联表单回调
    associatedFormChange(val){
       this.updateProperties({
        associatedForm: val
      })
    },
    //updateProperties
    //分支节点发生变化回调
    sequenceFlowChange(val){
      let moddle = this.modeler.get('moddle')
     this.updateProperties({
        conditionExpression: moddle.create('bpmn:FormalExpression', {
          body: `<![CDATA[${val}]]>`
        })
      })
   },
   getBranchData(element){
     if(element.businessObject.conditionExpression && element.businessObject.conditionExpression.body){
      return element.businessObject.conditionExpression && element.businessObject.conditionExpression.body.replace('<![CDATA[', '').replace(']]>', '');
      }
      return '';
   },
    handleModeler() {
      // 监听节点选择变化
      this.modeler.on("selection.changed", e => {
        const element = e.newSelection[0];
        this.element = element;
        if (!element) return;
        let branchData = this.getBranchData( element )
        this.form = {
          ...element.businessObject,
          ...element.businessObject.$attrs,
          user: branchData ? branchData : ''
        };
        //同步分支条件节点数据
        if(element.type == 'bpmn:SequenceFlow' && branchData){
          this.sequenceFlowChange(branchData);
        }
        if( this.form[this.form.userType] ){
          this.form[this.form.userType] = typeof(this.form[this.form.userType]) == 'string' ? this.form[this.form.userType].split(',').map(item=>item) : this.form[this.form.userType];
          if( this.form[this.form.userType].length <= 1 ){
            this.form[this.form.userType] = this.form[this.form.userType][0];
          }
        }
        //this.element.type
        if( this.form["candidateUsers"] ){
         this.form["candidateUsers"] = this.form["candidateUsers"].split(',') || [];
        }
         //查询数据
        if( this.form.userType ){
         this.switchPersonnelData();
        }
      });
      //  监听节点属性变化
      this.modeler.on("element.changed", e => {
        const { element } = e;
        if (!element) return;
        //  新增节点需要更新回属性面板
        if (element.id === this.form.id) {
          this.form.name = element.businessObject.name;
          this.form = { ...this.form };
        }
      });
    },
    // 属性面板名称，更新回流程节点
    nameChange(name) {
      const modeling = this.modeler.get("modeling");
      modeling.updateLabel(this.element, name);
    },
    // 属性面板颜色，更新回流程节点
    colorChange(color) {
      const modeling = this.modeler.get("modeling");
      modeling.setColor(this.element, {
        fill: null,
        stroke: color
      });
      modeling.updateProperties(this.element, { color: color });
    },
    // 任务节点配置人员
    addUser(properties) {
      //传值父组件
      this.$emit('func',this.form)
      this.updateProperties(
        Object.assign(properties, {
          userType: Object.keys(properties)[0],
          candidateUser: this.form.candidateUsers && this.form.candidateUsers.flat(Infinity).join(',')
        })
      );
    },
    // 切换人员类型
    typeChange() {
      const types = this.userTaskList.map(item=>item.key);
      //this.form.candidateUsers = '';
      //form[form.userType]
      this.switchPersonnelData();
      types.forEach(type => {
        delete this.element.businessObject.$attrs[type];
        delete this.form[type];
       // this.form[type] = '';
      });
    },
    //切换人员数据
    switchPersonnelData(){
      let currentData = this.dataLists[ this.form.userType ];
      //接口map
      let interfaceMap = {
          'candidateDept': getOrgDeptList,
          'candidatePosition': getOrgPositionList,
          'candidateRole': getOrgRoleList,
          'assignee': getOrgUserList,
          'candidateUser': getOrgUserList,
          'relativeUser': getRelativeUserList
      }
      //处理数据
      if( currentData ){
        this.users = currentData;
      } else {
        interfaceMap[ this.form.userType ]()
        .then(res=>{

          if( this.form.userType == 'relativeUser' ){
            res = res.map(item=>({
              name: item.name,
              id: item.key
            }))
          }

          this.buildData(res, 'children')
          this.dataLists[ this.form.userType ] = res;
          this.users = res;
        })
      }
      //更新节点数据
      this.updateProperties({ userType: this.form.userType })
    },
    // 在这里我们封装一个通用的更新节点属性的方法
    updateProperties(properties) {
      const modeling = this.modeler.get("modeling");

      modeling.updateProperties(this.element, properties);
    }
  }
};
</script>

<style lang="scss" scoped>
.property-panel {
  position: absolute;
  right: 0px;
  top: 0px;
  border-left: 1px solid #cccccc;
  padding: 20px 0;
  width: 300px;
  height: 100%;
}
</style>
