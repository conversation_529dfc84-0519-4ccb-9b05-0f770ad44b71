// // PanelToolbar/index.js
// import inherits from 'inherits';
// import PropertiesActivator from 'bpmn-js-properties-panel/lib/PropertiesActivator';
// import baseInfo from './parts/BaseInfoProps';

// // 创建基础信息看板
// function createBaseInfoTab(element, bpmnFactory, elementRegistry, translate) {
//     const generalGroup = {
//         id: 'baseInfo',
//         label: '',
//         entries: [],
//     };

//     baseInfo(generalGroup, element, bpmnFactory, translate);
//     return [generalGroup];
// }

// function MagicPropertiesProvider(eventBus, bpmnFactory, elementRegistry, translate) {
//     PropertiesActivator.call(this, eventBus);
//     this.getTabs = function(element) {
//         const baseInfoTab = {
//             id: 'baseInfo',
//             label: '基本信息',
//             groups: createBaseInfoTab(element, bpmnFactory, elementRegistry, translate),
//         };

//         return [baseInfoTab];
//     };
// }

// inherits(MagicPropertiesProvider, PropertiesActivator);

// MagicPropertiesProvider.$inject = ['eventBus', 'bpmnFactory', 'elementRegistry', 'translate'];

// export default {
//     __init__: ['propertiesProvider'],
//     propertiesProvider: ['type', MagicPropertiesProvider],
// };