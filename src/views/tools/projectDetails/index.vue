<template>
  <el-tabs type="card" v-model="activeName" @tab-click="tabHandleClick">
    <el-tab-pane label="报审信息" name="0">
      <table border="0" cellspacing="0" cellpadding="0">
        <tbody>
        <tr>
          <td class="label-width">报审编号</td>
          <td>{{ currentData.auditCode }}</td>
          <td class="label-width">项目类型</td>
          <td>{{ showCurrentData( dict.project_type_new, project.projectTypeCd) }}</td>
        </tr>
        <tr>
          <td class="label-width">项目名称</td>
          <td>{{ project.projectName }}</td>
          <td class="label-width">建设单位</td>
          <td>{{ project.buildThreeOrgName }}</td>
        </tr>
        <tr>
          <td class="label-width" style="border-left:none">单体工程名称</td>
          <td colspan="3">
            {{ currentData.projectAuditName }}
          </td>
        </tr>
        <tr>
          <td class="label-width" style="border-bottom:none;border-left:none">报审工程内容</td>
          <td colspan="3" style="border-bottom:none">
            {{ currentData.projectAuditContext }}
          </td>
        </tr>
        <tr>
          <td class="label-width" style="border-bottom:none;border-left:none">施工单位</td>
          <td colspan="3" style="border-bottom:none">
            {{ currentData.constructionOrgName }}
          </td>
        </tr>

        <tr>
          <td class="label-width">项目情况</td>
          <td colspan="6" style="padding:0">
            <table style="border:none">
              <tr>
                <td class="label-width" style="border-left:none;border-top:none;">报审金额</td>
                <td colspan="7" style="border-top:none">{{ currentData.buildTotalInvestmentAmount }}万元</td>
              </tr>
              <tr>
                <td class="label-width" style="border-top:none">工程费用</td>
                <td class="label-width" style="border-top:none;text-align:left">{{ currentData.constructionAmount }}万元
                </td>
                <td class="label-width" style="border-top:none">甲供设备材料费</td>
                <td class="label-width" style="border-top:none;text-align:left">{{ currentData.auditEmFirstAmount }}万元
                </td>
                <td class="label-width" style="border-top:none">其他费</td>
                <td class="label-width" style="border-top:none;text-align:left">{{ currentData.otherAmount }}万元</td>
                <td class="label-width" style="border-top:none">抵扣增值税额</td>
                <td class="label-width" style="border-top:none;text-align:left">{{ currentData.auditAddValueAmount
                  }}万元
                </td>
              </tr>
              <tr>
                <td class="label-width" style="border-top:none">送审人</td>
                <td colspan="3" style="border-top:none;border-right:none">{{ currentData.createUserName }}</td>
                <td class="label-width" style="border-top:none">送审时间</td>
                <td colspan="3" style="border-top:none;border-right:none">{{ currentData.submitAuditTime }}</td>
              </tr>
              <tr>
                <td class="label-width" style="border-top:none">初审外委单位名称</td>
                <td colspan="7" style="border-top:none;border-right:none">{{
                  showCurrentData(unitDict,currentData.auditInitOrgName) }}
                </td>
              </tr>
              <tr>
                <td class="label-width" style="border-left:none">初设批复文号</td>
                <td colspan="7" style="border-top:none;border-right:none">{{ currentData.auditInitCode }}</td>
              </tr>
              <tr>
                <td class="label-width">初设批复内容</td>
                <td colspan="7" style="border-top:none;border-right:none">{{ currentData.auditInitContext }}</td>
              </tr>

              <tr>
                <td class="label-width" style="border-left:none;border-top:none;">批复概算总金额</td>
                <td colspan="7" style="border-top:none;text-align:left">{{
                  project?project.buildTotalInvestmentAmount:'' }}万元
                </td>
              </tr>
              <tr>
                <td class="label-width" style="border-top:none">批复概算工程费</td>
                <td class="label-width" style="border-top:none;text-align:left">{{
                  project?project.budgetProjectTotalAmount:'' }}万元
                </td>
                <td class="label-width" style="border-top:none">甲供材料设备费</td>
                <td class="label-width" style="border-top:none;text-align:left">{{
                  project?project.budgetEmFirstAmount:'' }}万元
                </td>
                <td class="label-width" style="border-top:none">批复概算其他费</td>
                <td class="label-width" style="border-top:none;text-align:left">{{
                  project?project.budgetOtherTotalAmount:'' }}万元
                </td>
                <td class="label-width" style="border-top:none">批复概算抵扣增值税</td>
                <td class="label-width" style="border-top:none;text-align:left">{{
                  project?project.budgetAddValueTotalAmount:''}}万元
                </td>
              </tr>
            </table>
          </td>
        </tr>
        </tbody>
      </table>
    </el-tab-pane>
    <el-tab-pane label="审核记录" name="3">
      <el-table
        :data="auditRecords.records"
        v-loading="auditLoading"
        style="width: 100%">
        <el-table-column type="expand">
          <template v-slot="props">
            <p v-if="!props.row.formContent">暂无数据</p>
            <PreviewForm
              style="margin:0 60px 0 60px"
              v-if="props.row.formContent"
              :jsonData="props.row.formContent"
              :submit="submitData"
              :defaultValue="props.row.formData"
              :isHideBtn="true"
              :disabled="true"
              :record="props.row"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="任务名称"
          prop="name">
        </el-table-column>
        <el-table-column
          label="执行人"
          prop="assigneeName">
        </el-table-column>
        <el-table-column
          label="创建时间"
          prop="createdDate">
        </el-table-column>
        <el-table-column
          label="完成时间"
          prop="completedDate">
        </el-table-column>
        <el-table-column
          label="持续时间"
        >
          <template v-slot="scope">
            {{ scope.row.duration | timeConsuming }}
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :page-size="auditPageSize"
        :current-page="auditPage"
        layout="prev, pager, next"
        @current-change="auditPageChange"
        :total="auditRecords.total">
      </el-pagination>
    </el-tab-pane>
    <el-tab-pane label="附件" name="1">
      <el-table
        :data="enclosureList"
        border
        :header-cell-style="{background: '#f5f7fa'}"
        style="width: 100%; ">
        <el-table-column
          prop="name"
          label="附件名称"
        >
        </el-table-column>
        <el-table-column
          label="资料名录">
          <template v-slot="scope">
            {{ showCurrentData(dict.catalogue, scope.row.directoriesCd) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="createDate"
          label="创建时间">
        </el-table-column>
        <el-table-column
          label="操作">
          <template v-slot="scope">
            <el-button type="text" @click="downLoadEnclosure(scope.row.name,scope.row.uri)">下载附件</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-tab-pane>
    <el-tab-pane label="结算数据" name="2">
      <div style="height: 35px;display:flex;justify-content:left;align-items:center;">
        <div align="left" style="float:left;">
          结算文件:{{fileDetail.name}}
        </div>
        <div align="right" style="margin-left: 100px">
          <el-button type="text" @click="downLoadEnclosure(fileDetail.name,fileDetail.url)">下载</el-button>
        </div>
      </div>
      <el-table
        :data="resultFileList"
        border
        :header-cell-style="{
                background: '#f5f7fa'
            }"
        style="width: 100%; margin-top: 10px ">
        <el-table-column
          prop="name"
          label="文件名称"
        >
        </el-table-column>

        <el-table-column
          label="文件类型"
        >
          <template v-slot="scope">
            {{ showCurrentData(dict.result_file_type, scope.row.sheetType) }}
          </template>
        </el-table-column>
        <el-table-column
          label="手动归集"
        >
          <template v-slot="scope">
            <el-switch
              v-model="scope.row.autoType"
              :disabled="true">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column
          prop="budgetNames"
          label="归集节点"
        >
        </el-table-column>
      </el-table>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
  import {resultFileDetail} from "@/views/compare/result/config/request";
  import request from '@/utils/request'
  import {downloadFile} from '@/utils/index'
  import {getHistoryTaskPage} from '@/views/workflow/workflow/config/request'
  import PreviewForm from '@/components/PreviewForm'
  import { timeConsuming } from '@/utils/datetime'
  export default {
    props: {
      id: {
        type: String,
        default: ''
      },
      projectId: {
        type: String,
        default: ''
      },
      visible: {
        type: Boolean,
        default: false
      },
      unitDict: {
        type: Array,
        default: ()=>[]
      },
      record: {
        type: Object,
        default: () => ({})
      },
    },
    name: 'projectDetails',
    watch: {
      id(newVal) {
        this.activeName = '0';
      },
      projectId(newVal) {
        this.activeName = '0';
      },
      visible() {
        this.auditRecords = {};
        this.radio = '1';
        this.activeName = '2';
        this.auditPage = 1;
        this.getProjectAuditAccessory(this.id)
        this.initPage();
      },
    },
    components: {
      PreviewForm,
    },
    data() {
      return {
        activeName: '0',
        currentData: {},
        project: {},
        auditRecords: {},
        enclosureList: [],
        resultFileList: [],
        fileDetail: {},
        auditPageSize: 10,
        auditPage: 1,
        auditLoading: false,
      }
    },
    dicts: ['attachment', 'result_file_type', 'audit_nature', 'engineering_types', 'project_nature','project_type_new','catalogue'],
    methods: {
        //耗时转换
      timeConsuming(time){
        console.log(timeConsuming( time ));
        return timeConsuming( time );
      },
      tabHandleClick(record, index) {
        // if (this.auditRecords.total) return;
        // this.getAuditPage();
      },
      //下载附件
      downLoadEnclosure(name, url) {
        request({
          url: '/general/file/download?uri=' + url,
          type: 'get',
          responseType: 'blob'
        })
          .then(res => {
            downloadFile(res, name)
          })
      },
      //获取审核附件
      getProjectAuditAccessory(data) {
        //获取附件信息
        request({
          url: '/project/projectAudit/getProjectAuditAccessory/' + data,
          method: 'get',
        })
          .then(res => {
            this.enclosureList = res;
          })
      },
      initPage() {
        let id = this.$route.query.id || this.id;
        if (id == '') return;
        request({
          url: '/project/projectAudit/' + id,
          method: 'get',
        })
          .then(res => {
            this.currentData = res || {};
          })
        //获取项目信息
        if (this.projectId == '') return;
        request({
          url: '/project/project/' + this.projectId,
          method: 'get',
        })
          .then(res => {
            if (res) {
              this.project = res;
            }
          })
        //获取结算信息
        this.getFileData();
        //获取审核记录
        this.getAuditPage();
      },
      getFileData() {
        //获取结算数据
        let data = {
          auditId: this.id,
          nodeType: 3
        }
        resultFileDetail(data).then(res => {
          if (res.length > 0) {
            this.resultFileList = res[0].sheetList;
            this.fileDetail = res[0];
          }
        })
      },
      getAuditPage() {
        this.auditLoading = true;
        let processInstanceId = this.record.processInstanceId;
        getHistoryTaskPage({
          current: this.auditPage,
          size: this.auditPageSize,
          //避免新建的审核返回审核记录，传一个不存在的id
          processInstanceId: processInstanceId == null || processInstanceId === ''?'0':processInstanceId
        })
          .then(res => {
            res.records.map(item => {
              item.formContent = item.formContent && JSON.parse(item.formContent);
              item.formData = item.formData && JSON.parse(item.formData);
            })
            this.auditRecords = res;
            this.auditLoading = false;
          })
          .catch(err => {
            this.auditLoading = false;
          })
      },
      auditPageChange(val) {
        this.auditPage = val;
        this.getAuditPage();
      },
      submitData(data, result) {
        if (this.radio == 1) {
          this.submit({
            examineState: this.radio,
            ...data
          }, result);
        } else {
          this.submit({
            examineState: this.radio
          });
        }
      }
    },
    mounted() {
      this.initPage();
      this.getProjectAuditAccessory(this.id)

    }
  }
</script>
<style scoped>
  table, table tr th, table tr td {
    border: 1px solid #dfe6ec;
  }

  table tr td {
    padding-left: 6px;
    height: 50px;
  }

  .label-width {
    width: 126px;
    text-align: right;
    padding-right: 6px;
  }

  table {
    width: 100%;
    min-height: 25px;
    line-height: 25px;
    text-align: left;
    border-collapse: collapse;
    padding: 2px;
  }

  .pg-container .table-class td.none-col .el-table__expand-icon--expanded {
    /*只能隐藏内容，隐藏整个的单元格要另想办法（3）*/
    display: none;
  }

  .pg-container .table-class td {
    /*注意是每个单元格有自己的下边框（4）*/
    border-bottom: unset;
  }

  .pg-container .table-class .el-table__expanded-cell {
    padding: 6px 10px 12px 10px;
    /*补上扩展行的下边框（4）*/
    border-bottom: 1px solid #ebeef5;
  }
</style>
