<template xmlns="http://www.w3.org/1999/html">
  <div class="content">
    <div class="header-title">
      <p class="title"> {{ details.title }} </p>
      <div class="bar-content">
        {{ formatCreateDate(details.createDate) }}&nbsp;·&nbsp;阅读&nbsp;{{
          readingCount
        }}&nbsp;·&nbsp;下载&nbsp;{{ downloadCount }}
      </div>
      <el-divider style="margin: 16px 0"></el-divider>
    </div>

    <div style="margin: auto;width: 45em">
      <div v-html="details.content" style="margin: auto;width: 45em"></div>
      <br/>
      <div v-if="fileList.length > 0">
        <strong style="font-size: 13px"> 附件: </strong>
      </div>
      <div>
        <template v-for="(item, key) in fileList">
          <div style="margin-left: 50px">
            <el-button type="text" @click="downLoad(item)">{{ item.name }}</el-button>
            <el-button type="text" size="mini" @click="previewAttachment(item)" plain style="float: right">预览</el-button>
          </div>
        </template>
      </div>
    </div>
    <reviewFile
      :reviewFile="reviewFile"
      :open-review="showReview"
      :closeReview="closeReview"
    >
    </reviewFile>
  </div>
</template>

<script>
import {getDetails, statistical, getStatistical} from './config/request'
import request from '@/utils/request'
import {downloadFile} from '@/utils/index'
import moment from "moment";
import reviewFile from "@/components/Review";
export default {
  data() {
    return {
      downloadCount: 0,
      readingCount: 0,
      details: {},
      fileList: [],
      reviewFile: {},
      showReview: false,
    }
  },
  components: {
    reviewFile
  },
  watch: {
    fileList(val) {
      //获取阅读下载数量
      this.getStatisticalAll(this.$route.query.id, val);
    }
  },
  methods: {
    // 预览文件
    previewAttachment(row) {
      this.reviewFile = row
      this.reviewFile.uri = row.url
      const arrFile = row.name.split(".");
      const fileType = arrFile[arrFile.length - 1].toLowerCase();
      const allowFileType = ['pdf', 'docx', 'xlsx', 'xls']
      if (allowFileType.indexOf(fileType) < 0) {
        this.$message.error('暂不支持该文件类型预览')
        return false
      }
      this.showReview = true
    },
    closeReview() {
      this.showReview = false
    },
    getStatisticalAll(noticeId, noticeFileList) {
      let fileIdList = noticeFileList.map(item => item.id);
      getStatistical({noticeId: noticeId, noticeFileIdList: fileIdList}).then(res => {
        this.readingCount = res[noticeId];
        let downCount = 0;
        this.fileList.forEach(item => {
          downCount += res[item.id]?res[item.id]:0;
        })
        this.downloadCount = downCount;
      });
    },
    statisticalAll(noticeId,noticeFileId){
      statistical({noticeId: noticeId, noticeFileId: noticeFileId}).then(item=>{
        //获取阅读下载数量
        this.getStatisticalAll(this.$route.query.id, this.fileList);
      })
    },
    formatCreateDate(date) {
      let year = moment(date).format('YYYY');
      let month = moment(date).format('MM');
      let day = moment(date).format('DD');
      let time = moment(date).format("HH:mm");
      return year + '年' + month + '月' + day + '日 ' + time;
    },
    initData() {
      getDetails({
        id: this.$route.query.id
      })
          .then(res => {
            this.fileList = res.fileList;
            this.details = res;
          })
    },
    downLoad(item) {
      request({
        url: '/general/file/download?uri=' + item.url,
        type: 'get',
        responseType: 'blob'
      })
          .then(res => {
            downloadFile(res, item.name)
          })
    }
  },
  created() {
    this.initData();
  },
  mounted() {
    this.statisticalAll(this.$route.query.id);
  }
}
</script>
<style scoped>
.header-title {
  line-height: 0.63;
}

.bar-content {
  letter-spacing: 0.3px;
  font-size: 0.80rem;
  color: #8a919f;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  flex-direction: column-reverse;
}

.content {
  width: 95%;
  margin: 0 auto;
  padding-bottom: 50px;
}

.title {
  width: 100%;
  text-align: center;
  padding-top: 20px;
  font-size: 20px;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
}
</style>
