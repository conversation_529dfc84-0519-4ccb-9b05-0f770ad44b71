export default {
  //获取某类型字段列表
  getFields(type) {
    return this.parames.filter(item => {
      return item.whereShow.some((citem) => citem == type)
    })
  },
  //加工页面formData
  getDefaultForm(data) {
    var json = {};
    data.map(item => {
      json[item.fieldName] = item.defaultVal;
    })
    return json;
  },
  //页面请求地址
  requestUrl: '/system/learningField',

  //页面字段参数，可根据实际需求扩展
  parames: [
    {
      label: '主键ID',
      fieldName: 'id',
      defaultVal: '',
      type: 'input',
      whereShow: ['']
    },
    {
      label: '标题',
      fieldName: 'title',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '附件上传',
      fieldName: 'fileName',
      defaultVal: '',
      type: 'file',
      whereShow: ['form', 'add', 'edit']
    },
    {
      label: '文件地址',
      fieldName: 'fileUri',
      defaultVal: '',
      type: 'fileUri',
      whereShow: ['']
    },
    {
      label: '发布人',
      fieldName: 'createUserName',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList','tableListList']
    },
    {
      label: '创建时间',
      fieldName: 'createDate',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },
  ]
}
