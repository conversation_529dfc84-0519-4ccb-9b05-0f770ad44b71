<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container" style="text-align: center;">

      <div style="margin-top: 5px;margin-bottom: 5px">
        <div align="left" style="float:left;display:inline-block;">
          <el-button type="primary" size="mini" style="margin-top: 5px" @click="exportMonthly">导出</el-button>
        </div>
        <div align="right">
        </div>
      </div>
    </div>
    <!--表格渲染-->
    <el-table ref="table" v-loading="crud.loading" default-expand-all :data="summaryData" row-key="userId" empty-text="导出" border>
      <template v-for="(item, key) in tableListFields">
        <el-table-column :label="item.label" :prop="item.fieldName" :key="key" align="center" header-align="center"
                         :width="item.size" :show-overflow-tooltip="true" :fixed="item.fixed">

        </el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script>
  import crudDept from './config/request'
  import '@riophae/vue-treeselect/dist/vue-treeselect.css'
  import CRUD, {presenter,form} from '@crud/crud'
  import {downloadFile} from '@/utils/index'
  //注入配置文件
  import config from './config/index'

  const formFields = config.getFields('form');
  const tableListFields = config.getFields('tableList');
  const defaultForm = config.getDefaultForm(formFields);
  export default {
    name: 'userSummary',
    components: {},
    cruds() {
      return CRUD({
        title: '用户汇总', url: config.requestUrl, crudMethod: {...crudDept},
        optShow: {add: false, edit: false, del: false, reset: false},
      })
    },
    mixins: [presenter(),form(defaultForm)],
    // 设置数据字典
    dicts: [],
    data() {
      return {
        formFields: formFields,
        tableListFields: tableListFields,
        companyList: [],
        summaryData: [],
      }
    },
    mounted() {
      // this.getPageData()
    },
    methods: {
      getPageData(){
        crudDept.getPage().then(res=>{
          this.summaryData = res;
        })
      },
      [CRUD.HOOK.beforeRefresh](){
        return false;
      },
        exportMonthly() {
          let title = "用户汇总.xlsx"
          crudDept.exportUserSummaryExcel().then(res => {
            downloadFile(res, title)
          })
        },
    }
  }
</script>

<style scoped>
</style>
