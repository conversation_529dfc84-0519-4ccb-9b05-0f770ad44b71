import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;

export function getPage() {
    return request({
        url: requestUrl+'/getUserSummary',
        method: 'get'
    })
}


export function exportUserSummaryExcel(params) {
  return request({
    url: requestUrl+'/exportUserSummaryExcel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}



export default { getPage,exportUserSummaryExcel }
