export default {
  //获取某类型字段列表
  getFields(type) {
    let arr = [];
    this.parames.forEach((value) => {
      if (value.whereShow.some((item) => item === type)) {
        let data = JSON.parse(JSON.stringify(value));
        if (value.children) {
          data.children = [];
          value.children.forEach(item => {
            if (item.whereShow.some((item) => item === type)) {
              data.children.push(item);
            }
          })
        }
        arr.push(data)
      }
    })
    return arr;
  },
  //加工页面formData
  getDefaultForm(data) {
    var json = {};
    data.map(item => {
      json[item.fieldName] = item.defaultVal;
    })
    return json;
  },
  //页面请求地址
  requestUrl: '/complex/query',

  //页面字段参数，可根据实际需求扩展
  parames: [
    {
      label: '所属二级公司',
      fieldName: 'secondaryCompany',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '所属项目公司',
      fieldName: 'projectCompany',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '项目创建数量',
      fieldName: 'projectCount',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '岗位',
      fieldName: 'job',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '姓名',
      fieldName: 'userName',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '电话',
      fieldName: 'phone',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    }
  ]
}
