export default {
  //获取某类型字段列表
  getFields(type){
    return  this.parames.filter(item=>{
      return item.whereShow.some((citem)=>citem == type)
    })
  },
  //加工页面formData
  getDefaultForm(data){
    var json = {};
    data.map(item=>{
      json[ item.fieldName ] = item.defaultVal;
    })
    return json;
  },

  //页面字段参数，可根据实际需求扩展
  parames: [
    {
      label: '项目名称',
      fieldName: 'projectName',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '报审金额（万元）',
      fieldName: 'buildTotalInvestmentAmount',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    },
    {
      label: '审定金额（万元）',
      fieldName: 'resultAmount',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList', 'form', 'add', 'edit']
    }
  ]
}
