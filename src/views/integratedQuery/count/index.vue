<template>
  <div class="app-container">
    <el-form ref="form" :model="query" size="small" inline style="margin-top: 20px">
      <el-form-item label="部门名称" label-width="200px">
        <el-cascader
          style="width: 179px"
          v-model="query.deptOrgCode"
          :options="deptList"
          @change="queryUser"
          :props="{ multiple: false,
                    value: 'code',
                    label: 'name',
                    children: 'children',
                    checkStrictly: true,
                    emitPath: false}"
          clearable>
        </el-cascader>
      </el-form-item>
      <el-form-item label="姓名" label-width="200px">
        <el-select v-model="query.userId" placeholder="请选择" style="width: 179px" clearable @change="queryCommit">
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="项目性质" label-width="200px">
        <el-select v-model="query.projectNatureCd" placeholder="请选择" style="width: 179px" clearable
                   @change="queryCommit">
          <el-option
            v-for="(item) in dict.project_nature"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="审核性质" label-width="200px">
        <el-select v-model="query.auditNatureCd" placeholder="请选择" style="width: 179px" clearable
                   @change="queryCommit">
          <el-option
            v-for="(item) in dict.audit_nature"
            :key="item.code"
            :disabled="item.disabled"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="建设单位" label-width="200px">
        <el-select
          v-model="query.buildThreeLevelOrg"
          placeholder="请选择"
          style="width: 179px"
          @change="queryCommit"
          clearable>
          <el-option
            v-for="(item) in buildThreeLevelOrg"
            :key="item.code"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="项目名称" label-width="200px">
        <el-input
          @change="queryCommit"
          clearable
          v-model="query.projectName"
          :maxlength="50"
          style="width: 179px"></el-input>
      </el-form-item>

      <el-form-item label="项目类别" label-width="200px">
        <el-select v-model="query.projectCategory1Cd" placeholder="请选择"
                   clearable @change="queryCommit">
          <el-option
            v-for="(item) in dict.project_category_one"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="" label-width="200px">
        <el-select v-model="query.projectCategory2Cd" placeholder="请选择"
                   clearable @change="queryCommit">
          <el-option
            v-for="(item) in dict.project_types"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="起止时间" label-width="200px">
        <el-date-picker
          @change="queryCommit"
          v-model="query.createTime"
          :default-time="['00:00:00','23:59:59']"
          type="daterange"
          range-separator=":"
          size="small"
          class="date-item"
          value-format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>

    </el-form>
    <div slot="footer" style="margin-right: 20px" align="right">
      <el-button type="primary" @click="queryCommit">统计</el-button>
      <el-button type="primary" @click="queryCommitList">查看结果</el-button>
    </div>
    <!--表格渲染-->
    <el-table ref="table"
              :data="integratedResult"
              show-summary
              highlight-current-row
              border>
      <el-table-column label="序号" type="index" align="center"/>
      <template v-for="(item, key) in tableListFields">
        <el-table-column :label="item.label" :prop="item.fieldName" :key="key" align="center" :width="item.size">
          <template v-slot="scope">
            <template>
              {{ scope.row[item.fieldName] }}
            </template>
          </template>
        </el-table-column>
      </template>
    </el-table>

    <!--审核列表-->
    <el-dialog
      title="审核信息"
      :visible.sync="queryDialogVisible"
      width="80%"
    >
      <el-table ref="table" default-expand-all :data="audit.auditRecords.records" row-key="id" border>
        <template v-for="(item, key) in auditTableListFields">
          <el-table-column :label="item.label" :prop="item.fieldName" :key="key" width="155" align="center">
            <template v-slot="scope">
              <template v-if="item.fieldName === 'projectTypeCd'">
                {{ showCurrentData(dict.engineering_types, scope.row[item.fieldName]) }}
              </template>
              <template v-if="item.fieldName === 'auditStatus'">
                {{ showCurrentData(dict.audit_status, scope.row[item.fieldName]) }}
              </template>
              <template v-else-if="item.fieldName === 'projectNatureCd'">
                {{ showCurrentData(dict.project_nature, scope.row[item.fieldName]) }}
              </template>
              <template v-else-if="item.fieldName === 'auditNatureCd'">
                {{ showCurrentData(dict.audit_nature, scope.row[item.fieldName]) }}
              </template>
              <template v-else-if="item.fieldName === 'ifResultBefore'">
                {{ showCurrentData(dict.if_result_before, scope.row[item.fieldName]) }}
              </template>
              <template v-else>
                {{ scope.row[item.fieldName] }}
              </template>
            </template>
          </el-table-column>
        </template>
      </el-table>
      <el-pagination
        :page-size="audit.auditPageSize"
        :current-page="audit.auditPage"
        layout="prev, pager, next"
        @current-change="auditPageChange"
        :total="audit.auditRecords.total">
      </el-pagination>
    </el-dialog>

  </div>
</template>

<script>
import {
  getPage as getAuditPage,
  getAuditResult,
  getQueryPage
} from "../../projectManagement/projectAudit/config/request";
import auditConfig from "../../projectManagement/projectAudit/config";
import {getTree} from "../../user/dept/config/request"
import {userList} from "../../user/user/config/request"
import config from "./config"
import {mapGetters} from "vuex";
import crudDept from "../../projectManagement/projectInfo/config/request";

const auditTableListFields = auditConfig.getFields('tableList');
const tableListFields = config.getFields('tableList');
export default {
  dicts: [, 'project_category_one', 'project_types', 'engineering_types', 'project_nature', 'audit_nature', 'if_result_before', 'audit_status'],
  computed: {
    ...mapGetters([
      'user',
    ])
  },
  data() {
    return {
      buildThreeLevelOrg: [],
      tableListFields: tableListFields,
      auditTableListFields: auditTableListFields,
      deptList: [],
      userList: [],
      queryDialogVisible: false,
      query: {},
      integratedResult: [],
      audit: {
        auditRecords: {},
        auditPageSize: 10,
        auditPage: 1,
      },
    }
  },
  mounted() {
    this.getBuildThreeLevelOrg();
    getTree({
      'orgCodeRight': this.user.deptCode
    }).then(res => {
      this.deptList = this.getTreeData(res);
    })
  },

  methods: {
    getBuildThreeLevelOrg() {
      crudDept.getBuildThreeLevelOrg({})
        .then(res => {
          this.buildThreeLevelOrg = res;
        })
    },
    queryCommit() {
      getAuditResult(this.query).then(res => {
        this.integratedResult = res
      })
    },
    queryUser(data) {
      userList(data).then(res => {
        this.userList = res;
      })
      this.queryCommit();
    },
    // 递归判断列表，把最后的children设为undefined
    getTreeData(data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          // children若为空数组，则将children设为undefined
          data[i].children = undefined;
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          this.getTreeData(data[i].children);
        }
      }
      return data;
    },
    queryCommitList() {
      this.queryDialogVisible = true
      this.audit.auditPage = 1;
      this.getAuditPage();
    },
    auditPageChange(val) {
      this.audit.auditPage = val;
      this.getAuditPage();
    },
    getAuditPage() {
      //获取审核列表
      getQueryPage({
        current: this.audit.auditPage,
        size: this.audit.auditPageSize,
        ...this.query,
        type: 1
      })
        .then(res => {
          this.audit.auditRecords = res
        })
    },
  }

}
</script>

