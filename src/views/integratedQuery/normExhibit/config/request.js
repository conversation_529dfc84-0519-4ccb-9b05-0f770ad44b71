import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;

export function getProjectLeftTree(params) {
  return request({
    url: requestUrl+'/getProjectLeftTree',
    method: 'get',
    params
  })
}

export function getNormExhibitAll(params) {
  return request({
    url: requestUrl+'/getNormExhibitAll',
    method: 'get',
    params
  })
}

export default { getProjectLeftTree, getNormExhibitAll }
