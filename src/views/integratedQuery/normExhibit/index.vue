<template>
  <div class="app-container">
    <el-row :gutter="15">
      <!--左侧树-->
      <el-col :xs="9" :sm="6" :md="4" :lg="4" :xl="4">
        <div class="tree-menu">
          <el-tree
            :data="projects"
            :props="defaultProps"
            :expand-on-click-node="false"
            accordion
            @node-click="handleNodeClick"
            highlight-current>
            <template v-slot="{ node, data }">
              <span class="custom-tree-node" style="font-size: 12px" >
                <!-- <el-tooltip class="item" effect="dark" :content="node.label" placement="top-start">-->
                <span :title="node.label">{{ node.label }}</span>
                <!--  </el-tooltip>-->
              </span>
            </template>
          </el-tree>
        </div>

      </el-col>
      <el-col :xs="10" :sm="18" :md="17" :lg="17" :xl="17">
        <h3 style="text-align:center;margin-left:200px">{{tableTitle}}
          <span class="dwClass">单位：万元</span>
        </h3>
        <div style="height:100%;position:fixed;width: 70%">
          <iframe allowtransparency="true" v-if='!isShow'  id="iframe" :src="pdfUrl+'#toolbar=0'" frameborder="no" border="0"  marginheight="0" scrolling="no"></iframe>
        </div>
        <el-table
          v-if='isShow'
          highlight-current-row
          border
          :data="tableData"
          style="width: 100%;margin-bottom: 20px;margin-left:100px"
          row-key="id"
          :default-expand-all="false"
        >
          <el-table-column
            header-align="center"
            show-overflow-tooltip
            :key="col.prop"
            :label="col.name"
            :prop="col.prop"
            v-for="col in columnData">
            <el-table-column
              header-align="center"
              show-overflow-tooltip
              :key="s_col.prop"
              :label="s_col.name"
              :prop="s_col.prop"
              v-for="s_col in col.descendants">
            </el-table-column>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
  </div>
</template>

<script>


  import crudRequest from './config/request'
  import CRUD from '@crud/crud'
  import crudOperation from '@crud/CRUD.operation'
  import pagination from '@crud/Pagination'
  import '@riophae/vue-treeselect/dist/vue-treeselect.css'
  //注入配置文件
  import config from './config'

  const getProjectLeftTree = crudRequest.getProjectLeftTree;
  const getNormExhibitAll = crudRequest.getNormExhibitAll;

  const formFields = config.getFields('form');
  const tableListFields = config.getFields('tableList');
  export default {
    name: 'simpleList',
    components: {crudOperation, pagination},
    cruds() {
      return CRUD({title: '指标展示', url: config.requestUrl, crudMethod: {...crudRequest}})
    },
    // 设置数据字典
    data() {
      return {
        formFields: formFields,
        tableListFields: tableListFields,
        rules: {
          name: [
            {required: true, message: '请输入名称', trigger: 'blur'}
          ]
        },
        permission: {},
        enabledTypeOptions: [
          {key: 'true', display_name: '正常'},
          {key: 'false', display_name: '禁用'}
        ],
        defaultProps: {
          children: 'children',
          label: 'name'
        },
        projects: [],
        tableData: [],
        columnData: [],
        tableTitle: '',
        pdfUrl: '/file-src/zbzs.pdf',
        isShow: true
      }
    },
    created() {
      this.$nextTick(() => {
        this.getTree();
      })
    },
    mounted() {
    },
    methods: {
      getTree() {
        getProjectLeftTree().then(res => {
          this.projects = res;
        })
      },
      handleNodeClick(data, node) {
        this.columnData = undefined;
        this.tableData = undefined;
        this.tableTitle = undefined;
        if (data.children.length === 0) {
          let obj = {}
          let param = {"year": null, "chapterId": null}
          //获取二级节点
          for (let i = 0; i < node.level - 2; i++) {
            if (!i) {
              obj = node.parent
            } else {
              obj = obj.parent
            }
          }
          param.year = obj.data.name
          this.tableTitle = node.data.name
          param.chapterId = node.data.id
          param.parentId = node.data.parentId
          getNormExhibitAll(param).then(res => {
            this.tableData = res.tableData;
            this.columnData = res.column;
          })
        }
        if (node.data.name === '2020' && node.parent.data.name === '城市燃气工程投资参考指标') {
          this.isShow = false;
        }else {
          this.isShow = true;

        }
      },
      [CRUD.HOOK.beforeToAdd](crud, record) {
      },
      [CRUD.HOOK.beforeSubmit](crud, record) {
      },
      [CRUD.HOOK.beforeToEdit](crud, record) {

      },
    }
  }
</script>
<style scoped>
  .custom-tree-node {
    font-size: 15px;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  >>> .el-tree-node__label {
    font-size: 12px !important;
  }
</style>

<style lang="css">
  .dwClass{
    color: red;
    font-size: 12px;
    position: absolute;
    right: 120px;
    top: 37px;
  }
  iframe{
    background-color: #0c0c0c;
    padding: 0px;
    width: 100%;
    height:85% ;
  }
  .el-tooltip__popper {
    font-size: 13px;
    max-width: 30%
  }
  .tree-menu{
    width: 280px;
    height: 100%;
    overflow: auto;

  }
  .el-tree {
    display: inline-block;
    min-width: 100%;
  }
</style>
