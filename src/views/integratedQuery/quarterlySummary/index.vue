<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container" style="text-align: center;">
      <div v-if="crud.props.searchToggle">
        <el-date-picker
          @change="queryData"
          size="small"
          class="filter-item"
          v-model="queryDate"
          type="monthrange"
          align="right"
          unlink-panels
          range-separator="-"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          :picker-options="pickerOptions"
          :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
        <span>
          <el-button class="filter-item" size="mini" type="success" icon="el-icon-search"
                     @click="queryData">搜索</el-button>
          <el-button v-if="crud.optShow.reset" class="filter-item" size="mini" type="warning"
                     icon="el-icon-refresh-left" @click="crud.resetQuery()">重置</el-button>
        </span>
      </div>
      <div style="margin-top: 5px;margin-bottom: 5px">
        <div align="left" style="float:left;display:inline-block;">
          <el-button type="primary" size="mini" style="margin-top: 5px" @click="exportMonthly">导出</el-button>
        </div>
        <div align="right">
          <crudOperation/>
        </div>
      </div>
    </div>
    <!--表格渲染-->
    <el-table ref="table" v-loading="crud.loading" :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
              default-expand-all :data="summaryData" row-key="id" @select="crud.selectChange"
              @select-all="crud.selectAllChange" @selection-change="crud.selectionChangeHandler" :height="470" border>
      <el-table-column
        type="index"
        width="100"
        label="序号">
      </el-table-column>
      <template v-for="(item, key) in tableListFields">
        <el-table-column :label="item.label" :prop="item.fieldName" :key="key" align="center" header-align="center"
                         :width="item.size" :show-overflow-tooltip="true" :fixed="item.fixed">
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script>
  import moment from "moment";
  import crudDept, {getCompany} from './config/request'
  import Treeselect from '@riophae/vue-treeselect'
  import '@riophae/vue-treeselect/dist/vue-treeselect.css'
  import CRUD, {presenter, header, form, crud} from '@crud/crud'
  import rrOperation from '@crud/RR.operation'
  import crudOperation from '@crud/CRUD.operation'
  import udOperation from '@crud/UD.operation'
  import pagination from '@crud/Pagination'
  import {downloadFile} from '@/utils/index'
  //注入配置文件
  import config from './config/index'

  const formFields = config.getFields('form');
  const tableListFields = config.getFields('tableList');
  const defaultForm = config.getDefaultForm(formFields);
  export default {
    name: 'simpleList',
    components: {Treeselect, crudOperation, rrOperation, udOperation, pagination},
    cruds() {
      return CRUD({
        title: '类目', url: config.requestUrl, crudMethod: {...crudDept},
        optShow: {add: false, edit: false, del: false, reset: true},
        query: {
          queryStartDate: moment().startOf('year').format('YYYY-MM-DD HH:mm:ss'),
          queryEndDate: moment().endOf('year').format('YYYY-MM-DD HH:mm:ss')
        },
      })
    },
    mixins: [presenter(), header(), form(defaultForm), crud()],
    // 设置数据字典
    dicts: ['companyType', 'quarterly'],
    data() {
      return {
        queryDate: [new Date(new Date().getFullYear(), 0, 1),new Date(new Date().getFullYear(), 11, 31)],
        pickerOptions: {
          shortcuts: [{
            text: '本年',
            onClick(picker) {
              const start = new Date(new Date().getFullYear(), 0);
              const end = new Date(new Date().getFullYear(), 11);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '本月',
            onClick(picker) {
              picker.$emit('pick', [new Date(), new Date()]);
            }
          }, {
            text: '第一季度',
            onClick(picker) {
              const start = new Date(new Date().getFullYear(), 0);
              const end = new Date(new Date().getFullYear(), 2);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '第二季度',
            onClick(picker) {
              const start = new Date(new Date().getFullYear(), 3);
              const end = new Date(new Date().getFullYear(), 5);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '第三季度',
            onClick(picker) {
              const start = new Date(new Date().getFullYear(), 6);
              const end = new Date(new Date().getFullYear(), 8);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '第四季度',
            onClick(picker) {
              const start = new Date(new Date().getFullYear(), 9);
              const end = new Date(new Date().getFullYear(), 11);
              picker.$emit('pick', [start, end]);
            }
          }]
        },
        formFields: formFields,
        tableListFields: tableListFields,
        companyList: [],
        summaryData: [],
      }
    },
    mounted() {
      this.queryData();
    },
    methods: {
      exportMonthly() {
        let startYear = moment(this.query.queryStartDate).format('YYYY');
        let startMont = moment(this.query.queryStartDate).format('M');
        let endYear = moment(this.query.queryEndDate).format('YYYY');
        let endMont = moment(this.query.queryEndDate).format('M');
        var title = "工程造价管理系统使用情况统计表(" + startYear + '年'+ startMont + '月' + '-' + endYear + '年'+ endMont + '月' + ").xlsx"
        crudDept.exportMonthly(this.query).then(res => {
          downloadFile(res, title)
        })
      },
      queryData() {
        if (this.queryDate.length > 1) {
          this.query.queryStartDate = moment(this.queryDate[0]).startOf('month').format('YYYY-MM-DD HH:mm:ss')
          this.query.queryEndDate = moment(this.queryDate[1]).endOf('month').format('YYYY-MM-DD HH:mm:ss')
        }
        crudDept.getSummary(this.query).then(res => {
          this.summaryData = res;
        })
      },
      getCompanyList(value) {
        getCompany({companyType: value, typeCd: 2}).then(res => {
          this.companyList = res;
        })
      },
    }
  }
</script>

<style scoped>
</style>
