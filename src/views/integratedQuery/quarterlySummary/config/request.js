import request from '@/utils/request'
import config from './index'
const requestUrl = config.requestUrl;

export function getPage(params) {
    return request({
        url: requestUrl+'/page',
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: requestUrl,
        method: 'post',
        data
    })
}

export function del(ids) {
    return request({
        url: requestUrl,
        method: 'delete',
        data: ids
    })
}

export function edit(data) {
    return request({
        url: requestUrl,
        method: 'put',
        data
    })
}

export function getCompany(params) {
  return request({
    url: '/user/org/allList',
    method: 'get',
    params
  })
}

export function summaryEcharts() {
  return request({
    url: '/complex/query/summaryEcharts',
    method: 'get'
  })
}

export function getSummary(params) {
    return request({
        url: '/complex/query/summary',
        method: 'get',
        params
    })
}

export function exportMonthly(params) {
  return request({
    url: '/complex/query/exportMonthly',
    method: 'get',
    params,
    responseType: 'blob'
  })
}



export default { add, edit, del, getPage,getCompany,getSummary,exportMonthly, summaryEcharts }
