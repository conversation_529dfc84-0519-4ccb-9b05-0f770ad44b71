export default {
  //获取某类型字段列表
  getFields(type) {
    let arr = [];
    this.parames.forEach((value) => {
      if (value.whereShow.some((item) => item === type)) {
        let data = JSON.parse(JSON.stringify(value));
        if (value.children) {
          data.children = [];
          value.children.forEach(item => {
            if (item.whereShow.some((item) => item === type)) {
              data.children.push(item);
            }
          })
        }
        arr.push(data)
      }
    })
    return arr;
  },
  //加工页面formData
  getDefaultForm(data) {
    var json = {};
    data.map(item => {
      json[item.fieldName] = item.defaultVal;
    })
    return json;
  },
  //页面请求地址
  requestUrl: '/general/messageRecord',

  //页面字段参数，可根据实际需求扩展
  parames: [
    {
      label: '公司名称',
      fieldName: 'name',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '项目创建数量',
      fieldName: 'projectCount',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '结算报审数量',
      fieldName: 'auditCount',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '报审完成数量',
      fieldName: 'completeCount',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '月报数量',
      fieldName: 'monthlyCount',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },
    {
      label: '登录次数',
      fieldName: 'numberOfLogins',
      defaultVal: '',
      type: 'input',
      whereShow: ['tableList']
    },
    // {
    //   label: '总数量',
    //   fieldName: 'sum',
    //   defaultVal: '',
    //   type: 'input',
    //   whereShow: ['tableList']
    // }
  ]
}
