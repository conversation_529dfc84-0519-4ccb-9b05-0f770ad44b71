import Layout from '../layout/index'

export default [
  {
    "name": "fileTemplate",
    "path": "/fileTemplate",
    "hidden": false,
    "redirect": "noredirect",
    "component": Layout,
    "alwaysShow": true,
    "meta": {
      "title": "模板管理",
      "icon": "system",
      "noCache": true
    },
    "children": [
      {
        "name": "fileTemplateChildren",
        "path": "fileTemplateChildren",
        "hidden": true,
        "component": (resolve) => require(['@/views/fileTemplate/index'], resolve),
        "matchingComponentName": "fileTemplate/index",
        "meta": {
          "title": "模板管理",
          "icon": "peoples",
          "noCache": true
        }
      },
    ]
  },
  {
    "name": "normExhibit",
    "path": "/normExhibit",
    "hidden": false,
    "redirect": "noredirect",
    "component": Layout,
    "alwaysShow": true,
    "meta": {
      "title": "指标浏览",
      "icon": "system",
      "noCache": true
    },
    "children": [
      {
        "name": "normExhibitChildren",
        "path": "normExhibitChildren",
        "hidden": true,
        "component": (resolve) => require(['@/views/integratedQuery/normExhibit/index'], resolve),
        "matchingComponentName": "integratedQuery/normExhibit/index",
        "meta": {
          "title": "指标浏览",
          "icon": "peoples",
          "noCache": true
        }
      },
    ]
  },
  {
    "name": "costInformation",
    "path": "/costInformation",
    "hidden": false,
    "redirect": "noredirect",
    "component": Layout,
    "alwaysShow": true,
    "meta": {
      "title": "造价信息",
      "icon": "system",
      "noCache": true
    },
    "children": [
      {
        "name": "normExhibit",
        "path": "normExhibit",
        "hidden": true,
        "component": (resolve) => require(['@/views/integratedQuery/normExhibit/index'], resolve),
        "matchingComponentName": "integratedQuery/normExhibit/index",
        "meta": {
          "title": "指标浏览",
          "icon": "peoples",
          "noCache": true
        }
      },
      {
        "name": "organizeCommunication",
        "path": "organizeCommunication",
        "hidden": false,
        "component": (resolve) => require(['@/views/organizeCommunication/index'], resolve),
        "matchingComponentName": "organizeCommunication/index",
        "meta": {
          "title": "企业通知",
          "icon": "peoples",
          "noCache": true
        }
      },
      {
        "name": "announce",
        "path": "announce",
        "hidden": false,
        "component": (resolve) => require(['@/views/notice/announce/index'], resolve),
        "matchingComponentName": "notice/announce/index",
        "meta": {
          "title": "政策通知",
          "icon": "peoples",
          "noCache": true
        }
      }, {
        "name": "learningField",
        "path": "learningField",
        "hidden": false,
        "component": (resolve) => require(['@/views/learningField/index'], resolve),
        "matchingComponentName": 'learningField/index',
        "meta": {
          "title": "学习园地",
          "icon": "peoples",
          "noCache": true
        }
      }, {
        "name": "pricingBasis",
        "path": "pricingBasis",
        "hidden": false,
        "component": (resolve) => require(['@/views/organizeCommunication/index'], resolve),
        "matchingComponentName": "organizeCommunication/index",
        "meta": {
          "title": "计价依据",
          "icon": "peoples",
          "noCache": true
        }
      },
      {
        "name": "standardSpecification",
        "path": "standardSpecification",
        "hidden": false,
        "component": (resolve) => require(['@/views/organizeCommunication/index'], resolve),
        "matchingComponentName": "organizeCommunication/index",
        "meta": {
          "title": "标准规范",
          "icon": "peoples",
          "noCache": true
        }
      },
    ]
  },
  {
    "name": "companyManagement",
    "path": "/companyManagement",
    "hidden": false,
    "redirect": "noredirect",
    "component": Layout,
    "alwaysShow": true,
    "meta": {
      "title": "公司管理",
      "icon": "system",
      "noCache": true
    },
    "children": [{
      "name": "projectCompany",
      "path": "projectCompany",
      "hidden": false,
      "component": (resolve) => require(['@/views/companyManagement/projectCompany/index'], resolve),
      "matchingComponentName": 'companyManagement/projectCompany/index',
      "meta": {
        "title": "项目公司",
        "icon": "peoples",
        "noCache": true
      }
    },

      {
        "name": "costCenter",
        "path": "costCenter",
        "hidden": false,
        "component": (resolve) => require(['@/views/companyManagement/projectCompany/index'], resolve),
        "matchingComponentName": 'companyManagement/projectCompany/index',
        "meta": {
          "title": "造价中心",
          "icon": "peoples",
          "noCache": true
        }
      },
      {
        "name": "secondaryUnits",
        "path": "secondaryUnits",
        "hidden": false,
        "component": (resolve) => require(['@/views/companyManagement/projectCompany/index'], resolve),
        "matchingComponentName": 'companyManagement/projectCompany/index',
        "meta": {
          "title": "二级单位",
          "icon": "peoples",
          "noCache": true
        }
      },

    ]
  },
  {
    "name": "projectManagement",
    "path": "/projectManagement",
    "hidden": false,
    "redirect": "noredirect",
    "component": Layout,
    "alwaysShow": true,
    "meta": {
      "title": "项目管理",
      "icon": "system",
      "noCache": true
    },
    "children": [{
      "name": "projectInfo",
      "path": "projectInfo",
      "hidden": false,
      "component": (resolve) => require(['@/views/projectManagement/projectInfo/index'], resolve),
      "matchingComponentName": 'projectManagement/projectInfo/index',
      "meta": {
        "title": "项目概况",
        "icon": "peoples",
        "noCache": true
      }
    },
      {
        "name": "budget",
        "path": "budget",
        "hidden": false,
        "component": (resolve) => require(['@/views/compare/budget/index'], resolve),
        "matchingComponentName": "compare/budget/index",
        "meta": {
          "title": "概算数据",
          "icon": "peoples",
          "noCache": true
        }
      },
    ]
  },
  // {
  //   "name": "auditManagement",
  //   "path": "/auditManagement",
  //   "hidden": false,
  //   "redirect": "noredirect",
  //   "component": Layout,
  //   "alwaysShow": true,
  //   "meta": {
  //     "title": "审核管理",
  //     "icon": "peoples",
  //     "noCache": true
  //   },
  //   "children": []
  // },
  {
    "name": "workflow",
    "path": "/workflow",
    "hidden": false,
    "redirect": "noredirect",
    "component": Layout,
    "alwaysShow": true,
    "meta": {
      "title": "工作流管理",
      "icon": "system",
      "noCache": true
    },
    "children": [
      {
        "name": "workflow",
        "path": "workflow",
        "hidden": false,
        "component": (resolve) => require(['@/views/workflow/workflow/index'], resolve),
        "matchingComponentName": 'workflow/workflow/index',
        "meta": {
          "title": "流程",
          "icon": "peoples",
          "noCache": true
        }
      },
      {
        "name": "workflowCategory",
        "path": "workflowCategory",
        "hidden": false,
        "component": (resolve) => require(['@/views/workflow/workflowCategory/index'], resolve),
        "matchingComponentName": 'workflow/workflowCategory/index',
        "meta": {
          "title": "类目",
          "icon": "peoples",
          "noCache": true
        }
      },
      {
        "name": "workflowForm",
        "path": "workflowForm",
        "hidden": false,
        "component": (resolve) => require(['@/views/workflow/workflowForm/index'], resolve),
        "matchingComponentName": 'workflow/workflowForm/index',
        "meta": {
          "title": "表单",
          "icon": "peoples",
          "noCache": true
        }
      },

    ]
  },
  {
    "name": "系统管理",
    "path": "/system",
    "hidden": false,
    "redirect": "noredirect",
    "component": Layout,
    "alwaysShow": true,
    "meta": {
      "title": "系统管理",
      "icon": "system",
      "noCache": true
    },
    "children": [{
      "name": "synchronous",
      "path": "synchronous",
      "hidden": false,
      "component": (resolve) => require(['@/views/system/synchronous/index'], resolve),
      "matchingComponentName": 'system/synchronous/index',
      "meta": {
        "title": "同步数据",
        "icon": "menu",
        "noCache": true
      }
    },
      {
        "name": "systemLog",
        "path": "systemLog",
        "hidden": false,
        "component": (resolve) => require(['@/views/system/log/index'], resolve),
        "matchingComponentName": 'system/log/index',
        "meta": {
          "title": "操作日志",
          "icon": "menu",
          "noCache": true
        }
      },
      {
        "name": "onlineUser",
        "path": "onlineUser",
        "hidden": false,
        "component": (resolve) => require(['@/views/monitor/online/index'], resolve),
        "matchingComponentName": 'monitor/online/index',
        "meta": {
          "title": "在线用户",
          "icon": "menu",
          "noCache": true
        }
      },
      {
        "name": "Menu",
        "path": "menu",
        "hidden": false,
        "component": (resolve) => require(['@/views/system/menu/index'], resolve),
        "matchingComponentName": 'system/menu/index',
        "meta": {
          "title": "菜单管理",
          "icon": "menu",
          "noCache": true
        }
      },
      {
        "name": "Role",
        "path": "role",
        "hidden": false,
        "component": (resolve) => require(['@/views/system/role/index'], resolve),
        "matchingComponentName": 'system/role/index',
        "meta": {
          "title": "系统角色",
          "icon": "dictionary",
          "noCache": true
        }
      },
      {
        "name": "Dict",
        "path": "dict",
        "hidden": false,
        "component": (resolve) => require(['@/views/system/dict/index'], resolve),
        "matchingComponentName": 'system/dict/index',
        "meta": {
          "title": "字典管理",
          "icon": "dictionary",
          "noCache": true
        }
      }, {
        "name": "Config",
        "path": "config",
        "hidden": false,
        "component": (resolve) => require(['@/views/system/config/index'], resolve),
        "matchingComponentName": 'system/config/index',
        "meta": {
          "title": "配置管理",
          "icon": "dictionary",
          "noCache": true
        }
      }, {
        "name": "Timing",
        "path": "timing",
        "hidden": false,
        "component": (resolve) => require(['@/views/system/schedule/index'], resolve),
        "matchingComponentName": 'system/dictItem/index',
        "meta": {
          "title": "任务调度",
          "icon": "timing",
          "noCache": true
        }
      },
      {
        "name": "structuring",
        "path": "structuring",
        "hidden": false,
        "component": (resolve) => require(['@/views/system/structuring/index'], resolve),
        "matchingComponentName": "system/structuring/index",
        "meta": {
          "title": "组织构建",
          "icon": "timing",
          "noCache": true
        }
      }
    ]
  },
  {
    "name": "用户管理",
    "path": "/user",
    "hidden": false,
    "redirect": "noredirect",
    "component": Layout,
    "alwaysShow": true,
    "meta": {
      "title": "用户管理",
      "icon": "system",
      "noCache": true
    },
    "children": [{
      "name": "User",
      "path": "user",
      "hidden": false,
      "component": (resolve) => require(['@/views/user/user/index'], resolve),
      "matchingComponentName": "user/user/index",
      "meta": {
        "title": "用户管理",
        "icon": "peoples",
        "noCache": true
      }
    },
      {
        "name": "Role",
        "path": "role",
        "hidden": false,
        "component": (resolve) => require(['@/views/user/role/index'], resolve),
        "matchingComponentName": "user/role/index",
        "meta": {
          "title": "角色管理",
          "icon": "role",
          "noCache": true
        }
      }, {
        "name": "Dept",
        "path": "dept",
        "hidden": false,
        "component": (resolve) => require(['@/views/user/dept/index'], resolve),
        "matchingComponentName": "user/dept/index",
        "meta": {
          "title": "部门管理",
          "icon": "dept",
          "noCache": true
        }
      }, {
        "name": "Position",
        "path": "position",
        "hidden": false,
        "component": (resolve) => require(['@/views/user/position/index'], resolve),
        "matchingComponentName": "user/position/index",
        "meta": {
          "title": "岗位管理",
          "icon": "Steve-Jobs",
          "noCache": true
        }
      }]
  },
  {
    "name": "任务管理",
    "path": "/taskManagement",
    "hidden": false,
    "redirect": "noredirect",
    "component": Layout,
    "alwaysShow": true,
    "meta": {
      "title": "任务管理",
      "icon": "system",
      "noCache": true
    },
    "children": [{
      "name": "TaskPool",
      "path": "taskPool",
      "hidden": false,
      "component": (resolve) => require(['@/views/taskManagement/taskPool/index'], resolve),
      "matchingComponentName": "taskManagement/taskPool/index",
      "meta": {
        "title": "待办任务池",
        "icon": "peoples",
        "noCache": true
      }
    },
      {
        "name": "HistoryTaskPool",
        "path": "historyTaskPool",
        "hidden": false,
        "component": (resolve) => require(['@/views/taskManagement/historyTaskPool/index'], resolve),
        "matchingComponentName": "taskManagement/historyTaskPool/index",
        "meta": {
          "title": "历史任务池",
          "icon": "peoples",
          "noCache": true
        }
      }
    ]
  },
  {
    "name": "我的任务",
    "path": "/myTask",
    "hidden": false,
    "redirect": "noredirect",
    "component": Layout,
    "alwaysShow": true,
    "meta": {
      "title": "我的任务",
      "icon": "system",
      "noCache": true
    },
    "children": [{
      "name": "MyAgendaTask",
      "path": "myAgendaTask",
      "hidden": false,
      "component": (resolve) => require(['@/views/myTask/myAgendaTask/index'], resolve),
      "matchingComponentName": "myTask/myAgendaTask/index",
      "meta": {
        "title": "待办任务",
        "icon": "peoples",
        "noCache": true
      }
    }, {
      "name": "MyClaimTask",
      "path": "myClaimTask",
      "hidden": false,
      "component": (resolve) => require(['@/views/myTask/myClaimTask/index'], resolve),
      "matchingComponentName": "myTask/myClaimTask/index",
      "meta": {
        "title": "签收任务",
        "icon": "peoples",
        "noCache": true
      }
    }, {
      "name": "MyFinishTask",
      "path": "myFinishTask",
      "hidden": false,
      "component": (resolve) => require(['@/views/myTask/myFinishTask/index'], resolve),
      "matchingComponentName": "myTask/myFinishTask/index",
      "meta": {
        "title": "已完任务",
        "icon": "peoples",
        "noCache": true
      }
    }
    ]
  },
  {
    "name": "projectMonthlyReport",
    "path": "/projectMonthlyReport",
    "hidden": false,
    "redirect": "noredirect",
    "component": Layout,
    "alwaysShow": true,
    "meta": {
      "title": "项目月报",
      "icon": "system",
      "noCache": true
    },
    "children": [{
      "name": "projectMonthlyReport",
      "path": "projectMonthlyReport",
      "hidden": false,
      "component": (resolve) => require(['@/views/projectMonthlyReport/projectMonthlyReport/index'], resolve),
      "matchingComponentName": "projectMonthlyReport/projectMonthlyReport/index",
      "meta": {
        "title": "月报提交",
        "icon": "peoples",
        "noCache": true
      }
    },
      {
        "name": "monthlyManagement",
        "path": "monthlyManagement",
        "hidden": false,
        "component": (resolve) => require(['@/views/projectMonthlyReport/monthlyManagement/index'], resolve),
        "matchingComponentName": "projectMonthlyReport/monthlyManagement/index",
        "meta": {
          "title": "月报管理",
          "icon": "peoples",
          "noCache": true
        }
      },
      {
        "name": "summaryMonthlyReport",
        "path": "summaryMonthlyReport",
        "hidden": false,
        "component": (resolve) => require(['@/views/projectMonthlyReport/summaryMonthlyReport/index'], resolve),
        "matchingComponentName": "projectMonthlyReport/summaryMonthlyReport/index",
        "meta": {
          "title": "月报汇总",
          "icon": "peoples",
          "noCache": true
        }
      }
    ]
  },
  {
    "name": "qualificationManagement",
    "path": "/qualificationManagement",
    "hidden": false,
    "redirect": "noredirect",
    "component": Layout,
    "alwaysShow": true,
    "meta": {
      "title": "资质管理",
      "icon": "system",
      "noCache": true
    },
    "children": [{
      "name": "enterpriseQualification",
      "path": "enterpriseQualification",
      "hidden": false,
      "component": (resolve) => require(['@/views/qualificationManagement/enterpriseQualification/index'], resolve),
      "matchingComponentName": "qualificationManagement/enterpriseQualification/index",
      "meta": {
        "title": "单位资质",
        "icon": "peoples",
        "noCache": true
      }
    },
      {
        "name": "personalQualification",
        "path": "personalQualification",
        "hidden": false,
        "component": (resolve) => require(['@/views/qualificationManagement/personalQualification/index'], resolve),
        "matchingComponentName": "qualificationManagement/personalQualification/index",
        "meta": {
          "title": "人员资质",
          "icon": "peoples",
          "noCache": true
        }
      },
      {
        "name": "personContactManager",
        "path": "personContactManager",
        "hidden": false,
        "component": (resolve) => require(['@/views/qualificationManagement/personContactManager/index'], resolve),
        "matchingComponentName": "qualificationManagement/personContactManager/index",
        "meta": {
          "title": "人员通讯录",
          "icon": "peoples",
          "noCache": true
        }
      },
      {
        "name": "companyQualificationReport",
        "path": "companyQualificationReport",
        "hidden": false,
        "component": (resolve) => require(['@/views/qualificationManagement/companyQualificationReport/index'], resolve),
        "matchingComponentName": "qualificationManagement/companyQualificationReport/index",
        "meta": {
          "title": "单位考核汇总",
          "icon": "peoples",
          "noCache": true
        }
      }
    ]
  },

  {
    "name": "messageCenter",
    "path": "/messageCenter",
    "hidden": false,
    "redirect": "noredirect",
    "component": Layout,
    "alwaysShow": true,
    "meta": {
      "title": "消息中心",
      "icon": "system",
      "noCache": true
    },
    "children": [{
      "name": "messageRecord",
      "path": "messageRecord",
      "hidden": false,
      "component": (resolve) => require(['@/views/messageCenter/messageRecord/index'], resolve),
      "matchingComponentName": "messageCenter/messageRecord/index",
      "meta": {
        "title": "消息记录",
        "icon": "peoples",
        "noCache": true
      }
    }

    ]
  },
  {
    "name": "notice",
    "path": "/notice",
    "hidden": false,
    "redirect": "noredirect",
    "component": Layout,
    "alwaysShow": true,
    "meta": {
      "title": "公告",
      "icon": "system",
      "noCache": true
    },
    "children": [
      {
        "name": "announceList",
        "path": "announceList",
        "hidden": false,
        "component": (resolve) => require(['@/views/notice/announce/list'], resolve),
        "matchingComponentName": "notice/announce/list",
        "meta": {
          "title": "公告列表",
          "icon": "peoples",
          "noCache": true
        }
      }

    ]
  },
  {
    "name": "compare",
    "path": "/compare",
    "hidden": false,
    "redirect": "noredirect",
    "component": Layout,
    "alwaysShow": true,
    "meta": {
      "title": "结算审核",
      "icon": "system",
      "noCache": true
    },
    "children": [
      // {
      //   "name": "contract",
      //   "path": "contract",
      //   "hidden": false,
      //   "component": (resolve) => require(['@/views/compare/contract/index'], resolve),
      //   "matchingComponentName": "compare/contract/index",
      //   "meta": {
      //     "title": "合同维护",
      //     "icon": "peoples",
      //     "noCache": true
      //   }
      // },
      {
        "name": "projectAudit",
        "path": "projectAudit",
        "hidden": false,
        "component": (resolve) => require(['@/views/projectManagement/projectAudit/index'], resolve),
        "matchingComponentName": 'projectManagement/projectAudit/index',
        "meta": {
          "title": "审核管理",
          "icon": "peoples",
          "noCache": true
        }
      },
      {
        "name": "addSettle",
        "path": "addSettle",
        "hidden": false,
        "component": (resolve) => require(['@/views/compare/settle/index'], resolve),
        "matchingComponentName": "compare/settle/index",
        "meta": {
          "title": "结算数据补充",
          "icon": "peoples",
          "noCache": true
        }
      },
      {
        "name": "compareDetail",
        "path": "compareDetail",
        "hidden": false,
        "component": (resolve) => require(['@/views/compare/compareDetail/index'], resolve),
        "matchingComponentName": "compare/compareDetail/index",
        "meta": {
          "title": "数据分析",
          "icon": "peoples",
          "noCache": true
        }
      }]
  },
  {
    "name": "integratedQuery",
    "path": "/integratedQuery",
    "hidden": false,
    "redirect": "noredirect",
    "component": Layout,
    "alwaysShow": true,
    "meta": {
      "title": "综合查询",
      "icon": "system",
      "noCache": true
    },
    "children": [
      {
        "name": "userSummary",
        "path": "userSummary",
        "hidden": false,
        "component": (resolve) => require(['@/views/integratedQuery/userSummary/index'], resolve),
        "matchingComponentName": "integratedQuery/userSummary/index",
        "meta": {
          "title": "用户汇总",
          "icon": "peoples",
          "noCache": true
        }
      },
      {
        "name": "count",
        "path": "count",
        "hidden": false,
        "component": (resolve) => require(['@/views/integratedQuery/count/index'], resolve),
        "matchingComponentName": "integratedQuery/count/index",
        "meta": {
          "title": "汇总统计",
          "icon": "peoples",
          "noCache": true
        }
      },
      {
        "name": "mailList",
        "path": "mailList",
        "hidden": false,
        "component": (resolve) => require(['@/views/user/mailList/index'], resolve),
        "matchingComponentName": "user/mailList/index",
        "meta": {
          "title": "人员查询",
          "icon": "peoples",
          "noCache": true
        }
      },
      {
        "name": "quarterlySummary",
        "path": "quarterlySummary",
        "hidden": false,
        "component": (resolve) => require(['@/views/integratedQuery/quarterlySummary/index'], resolve),
        "matchingComponentName": "integratedQuery/quarterlySummary/index",
        "meta": {
          "title": "系统季报",
          "icon": "peoples",
          "noCache": true
        }
      }
    ]
  }
]
