import router from './routers'
import store from '@/store'
import Config from '@/settings'
import loca from './loca'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css'// progress bar style
import {getToken, setToken} from '@/utils/auth' // getToken from cookie
// TODO by tangjf
// import { buildMenus } from '@/api/system/menu'
import { buildMenus } from '@/api/menu'
import { filterAsyncRouter } from '@/store/modules/permission'
import request from "@/utils/request"
import {setUserInfo} from "@/store/modules/user"

NProgress.configure({ showSpinner: false })// NProgress Configuration

const whiteList = ['/login']// no redirect whitelist

//是否启用本地菜单
const isLocaMenu = false;

router.beforeEach(async (to, from, next) => {
  if (to.meta.title) {
    document.title = to.meta.title + ' - ' + Config.title
  }
  NProgress.start()
	if (to.path == '/dashboard' && to.query.token) {
		await request(`/system/auth/valid/${to.query.token}`).then(res => {
			setToken(res.bearerToken, true)
			store.commit('SET_TOKEN', res.bearerToken)
			setUserInfo(res.currentUser, store.commit)
			// 第一次加载菜单时用到， 具体见 src 目录下的 permission.js
			store.commit('SET_LOAD_MENUS', true)
		})
		next({ path: '/' })
	}
	if (getToken()) {
		// 已登录且要跳转的页面是登录页
		if (to.path === '/login') {
			next({ path: '/' })
			NProgress.done()
		} else {
			if (store.getters.roles.length === 0) {
// 判断当前用户是否已拉取完user_info信息
				store.dispatch('GetInfo').then(res => { // 拉取user_info
					// 动态路由，拉取菜单
					loadMenus(next, to)
				}).catch((err) => {
					store.dispatch('LogOut').then(() => {
						location.reload() // 为了重新实例化vue-router对象 避免bug
					})
				})
				// 登录时未拉取 菜单，在此处拉取
			} else if (store.getters.loadMenus) {
				// 修改成false，防止死循环
				store.dispatch('updateLoadMenus').then(res => {})
				loadMenus(next, to)
			} else {
				//判断是否要修改密码
				if (store.getters.user.ifChangeFlag === 1 && store.getters.user.username !== 'admin' && to.path !== '/dashboard') {
					next({ path: '/dashboard' })
				}else {
					next()
				}
			}

		}
	} else {
		/* has no token*/
		if (whiteList.indexOf(to.path) !== -1) { // 在免登录白名单，直接进入
			next()
		} else {
			next(`/login?redirect=${to.path}`) // 否则全部重定向到登录页
			NProgress.done()
		}
	}
})

export const loadMenus = (next, to) => {
  buildMenus().then(res => {
    routerInit(res, next, to, loca);
  })
  return;
  if(process.env.NODE_ENV === 'production'){
    buildMenus().then(res => {
      routerInit(res, next, to);
    })
  } else {
    routerInit(loca, next, to);
  }
}

function routerInit(res, next, to, loca){
  let asyncRouter;
  if(isLocaMenu){
    asyncRouter = loca;
  } else {
    asyncRouter = filterAsyncRouter(res, loca);
  }
  asyncRouter.push({ path: '*', redirect: '/404', hidden: true })
  store.dispatch('GenerateRoutes', asyncRouter)
	asyncRouter.forEach(i => {
		router.addRoute(i) // 动态添加可访问路由表
	})
	// 缓存中没有路由信息时，需要异步注册路由。
	// 直接next()导致页面空白的原因是当前时间切片内存中，路由还未注册，导致路由解析失败
	// 上面路由注册完成后通过next({ ...to, replace: true })，强制重新解析路由，解决空白问题
	next({ ...to, replace: true })
}
router.afterEach(() => {
  NProgress.done() // finish progress bar
})
