import Vue from 'vue'
import Router from 'vue-router'
import Layout from '../layout/index'

Vue.use(Router)

export var constantRouterMap = [
  { path: '/login',
    meta: { title: '登录', noCache: true },
    component: (resolve) => require(['@/views/login'], resolve),
    hidden: true
  },
  {
    path: '/404',
    component: (resolve) => require(['@/views/features/404'], resolve),
    hidden: true
  },
  {
    path: '/401',
    component: (resolve) => require(['@/views/features/401'], resolve),
    hidden: true
  },
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path*',
        component: (resolve) => require(['@/views/features/redirect'], resolve)
      }
    ]
  },
  {
    "path": "/learningField",
    "hidden": true,
     component: Layout,
     children: [
      {
        path: '/learningField/learningFieldDetails',
        name: 'learningFieldDetails',
        "component": (resolve) => require(['@/views/learningField/details'], resolve),
        meta: { title: '学习园地详情', icon: 'index', affix: false, noCache: true },
      }
    ]
  },
  {
    "path": "/announceDetails",
    "hidden": true,
    component: Layout,
    children: [
      {
        path: '/announceDetails/announceDetails',
        name: 'announceDetails',
        "component": (resolve) => require(['@/views/notice/announce/details'], resolve),
        meta: { title: '政策通知详情', icon: 'index', affix: false, noCache: true },
      }
    ]
  },
  {
    "path": "/organizeDetails",
    "hidden": true,
    component: Layout,
    children: [
      {
        path: '/organizeDetails',
        name: 'organizeDetails',
        "component": (resolve) => require(['@/views/organizeCommunication/details'], resolve),
        meta: { title: '企业通知详情', icon: 'index', affix: false, noCache: true },
      }
    ]
  },
  {
    "path": "/pricingBasisDetails",
    "hidden": true,
    component: Layout,
    children: [
      {
        path: '/pricingBasisDetails',
        name: 'pricingBasisDetails',
        "component": (resolve) => require(['@/views/organizeCommunication/details'], resolve),
        meta: { title: '计价依据详情', icon: 'index', affix: false, noCache: true },
      }
    ]
  },
  {
    "path": "/standardSpecificationDetails",
    "hidden": true,
    component: Layout,
    children: [
      {
        path: '/standardSpecificationDetails',
        name: 'standardSpecificationDetails',
        "component": (resolve) => require(['@/views/organizeCommunication/details'], resolve),
        meta: { title: '标准规范详情', icon: 'index', affix: false, noCache: true },
      }
    ]
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: (resolve) => require(['@/views/home'], resolve),
        name: 'Dashboard',
        meta: { title: '首页', icon: 'index', affix: true, noCache: true },
      }
     ]
  },
  { path: '/tools',
    component: Layout,
    redirect: '/tools/bpmn',
    children: [
      {
        path: 'bpmn',
        component: (resolve) => require(['@/views/tools/bpmn'], resolve),
        name: 'bpmn',
        meta: { title: '流程定义', icon: 'index',  noCache: true },
        hidden: true
      },
      {
        path: 'kFormDesign',
        component: (resolve) => require(['@/views/tools/kFormDesign'], resolve),
        name: 'kFormDesign',
        meta: { title: '表单设计', icon: 'index',  noCache: true },
        hidden: true
      },
      {
        path: 'kFormDesign/preview',
        component: (resolve) => require(['@/views/tools/kFormDesign/preview'], resolve),
        name: 'preview',
        meta: { title: '表单预览', icon: 'index',  noCache: true },
        hidden: true
      },

    ],
    hidden: true
  },

  {
    path: '/tools/projectDetails',
    component: (resolve) => require(['@/views/tools/projectDetails/index'], resolve),
    name: 'projectDetails',
    meta: { title: '项目详情', icon: 'index',  noCache: true },
    hidden: true
  },
  {
    path: '/users',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'center',
        component: (resolve) => require(['@/views/user/user/center'], resolve),
        name: '个人中心',
        meta: { title: '个人中心' }
      }
    ]
  }
	// ,
	// {
	// 	path: '/statistics',
	// 	component: Layout,
	// 	meta: {
	// 		title: '问卷统计',
	// 		icon: 'index'
	// 	},
	// 	children: [
	// 		{
	// 			path: '',
	// 			meta: {title: '答题统计'},
	// 			component: () => import('@/views/statistics/paper/index')
	// 		},
	// 		{
	// 			path: 'survey',
	// 			meta: {title: '问卷统计'},
	// 			component: () => import('@/views/statistics/survey/index')
	// 		}
	// 	]
	// }
]

export default new Router({
  mode: 'history',
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRouterMap
})
