import JSEncrypt from 'jsencrypt/bin/jsencrypt'

// 密钥对生成 http://web.chacuo.net/netrsakeypair

// TODO by tangjf
// const publicKey = 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBANL378k3RiZHWx5AfJqdH9xRNBmD9wGD\n' +
//   '2iRe41HdTNF8RUhNnHit5NpMNtGL0NPTSSpPjjI1kJfVorRvaQerUgkCAwEAAQ=='

// const privateKey = 'MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEA0vfvyTdGJkdbHkB8\n' +
//   'mp0f3FE0GYP3AYPaJF7jUd1M0XxFSE2ceK3k2kw20YvQ09NJKk+OMjWQl9WitG9p\n' +
//   'B6tSCQIDAQABAkA2SimBrWC2/wvauBuYqjCFwLvYiRYqZKThUS3MZlebXJiLB+Ue\n' +
//   '/gUifAAKIg1avttUZsHBHrop4qfJCwAI0+YRAiEA+W3NK/RaXtnRqmoUUkb59zsZ\n' +
//   'UBLpvZgQPfj1MhyHDz0CIQDYhsAhPJ3mgS64NbUZmGWuuNKp5coY2GIj/zYDMJp6\n' +
//   'vQIgUueLFXv/eZ1ekgz2Oi67MNCk5jeTF2BurZqNLR3MSmUCIFT3Q6uHMtsB9Eha\n' +
//   '4u7hS31tj1UWE+D+ADzp59MGnoftAiBeHT7gDMuqeJHPL4b+kC+gzV4FGTfhR9q3\n' +
//   'tTbklZkD2A=='

const publicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDVZLcVo+FsMPJHifk017EFvRH06G4KrmMOO68b0fiBzjgZR/a/SvGW4I1ha7m3BV75Z7Mm0lHmBUAuKTk0lRAi+nuf2aLQCuHxW9dwI7vyfLIb3ezNWsliUctMHje+WkjUnZs1AnRscmaGtdmTxVp4wf8LFTUrRzg36Vd765vmlQIDAQAB'
const privateKey = 'MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBANVktxWj4Www8keJ+TTXsQW9EfTobgquYw47rxvR+IHOOBlH9r9K8ZbgjWFrubcFXvlnsybSUeYFQC4pOTSVECL6e5/ZotAK4fFb13Aju/J8shvd7M1ayWJRy0weN75aSNSdmzUCdGxyZoa12ZPFWnjB/wsVNStHODfpV3vrm+aVAgMBAAECgYEAkYvsRFpOuph358t3qk8jcqKOyNhvleFztpWGzhbcxMx+/t9SsQ7Q0PO/+p6bQPXs3XeZffUd3yHdsJ51YiP5qGIEjZkQwS0ITVwSFlqyDlSoXvO37xs2w9XCq9FSm/jK63ncyAJakkhKBdr7S7mYR70qgkm+217Ztk1OQ7EIZ9ECQQD9gu2sbOKys9sXeRvj2m612BMRobPfI49lWkuCu5LcIRjjX6lJBRncb/rsU94h+H9cqdpISQHqsrDQnMres567AkEA13z4eQvmh8xBQ4YnF8Qj9XReooPZnp4zbuPDiK+0wzriEkphMEqHPExe/uo3Le6T26oADNpa/m5cYNdd91PC7wJBALKNl/b8zgLNmL+EpncqbxWW9X9SpAzC0AmzukF++aYfjZb/1oy+kgNnv5TaUTcd/h52525INZhneQ/Fj0Ghw2cCQEOHnG6Ctjn0XreEgRfHleVEFKwlWbuFpGSp6Asb1TAhcdt9h7uUf5D79gE48RXp4FhEjEN64m09tKF8nKv3ie0CQQCFUaQFcZ+1lL7OzDFUAANox347fw2RabuaL02krwlE/hLuRVLtFoEUFRJ4bEntD/LzhrVf3a2FwrT//n2DBERH'

// 加密
export function encrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey) // 设置公钥
  return encryptor.encrypt(txt) // 对需要加密的数据进行加密
}

// 解密
export function decrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPrivateKey(privateKey)
  return encryptor.decrypt(txt)
}

