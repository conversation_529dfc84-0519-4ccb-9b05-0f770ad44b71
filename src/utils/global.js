import {flatten} from '@/utils/index'
import {timeConsuming} from '@/utils/datetime'
import {isArray} from "./validate";

export default {
  install(Vue, options) {
    Vue.mixin({
      filters: {
        timeConsuming(time) {
          return timeConsuming(time)
        }
      },
      data() {
        return {
          //form input style
          formInputStyle: {
            width: '195px'
          },
          formStyle: {
            width: '680px'
          }
        }
      },
      methods: {
        //补0
        zeroFilling(n) {
          if (n < 10) {
            return '0' + n;
          }
          return n;
        },
        //显示文件大小单位
        formatBytes(bytes) {
          if (bytes === 0) return '0 B';
          var k = 1000, // or 1024
            sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],
            i = Math.floor(Math.log(bytes) / Math.log(k));

          return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i];
        },
        showCurrentData(dicts, val, key, name) {

          dicts = JSON.parse(JSON.stringify(dicts));

          dicts = flatten(dicts, 'children');
          
          let find = dicts.filter(item => item[key ? key : 'code'] == val);
          return find.length > 0 ? find[0][name ? name : 'name'] : '';
        },
        showArrayCurrentData(dicts, val, key, name) {
          if (val ) {
            dicts = JSON.parse(JSON.stringify(dicts));
            dicts = flatten(dicts, 'children');
            let names = ''
            val.split(';').forEach(k => {
              let find = dicts.filter(item => item[key ? key : 'code'] == k);
              names = names + (find.length > 0 ? find[0][name ? name : 'name'] : '')
            })
            return names;
          }
          return ''
        },
        openUrl(url) {
          this.$router.push(url);
        },
        //递归data处理
        buildData(datas, key, callback, leafIndex) {
          datas.forEach((data, index) => {
            //判断叶子节点
            if (leafIndex) {
              data.leafIndex = leafIndex;
            } else {
              data.leafIndex = index;
            }
            callback && callback(data)
            if (data[key]) {
              //leafIndex 叶子节点
              this.buildData(data[key], key, callback, index);
            }

            if (data[key] && data[key].length <= 0) {
              delete data[key];
            }
          })
        },
        parseDecimal(num, decimal = 4) {
          if (!num) return ''
          const number = parseFloat(num)
          if (isNaN(number)) return 0
          const decimalPlaces = countDecimalPlaces(number)
          // return Number.isInteger(number) || decimalPlaces < 4 ? number : parseFloat(number.toFixed(decimal))
          return number.toFixed(decimal)
        },
        parseDecimal4(num) {
          if (!num) return ''
          let obj = parseFloat(num)
          return obj.toFixed(4)
        },
      }

    })
  }
}
