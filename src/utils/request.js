import axios from 'axios'
import router from '@/router/routers'
import { Notification, MessageBox } from 'element-ui'
import store from '../store'
import { getToken } from '@/utils/auth'
import Config from '@/settings'
import { picAdaptation } from './index'
import Vue from 'vue'


//定义是否走图片匹配器接口

//const


// 创建axios实例
const service = axios.create({
  // TODO by tangjf
  // baseURL: process.env.NODE_ENV === 'production' ? process.env.VUE_APP_BASE_API : '/', // api 的 base_url
  //baseURL: process.env.VUE_APP_BASE_API, // api 的 base_url
  baseURL: process.env.VUE_APP_BASE_API,
  timeout: Config.timeout // 请求超时时间
})

// request拦截器
service.interceptors.request.use(
  config => {
    if (getToken()) {
      // TODO by tangjf
      config.headers['xToken'] = getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    config.headers['Content-Type'] = 'application/json'
    return config
  },
  error => {
    // Do something with request error
    console.log(error) // for debug
    Promise.reject(error)
  }
)

// response 拦截器
service.interceptors.response.use(
  response => {
    const code = response.status
    if (code < 200 || code > 300) {
      Notification.error({
        title: response.message
      })
      return Promise.reject('error')
    } else {
      // TODO by tangjf
      // return response.data
      const apiRes = response.data;
      const contentType = response.headers['content-type'];
      if(contentType.indexOf('application/json') > -1) {
        // 数据接口返回值处理
        if(apiRes.success && apiRes.status == 200) {
          return apiRes.data
          return picAdaptation(apiRes.data);
        } else if(apiRes.status == 401){
          store.dispatch('LogOut').then(() => {
            location.reload() // 为了重新实例化vue-router对象 避免bug
          })
        } else if(apiRes.status == 206){
          Notification.warning({
            title: apiRes.message
          })
          return Promise.reject('warning')
        } else {
          Notification.error({
            title: apiRes.message
          })
          return Promise.reject('error')
        }
      } else if(contentType.indexOf('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') > -1) {
        // 导出Excel文件处理
        return apiRes;
      } else if(contentType.indexOf('application/octet-stream') > -1) {
        // 下载文件处理
        return apiRes;
      } else if(contentType.indexOf('image') > -1) {
        return apiRes;
      }
     else {
        Notification.error({
          title: '未知的返回头类型' + contentType
        })
        return Promise.reject('error')
      }

    }
  },
  error => {
    let code = 0
    try {
      code = error.response.data.status
    } catch (e) {
      if (error.toString().indexOf('Error: timeout') !== -1) {
        Notification.error({
          title: '网络请求超时',
          duration: 5000
        })
        return Promise.reject(error)
      }
    }
    if (code) {
      if (code === 403) {
        router.push({ path: '/401' })
      } else if (code === 400) {
        // 校验错误
        const errorMsg = error.response.data.message
        const validMsg = error.response.data.data
        if (validMsg !== undefined) {
          Vue.prototype.$message({
            message: validMsg[0],
            type: 'error'
          });
          // Notification.error({
          //   title: errorMsg,
          //   message: validMsg[0],
          //   duration: 5000
          // })
        }
      } else {
        const errorMsg = error.response.data.message
        if (errorMsg !== undefined) {
          Notification.error({
            title: errorMsg,
            duration: 5000
          })
        }
      }
    } else {
      Notification.error({
        title: '接口请求失败',
        duration: 5000
      })
    }
    return Promise.reject(error)
  }
)
export default service
