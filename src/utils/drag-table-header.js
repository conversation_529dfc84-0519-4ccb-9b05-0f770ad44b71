import Sortable from "sortablejs";
import request from "@/utils/request";
import store from '@/store'

export function columnDrop(tableRef, that) {


    // 第一步，获取列容器
    const wrapperColumn = document.querySelector(
        ".el-table__header-wrapper tr"
    );
    // 第二步，给列容器指定对应拖拽规则
    Sortable.create(wrapperColumn, {
        group:that.$route.name + '_drag',
        animation: 180,
        delay: 15,
        easing: "cubic-bezier(0.05, 9, 0.01, 0.01)",
        dragClass: "dragClass", //设置拖拽样式类名
        ghostClass: "ghostClass", //设置拖拽停靠样式类名
        chosenClass: "chosenClass", //设置选中样式类名
        dataIdAttr: 'row-key',
        store: {
            /**
             * Get the order of elements. Called once during initialization.
             * @param   {Sortable}  sortable
             * @returns {Array}
             */
            get: function (sortable) {
                let savedColumnOrder = [];
                // 获取已保存的列顺序
                request({
                    url: '/system/userTableColumn/' + store.getters.user.id + '/' + that.$route.name + '_drag',
                    method: 'get'
                }).then(res => {
                    if (res) {
                        savedColumnOrder = JSON.parse(res.context);
                        if (savedColumnOrder.length > 0) {
                            for (let i = 0; i < tableRef.columns.length; i++) {
                                let column = tableRef.columns[i];
                                let reverseColumn = savedColumnOrder[i];
                                if (column.property && reverseColumn.property) {
                                    if (column.property !== reverseColumn.property) {
                                        const vm2 = tableRef.$children.find(e => e.prop === reverseColumn.property)
                                        const columnConfig2 = vm2.columnConfig
                                        vm2.owner.store.commit('removeColumn', columnConfig2, null)
                                        vm2.owner.store.commit('insertColumn', columnConfig2, i, null)
                                    }
                                }
                            }
                        }
                    }
                })


            },

            /**
             * Save the order of elements. Called onEnd (when the item is dropped).
             * @param {Sortable}  sortable
             */
            set: function (sortable) {

            }
        },
        onEnd({newIndex, oldIndex}) {
            const targetRow = tableRef.columns.splice(oldIndex, 1)[0];
            tableRef.columns.splice(newIndex, 0, targetRow)
            if (newIndex !== oldIndex) {
                request({
                    url: '/system/userTableColumn',
                    method: 'put',
                    data: {
                        userId: store.getters.user.id,
                        routerName: that.$route.name + '_drag',
                        context: JSON.stringify(tableRef.columns.map(item=>({property:item.property})))
                    }
                })
            }
        }
    });
}
