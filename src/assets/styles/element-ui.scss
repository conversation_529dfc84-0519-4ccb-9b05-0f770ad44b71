// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 900 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

.el-dialog__headerbtn {
  top: 15px !important;
  background: url('../images/close.png') left no-repeat !important;
  background-size: 16px 16px !important;
  right: 15px;
  display: inline-block;
  horiz-align: center;
  float: right;
  width: 16px;
}


.el-dialog__headerbtn i {
  font-size: 25px;
  visibility: hidden;
}

.el-dialog__body {
  padding: 0 20px 20px 20px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}

.el-dialog__header {
  background: #468EFF;
  padding: 15px;
  margin-bottom: 10px;
  color: white;
  box-shadow: 2px 2px 2px #888888;
}

.el-dialog__title {
  line-height: 25px;
  font-size: 18px;
  color: white;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

//.el-table--border th , .el-table--border td{
//  border-right: 0px;
//}
//
//.el-table--border{
//  border:0px
//}

.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content{
  background-color:#66b1ff87;
}
