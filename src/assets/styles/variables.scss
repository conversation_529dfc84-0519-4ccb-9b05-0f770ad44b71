// base color
$blue:#324157;
$light-blue:#3A71A8;
$red:#C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow:#FEC171;
$panGreen: #30B08F;

// sidebar
$menuText:#bfcbd9;
$menuActiveText:#409EFF;
$subMenuActiveText:#f4f4f5; // https://github.com/ElemeFE/element/issues/12951


$menuBg:#304156;
$menuHover:#263445;

$subMenuBg:#1f2d3d;
$subMenuHover:#001528;

$sideBarWidth: 205px;



$navbarBg: #fff;
$navbarTextHomeColor: #303133;
$navbarTextOtherColor: #97a8be;
$navbarTagViewBg:#fff;
$navbarTagItemBg:#fff;
$navbarTagItemActiveBg:#42b983;
$mainContainerBg: #fff;
$appContainerBg: #fff;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;                                                //左侧菜单文字默认颜色
  menuActiveText: $menuActiveText;                                    //左侧子级菜单文字活跃颜色
  subMenuActiveText: $subMenuActiveText;                              //左侧父级菜单文字活跃颜色
  menuBg: $menuBg;                                                    //左侧菜单背景色
  menuHover: $menuHover;                                              //左侧父级菜单hover颜色
  subMenuBg: $subMenuBg;                                              //左侧子级菜单背景默认颜色
  subMenuHover: $subMenuHover;                                        //左侧子级菜单hover颜色
  sideBarWidth: $sideBarWidth;                                        //左侧菜单宽度
  navbarBg: $navbarBg;                                                //固定导航背景颜色
  navbarTextHomeColor: $navbarTextHomeColor;                          //固定导航"首页"文字颜色
  navbarTextOtherColor: $navbarTextOtherColor;                        //固定导航"除首页外的"文字颜色
  navbarTagViewBg: $navbarTagViewBg;                                  //历史记录容器背景色
  navbarTagItemBg: $navbarTagItemBg;                                  //历史记录标签默认背景色
  navbarTagItemActiveBg: $navbarTagItemActiveBg;                      //历史记录标签活跃背景色
  mainContainerBg: $mainContainerBg;                                  //内容框架背景色
  appContainerBg: $appContainerBg;                                    //应用背景色
}
