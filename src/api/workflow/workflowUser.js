import request from '@/utils/request'
//获取当前组织的用户列表
export function getOrgUserList(data) {
    return request({
        url: '/workflow/workflowUserTree/getOrgUserList',
        method: 'get',
        data
    })
}

//获取当前组织的部门
export function getOrgDeptList(data) {
    return request({
        url: '/workflow/workflowUserTree/getOrgDeptList',
        method: 'get',
        data
    })
}

//获取织组织的岗位列表
export function getOrgPositionList(data) {
    return request({
        url: '/workflow/workflowUserTree/getOrgPositionList',
        method: 'get',
        data
    })
}

//获取当前组织的角色列表
export function getOrgRoleList(data) {
    return request({
        url: '/workflow/workflowUserTree/getOrgRoleList',
        method: 'get',
        data
    })
}

//获取相对人列表
export function getRelativeUserList(data) {
    return request({
        url: '/workflow/workflowUserTree/getRelativeUserList',
        method: 'get',
        data
    })
}


//获取用户类型列表
export function getUserTypeList(data) {
    return request({
        url: '/workflow/workflowUserTree/getUserTypeList',
        method: 'get',
        data
    })
}