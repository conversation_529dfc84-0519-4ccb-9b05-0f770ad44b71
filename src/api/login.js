import request from '@/utils/request'

export function login(username, password, captcha<PERSON><PERSON>, captcha<PERSON><PERSON>) {
  return request({
    url: 'general/login',
    method: 'post',
    data: {
      username,
      password,
      captcha<PERSON><PERSON>,
      captcha<PERSON><PERSON>
    }
  })
}

export function getInfo() {
  return request({
    url: 'general/currentUser',
    method: 'get'
  })
}

export function getCodeImg() {
  return request({
    url: 'general/captcha',
    method: 'get'
  })
}

export function logout() {
  return request({
    url: 'general/logout',
    method: 'delete'
  })
}

export function updatePassword(data) {
  return request({
    async: false,
    url: 'general/updatePassword',
    method: 'post',
    data
  })
}
