import Vue from 'vue'
import Cookies from 'js-cookie'
import 'normalize.css/normalize.css'
import Element from 'element-ui'
// import mavonEditor from 'mavon-editor'
// import 'mavon-editor/dist/css/index.css'
import global from '@/utils/global'
//Vue.prototype.$confirm = Modal.confirm
 Vue.prototype.$message = Element.message;

//引入(resolve) => require(['./components/k-form-design/packages'], resolve)

// import KFormDesign from './components/k-form-design/packages'
// import './components/k-form-design/styles/form-design.less'
// Vue.use(KFormDesign)

// Vue.component('k-form-design', () => import('./components/k-form-design/packages'))
// 数据字典
import dict from './components/Dict'

import VueCron from 'vue-cron'
Vue.use(VueCron);

// 权限指令
import permission from './components/Permission'
import './assets/styles/element-variables.scss'
// global css
import './assets/styles/index.scss'


//注入弹窗拖拽
import  './utils/dialogDrag'

// 代码高亮
//import VueHighlightJS from 'vue-highlightjs'
//import 'highlight.js/styles/atom-one-dark.css'
import App from './App'
import store from './store'
import router from './router/routers'

import './assets/icons' // icon
import './router/index' // permission control

//Vue.use(VueHighlightJS)
//Vue.use(mavonEditor)
Vue.use(permission)
Vue.use(dict)
Vue.use(global)

Vue.use(Element, {
  size: Cookies.get('size') || 'mini' // set element-ui default size
})

//Vue.use(KFormDesign)

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
//注释此条，影响select相关功能
// document.onselectstart = function () { return false; }
