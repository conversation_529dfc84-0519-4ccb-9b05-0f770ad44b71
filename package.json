{"name": "eladmin-web", "version": "2.4.0", "description": "EL-ADMIN 前端源码", "author": "<PERSON>", "license": "Apache-2.0", "scripts": {"dev": "set NODE_OPTIONS=--openssl-legacy-provider & vue-cli-service serve", "build:prod": "set NODE_OPTIONS=--openssl-legacy-provider & vue-cli-service build", "build:stage": "set NODE_OPTIONS=--openssl-legacy-provider & vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "vue-cli-service lint", "test:unit": "jest --clearCache && vue-cli-service test:unit", "svgo": "svgo -f src/assets/icons/svg --config=src/assets/icons/svgo.yml", "new": "plop"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "repository": {"type": "git", "url": "https://github.com/elunez/eladmin-web.git"}, "bugs": {"url": "https://github.com/elunez/eladmin/issues"}, "dependencies": {"@npkg/tinymce-plugins": "0.0.7", "@riophae/vue-treeselect": "0.1.0", "@tinymce/tinymce-vue": "^3.2.8", "ant-design-vue": "^1.6.2", "axios": "0.18.1", "bpmn": "^0.2.2", "bpmn-js": "^7.2.0", "bpmn-js-properties-panel": "^0.34.0", "camunda-bpmn-moddle": "^4.4.0", "clipboard": "2.0.4", "codemirror": "^5.49.2", "connect": "3.6.6", "core-js": "^3.6.4", "crypto-js": "^4.2.0", "default-passive-events": "^2.0.0", "docx-preview": "^0.1.4", "echarts": "4.2.1", "echarts-gl": "^1.1.1", "echarts-wordcloud": "^1.1.3", "element-china-area-data": "^5.0.0", "element-ui": "^2.15.3", "file-saver": "1.3.8", "fuse.js": "3.4.4", "js-beautify": "^1.10.2", "js-cookie": "2.2.0", "jsencrypt": "^3.0.0-rc.1", "jszip": "^3.1.5", "k-form-design": "^3.5.5", "mavon-editor": "^2.7.0", "mockjs": "^1.1.0", "moment": "^2.27.0", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "2.4.0", "qrcode.vue": "^1.7.0", "qs": "^6.9.1", "screenfull": "4.2.0", "sortablejs": "1.8.4", "tinymce": "^5.1.0", "umy-ui": "^1.1.6", "vue": "2.6.12", "vue-codemirror-lite": "^1.0.4", "vue-count-to": "1.0.13", "vue-cron": "^1.0.9", "vue-cropper": "0.4.9", "vue-highlightjs": "^1.3.3", "vue-quill-editor": "^3.0.6", "vue-router": "^3.0.2", "vue-schart": "^2.0.0", "vue-select-image": "^1.9.0", "vue-splitpane": "1.0.4", "vuedraggable": "^2.20.0", "vuex": "3.1.0", "wangeditor": "^3.1.1", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-babel": "5.0.1", "@vue/cli-plugin-eslint": "5.0.1", "@vue/cli-plugin-unit-jest": "5.0.1", "@vue/cli-service": "5.0.1", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "10.0.1", "babel-plugin-component": "^1.1.1", "babel-plugin-dynamic-import-node": "2.3.0", "babel-plugin-import": "^1.13.0", "babel-plugin-transform-remove-console": "^6.9.4", "css-loader": "^3.6.0", "eslint": "^7.12.1", "eslint-plugin-vue": "7.1.0", "html-webpack-plugin": "4.5.2", "http-proxy-middleware": "^0.19.1", "husky": "1.3.1", "less": "^3.11.3", "less-loader": "^4.1.0", "lint-staged": "8.1.5", "sass": "~1.32.1", "sass-loader": "^10.0.4", "script-ext-html-webpack-plugin": "^2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "^5.0.0", "svgo": "1.2.0", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}